# StarMet - 心理咨询顾问平台

一个基于 Vue3 + TypeScript + Vite 构建的现代化心理咨询顾问平台，支持响应式设计、多语言和暗黑模式。

## 特性

- 🧠 专业心理咨询服务展示
- 📱 响应式设计，适配多种设备尺寸
- 🌍 多语言支持 (中文/英文)
- 🔄 平滑的加载过渡效果
- 🎨 使用 Tailwind CSS 构建的现代化 UI
- 💪 TypeScript 支持，提供完整的类型定义

## 技术栈

- Vue 3.5.13
- TypeScript 5.7.2
- Vite 6.2.0
- Tailwind CSS 3.4.1
- Element Plus 2.9.8
- Vue Router 4.5.0
- Pinia 3.0.2
- VueUse 13.1.0
- Vue I18n 11.1.3

## 快速开始

### 环境要求

- Node.js >= 16.0.0
- pnpm >= 8.0.0

### 安装依赖

```bash
pnpm install
```

### 开发服务器

```bash
pnpm dev
```

### 构建生产版本

```bash
pnpm build
```

### 预览生产构建

```bash
pnpm preview
```

## 项目结构

```
src/
├── components/        # 通用组件
├── views/             # 页面组件
│   └── home/          # 首页
│       └── components/# 首页相关组件
├── stores/            # Pinia 状态管理
├── styles/            # 全局样式
├── routers/           # 路由配置
├── languages/         # 国际化配置
├── layout/            # 布局组件
│   └── components/    # 布局相关组件
└── types/             # TypeScript 类型定义
```

## 主要功能

### 顾问展示

- 顾问卡片展示
- 分类筛选 (全部、新人、评价、阅读、评分)
- 在线/离线状态显示
- 评分和阅读次数展示

### 主题切换

- 支持亮色/暗色主题
- 主题状态持久化
- 跟随系统主题自动切换

### 导航菜单

- 响应式导航栏
- 下拉菜单功能
- 滚动时动态颜色变化

## 自定义配置

### 调整响应式断点

可以在 `tailwind.config.js` 中修改断点配置：

```js
screens: {
  'sm': '640px',
  'md': '1120px',
  'lg': '1330px',
  'xl': '1440px',
  '2xl': '1536px',
}
```

### 修改字体大小

在 `tailwind.config.js` 中可以调整自定义字体大小：

```js
fontSize: {
  'r-0.8': ['0.8rem', { lineHeight: '1.5' }],
  'r-1': ['1rem', { lineHeight: '1.5' }],
  'r-1.2': ['1.2rem', { lineHeight: '1.5' }],
  // ...更多尺寸
}
```

## 许可证

[MIT License](LICENSE)
