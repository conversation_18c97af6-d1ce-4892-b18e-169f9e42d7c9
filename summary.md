# 项目概览

## 技术栈
- Vue 3
- TypeScript
- Tailwind CSS
- Element Plus (el-carousel 等组件)

## 主要功能和组件

### 布局结构
- 使用了布局组件 (layouts)，包含 header 组件
- 响应式设计 (移动端和桌面端不同布局)

### 首页 (Home)
- 轮播图展示 (使用 el-carousel)
- 主要内容区域分为几个部分:
  - 1-Hour Delivery
  - Top 9 Psychics
  - Online Advisors
  - Offline Advisors
- 使用 MainItem 组件来复用内容结构

### 导航
- 响应式导航菜单
- 下拉菜单功能 (All Advisor)
- 根据滚动位置动态改变导航颜色

### 样式特点
- 使用 Tailwind CSS 进行样式管理
- 自定义字体 ('Philosopher', 'ttChocolates')
- 暗黑模式支持
- 文本溢出省略 (两行省略)

### 最近添加的功能
1. 导航栏滚动时颜色变化 (白色变黑色)
2. 下拉菜单功能 (带小三角指示器)
3. 文本两行省略显示