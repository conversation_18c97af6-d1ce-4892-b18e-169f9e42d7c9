/** @type {import('tailwindcss').Config} */
export default {
	content: ['./index.html', './src/**/*.{vue,js,ts,jsx,tsx}'],
	darkMode: 'class',
	theme: {
		extend: {
			colors: {
				primary: {
					50: '#f0f9ff',
					100: '#e0f2fe',
					200: '#bae6fd',
					300: '#7dd3fc',
					400: '#38bdf8',
					500: '#0ea5e9',
					600: '#0284c7',
					700: '#0369a1',
					800: '#075985',
					900: '#0c4a6e',
					950: '#082f49',
                    'custom-border': 'rgba(28, 33, 88, 0.2)',
				},
			},
            lineHeihgt:{
                'l-60':['60px']
            },
			fontSize: {
				// 添加一系列rem尺寸
				'r-0.8': ['0.8rem', { lineHeight: '1.5' }],
				'r-0.9': ['0.9rem', { lineHeight: '1.5' }],
				'r-1': ['1rem', { lineHeight: '1.5' }],
				'r-1.05': ['1.05rem', { lineHeight: '1.5' }],
				'r-1.1': ['1.1rem', { lineHeight: '1.5' }],
				'r-1.15': ['1.15rem', { lineHeight: '1.5' }],
				'r-1.2': ['1.2rem', { lineHeight: '1.5' }],
				'r-1.25': ['1.25rem', { lineHeight: '1.5' }],
				'r-1.3': ['1.3rem', { lineHeight: '1.5' }],
				'r-1.35': ['1.35rem', { lineHeight: '1.5' }],
				'r-1.4': ['1.4rem', { lineHeight: '1.5' }],
                'r-2': ['2rem', { lineHeight: '1.5' }],
				'r-2.5': ['2.5rem', { lineHeight: '1.5' }],
				'r-3': ['3rem', { lineHeight: '1.5' }],
				'r-3.5': ['3.5rem', { lineHeight: '1.5' }],
				'r-4': ['4rem', { lineHeight: '1.5' }],
				// 可以继续添加更多尺寸...
			},
			keyframes: {
				fadeInUp: {
					from: {
						opacity: '0',
						transform: 'translateY(20px)',
					},
					to: {
						opacity: '1',
						transform: 'translateY(0)',
					},
				},
			},
			animation: {
				'fade-in-up-200': 'fadeInUp 0.6s ease 200ms forwards',
				'fade-in-up-600': 'fadeInUp 0.6s ease 600ms forwards',
			},
		},
		screens: {
			sm: '640px',
            xs: '800px',
			md: '1120px', // 添加自定义断点
			lg: '1330px',
			xl: '1440px',
			'2xl': '1536px'
		},
	},
	plugins: [],
}
