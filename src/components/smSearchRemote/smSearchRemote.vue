<!--  -->
<template>
    <el-select v-model="value" filterable remote reserve-keyword :placeholder="placeholder"
        @change="handleBirthPlaceChange" :remote-method="remoteMethod" :loading="loading" class="w-full"
        popper-class="sm-search-remote-popper">
        <el-option v-for="item in options" :key="item.id" :label="item.stateName + ' ' + item.name"
            :value="item.name + ',' + item.stateName + ',' + item.countryName" />
    </el-select>
</template>

<script setup lang="ts">
import { ref, computed, PropType } from 'vue'

interface SelectOption {
    value: string | number;
    label: string;
    [key: string]: any;
}

const props = defineProps({
    modelValue: {
        type: String,
        default: '',
    },
    // The remote search method should return a promise that resolves to an array of options
    remoteApi: {
        type: Function as PropType<(query: string) => Promise<any>>,
        required: true,
    },
    placeholder: {
        type: String,
        default: 'Please enter a keyword',
    },
});

const emit = defineEmits(['update:modelValue','changePlace']);

const loading = ref(false);
const options = ref<SelectOption[]>([]);

const remoteMethod = async (query: string) => {
    if (query) {
        loading.value = true;
        try {
            const res = await props.remoteApi(query);
            options.value = res.data
        } catch (error) {
            console.error('Remote search failed:', error);
            options.value = [];
        } finally {
            loading.value = false;
        }
    } else {
        options.value = [];
    }
};

const value = computed({
    get: () => props.modelValue,
    set: (val) => {
        emit('update:modelValue', val)
    }
})

const handleBirthPlaceChange = (val: string) => {
    console.log('val', val)
    const [name, stateName, countryName] = val.split(',')
    const { latitude, longitude } = options.value.filter(item => item.name === name)[0]
    emit('changePlace', { latitude, longitude })
}

</script>

<style lang="scss" scoped>
.el-select {
    width: 100% !important;
}
</style>