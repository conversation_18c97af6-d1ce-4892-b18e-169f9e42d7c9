<!-- rate -->
<template>
	<div class="sm-rate flex items-center gap-[6px]">
		<div v-for="(item, index) in 5" :key="index" class="sm-rate-item relative" @click="!isDisabled && handleClick($event, index)">
			<div class="star-wrapper relative w-[14px] h-[14px] lg:w-[18px] lg:h-[18px]">
				<img v-if="getStarType(index) === 'full'" src="@/assets/common/one-star.png" alt="" class="w-full h-full" />
				<img v-else-if="getStarType(index) === 'half'" src="@/assets/common/half-star.png" alt="" class="w-full h-full" />
				<img v-else src="@/assets/common/zero-star.png" alt="" class="w-full h-full" />
				<!-- 增加点击区域 -->
				<div class="absolute inset-0 flex">
					<div class="w-1/2 h-full" @click.stop="!isDisabled && handleHalfClick(index)"></div>
					<div class="w-1/2 h-full" @click.stop="!isDisabled && handleFullClick(index)"></div>
				</div>
			</div>
		</div>
		<div
			:class="['rate-num font-[Open_Sans_3] font-semibold', showSmall ? 'text-xs' : 'text-[20px]', 'md:text-[20px] lg:text-[20px]',showColor ? 'text-[#FF7C44]' : 'text-[#1C2158]']"
		>
			{{ props.rateValue.toFixed(2) }}
		</div>
	</div>
</template>

<script lang="ts" setup name="SmRate">
import { computed } from 'vue'

const props = defineProps<{
	rateValue: number
	isDisabled?: boolean,
    showSmall?: boolean,
    showColor?:Boolean
}>()

const emit = defineEmits(['update:rateValue'])

// 计算每个星星的类型（全星、半星、空星）
const getStarType = (index: number) => {
	const value = props.rateValue
	const decimal = value - Math.floor(value)
	
	if (index < Math.floor(value)) {
		return 'full'
	} else if (index === Math.floor(value) && decimal > 0.5) {
		return 'full'
	} else if (index === Math.floor(value) && decimal > 0 && decimal <= 0.5) {
		return 'half'
	} else {
		return 'empty'
	}
}

// 处理半星点击
const handleHalfClick = (index: number) => {
	if (props.isDisabled) return
	emit('update:rateValue', index + 0.5)
}

// 处理全星点击
const handleFullClick = (index: number) => {
	if (props.isDisabled) return
	emit('update:rateValue', index + 1)
}

// 处理整体点击（兼容旧版本）
const handleClick = (event: MouseEvent, index: number) => {
	if (props.isDisabled) return
	const target = event.currentTarget as HTMLElement
	const rect = target.getBoundingClientRect()
	const clickX = event.clientX - rect.left
	const isHalf = clickX < rect.width / 2
	emit('update:rateValue', index + (isHalf ? 0.5 : 1))
}
</script>

<style lang="scss" scoped>
.sm-rate {
	.sm-rate-item {
		cursor: pointer;
		.star-wrapper {
			&:hover {
				// transform: scale(1.1);
				// transition: transform 0.2s ease;
			}
		}
	}
}
</style>
