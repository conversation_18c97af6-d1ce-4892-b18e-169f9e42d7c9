<!--  -->
<template>
    <div class="custom-select-container relative" ref="selectRef">
        <!-- 选择框 -->
        <div 
            class="custom-select-input flex items-center justify-between px-4 py-3 w-full h-[48px] lg:h-[62px] rounded-lg border border-solid border-[rgba(28,33,88,0.5)] cursor-pointer"
            @click="toggleDropdown">
            <div class="text-[16px] lg:text-[20px] text-[#1C2158] font-['Open_Sans']" :class="{'opacity-50': !selectedOption}">
                {{ selectedOption ? selectedOption.label : placeholder }}
            </div>
            <svg 
                class="w-5 h-5 text-[#1C2158] transition-transform" 
                :class="{ 'rotate-180': isOpen }" 
                xmlns="http://www.w3.org/2000/svg" 
                viewBox="0 0 20 20" 
                fill="currentColor">
                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
        </div>

        <!-- 下拉菜单 (使用Teleport移到body) -->
        <Teleport to="body">
            <Transition
                enter-active-class="transition duration-200 ease-out"
                enter-from-class="opacity-0 transform scale-95"
                enter-to-class="opacity-100 transform scale-100"
                leave-active-class="transition duration-150 ease-in"
                leave-from-class="opacity-100 transform scale-100"
                leave-to-class="opacity-0 transform scale-95">
                <div 
                    v-if="isOpen" 
                    class="custom-select-dropdown fixed z-[4000] bg-white  shadow-lg max-h-60 overflow-auto py-[10px] rounded-[20px]"
                    :style="dropdownStyle">
                    <div 
                        v-for="option in options" 
                        :key="option.value" 
                        class="custom-select-option py-5 px-4 cursor-pointer hover:bg-gray-100 transition-colors pl-[56px] relative"
                        :class="{ 'bg-blue-100': option.value === modelValue }"
                        @click="selectOption(option)">
                        <div class="flex items-center ">
                            <div v-if="option.value === modelValue" class="check-mark text-blue-500 absolute left-[16px]">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <span class="text-[16px] lg:text-[20px] text-[#1C2158]">{{ option.label }}</span>
                        </div>
                    </div>
                </div>
            </Transition>
        </Teleport>
    </div>
</template>

<script lang='ts' setup>
import { ref, computed, watch, onMounted, onBeforeUnmount, nextTick } from 'vue'

interface Option {
    label: string;
    value: string | number;
}

const props = defineProps<{
    modelValue: string | number;
    options: Option[];
    placeholder?: string;
}>()

const emit = defineEmits(['update:modelValue'])

const isOpen = ref(false)
const selectRef = ref<HTMLElement | null>(null)
const dropdownPosition = ref({ top: 0, left: 0, width: 0 })

// 下拉菜单样式
const dropdownStyle = computed(() => {
    return {
        top: `${dropdownPosition.value.top}px`,
        left: `${dropdownPosition.value.left}px`,
        width: `${dropdownPosition.value.width}px`,
        minWidth: `${dropdownPosition.value.width}px`,
    }
})

// 根据当前选中的值找到对应的选项
const selectedOption = computed(() => {
    return props.options.find(option => option.value === props.modelValue)
})

// 更新下拉菜单位置
const updateDropdownPosition = () => {
    if (!selectRef.value) return

    const rect = selectRef.value.getBoundingClientRect()
    const spaceBelow = window.innerHeight - rect.bottom
    const spaceAbove = rect.top
    
    // 设置宽度与选择框一致
    dropdownPosition.value.width = rect.width
    dropdownPosition.value.left = rect.left + window.scrollX
    
    // 根据可用空间决定显示在上方还是下方
    if (spaceBelow >= 260 || spaceBelow > spaceAbove) {
        // 在下方显示
        dropdownPosition.value.top = rect.bottom + window.scrollY + 8
    } else {
        // 在上方显示，需要减去下拉菜单的高度
        dropdownPosition.value.top = rect.top + window.scrollY - 260
    }
}

// 切换下拉菜单的显示状态
const toggleDropdown = () => {
    isOpen.value = !isOpen.value
    
    if (isOpen.value) {
        // 显示下拉菜单时更新位置
        nextTick(() => {
            updateDropdownPosition()
        })
    }
}

// 选择选项
const selectOption = (option: Option) => {
    emit('update:modelValue', option.value)
    isOpen.value = false
}

// 处理窗口大小变化
const handleResize = () => {
    if (isOpen.value) {
        updateDropdownPosition()
    }
}

// 处理滚动事件
const handleScroll = () => {
    if (isOpen.value) {
        updateDropdownPosition()
    }
}

// 点击外部关闭下拉菜单
const closeDropdown = (event: MouseEvent) => {
    const target = event.target as HTMLElement
    if (isOpen.value && 
        selectRef.value && 
        !selectRef.value.contains(target) && 
        !target.closest('.custom-select-dropdown')) {
        isOpen.value = false
    }
}

onMounted(() => {
    document.addEventListener('click', closeDropdown)
    window.addEventListener('resize', handleResize)
    window.addEventListener('scroll', handleScroll, true)
})

onBeforeUnmount(() => {
    document.removeEventListener('click', closeDropdown)
    window.removeEventListener('resize', handleResize)
    window.removeEventListener('scroll', handleScroll, true)
})
</script>

<style lang='scss' scoped>
.custom-select-container {
    width: 100%;
    
    .custom-select-input {
        background-color: transparent
    }
}

:deep(.custom-select-dropdown) {
    border: 1px solid rgba(28, 33, 88, 0.5);
    background-color: white;
    
    .custom-select-option {
        &.bg-blue-100 {
            background-color: rgba(228, 235, 254, 1);
        }
    }
}
</style>