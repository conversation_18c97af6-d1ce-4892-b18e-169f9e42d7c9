<template>
	<TransitionGroup name="ant-message-fade" tag="div" class="ant-message-container">
		<div v-for="item in messages" :key="item.id" class="ant-message" :class="item.type">
			<span class="ant-message-icon">
				<MessageIcons :type="item.type" />
			</span>
			<span class="ant-message-content">{{ item.content }}</span>
		</div>
	</TransitionGroup>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import MessageIcons from './MessageIcons.vue'

interface MessageItem {
	id: number
	content: string
	type: 'success' | 'error' | 'warning' | 'info'
	duration: number
}

const messages = ref<MessageItem[]>([])

const add = (options: Partial<MessageItem>) => {
	const id = Date.now() + Math.random()
	const message: MessageItem = {
		id,
		content: options.content || '',
		type: options.type || 'info',
		duration: options.duration ?? 3000,
	}
	messages.value.push(message)
	if (message.duration > 0) {
		setTimeout(() => remove(id), message.duration)
	}
	return id
}

const remove = (id: number) => {
	const idx = messages.value.findIndex((m) => m.id === id)
	if (idx !== -1) messages.value.splice(idx, 1)
}

defineExpose({ add, remove })
</script>

<style lang="scss" scoped>
.ant-message-container {
	position: fixed;
	top: 16px;
	left: 0;
	right: 0;
	z-index: 9999;
	display: flex;
	flex-direction: column;
	align-items: center;
	pointer-events: none;
	width: 100%;
	padding: 0;
}

.ant-message {
	display: flex;
	align-items: center;
	// min-width: 200px;
    width:max-content;
	max-width: 90vw;
    
	margin-bottom: 8px;
	background: #ffffff;
	color: rgba(0, 0, 0, 0.85);
	border-radius: 4px;
	box-shadow: 0 3px 8px rgba(0,0,0,0.15);
	padding: 8px 16px;
	font-size: 14px;
	pointer-events: all;
	transition: box-shadow 0.4s;
	.ant-message-icon {
		margin-inline-end: 8px;
		font-size: 18px;
		display: flex;
		align-items: center;
	}
	&.success .ant-message-icon { color: #52c41a; }
	&.error .ant-message-icon { color: #ff4d4f; }
	&.warning .ant-message-icon { color: #faad14; }
	&.info .ant-message-icon { color: #1890ff; }
}

.ant-message-content {
	word-break: break-all;
	flex: 1;
}

/* 动画 */
.ant-message-fade-enter-active,
.ant-message-fade-leave-active {
	transition: opacity 0.4s cubic-bezier(0.645, 0.045, 0.355, 1), transform 0.4s cubic-bezier(0.645, 0.045, 0.355, 1), max-height 0.4s cubic-bezier(0.645, 0.045, 0.355, 1), margin-bottom 0.4s cubic-bezier(0.645, 0.045, 0.355, 1);
	overflow: hidden;
}
.ant-message-fade-enter-from,
.ant-message-fade-leave-to {
	opacity: 0;
	max-height: 0;
	margin-bottom: 0;
	transform: translateY(-50%) scale(0.98);
}
.ant-message-fade-enter-to,
.ant-message-fade-leave-from {
	opacity: 1;
	max-height: 150px;
	margin-bottom: 8px;
	transform: translateY(0) scale(1);
}
.ant-message-fade-move {
	transition: transform 0.4s cubic-bezier(0.645, 0.045, 0.355, 1);
}

/* 移动端适配 */
// @media (max-width: 600px) {
// 	.ant-message {
// 		min-width: 120px;
// 		max-width: 96vw;
// 		font-size: 13px;
// 		padding: 6px 12px;
// 	}
// }
</style>
