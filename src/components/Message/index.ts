import { h, render } from 'vue'
import MessageComponent from './MessageComponent.vue'

let instance: any = null

function getInstance() {
  if (!instance) {
    const container = document.createElement('div')
    document.body.appendChild(container)
    const vnode = h(MessageComponent)
    render(vnode, container)
    instance = vnode.component?.exposed
  }
  return instance
}

const message = {
  success(content: string, duration = 3000) {
    getInstance().add({ content, type: 'success', duration })
  },
  error(content: string, duration = 3000) {
    getInstance().add({ content, type: 'error', duration })
  },
  warning(content: string, duration = 3000) {
    getInstance().add({ content, type: 'warning', duration })
  },
  info(content: string, duration = 3000) {
    getInstance().add({ content, type: 'info', duration })
  }
}

export default message
export { message }
