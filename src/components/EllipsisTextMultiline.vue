<template>
	<div class="ellipsis-js-wrap">
		<span v-if="!expanded">
			{{ displayText }}
			<span v-if="isClamped">
				...<span class="read-more" @click="expanded = true">
					Read more
					<img src="@/assets/public/arrow-down.png" alt="" class="w-[16px] h-[16px]" style="display:inline-block;vertical-align:middle;" />
				</span>
			</span>
		</span>
		<span v-else>
			{{ text }}<span class="read-more" @click="expanded = false">
                Collapse <img src="@/assets/public/arrow-down.png" alt="" class="w-[16px] h-[16px] rotate-180" style="display:inline-block;vertical-align:middle;" />
            </span>
		</span>
		<span ref="measure" class="measure-span"></span>
	</div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, watch } from 'vue'

const props = defineProps<{ text: string, lineClamp?: number }>()
const expanded = ref(false)
const isClamped = ref(false)
const displayText = ref(props.text)
const measure = ref<HTMLElement | null>(null)
const lineClamp = props.lineClamp ?? 2

const readMoreText = 'Read more。。。'
const imgSpace = '        ' // 8个空格，约等于图片宽度，可微调

const getLineHeight = () => 22 // 你的实际行高
const getMaxHeight = () => lineClamp * getLineHeight()

const calcDisplayText = async () => {
	displayText.value = props.text
	isClamped.value = false
	await nextTick()
	await new Promise(r => setTimeout(r, 0))
	if (!measure.value) return

	// 设置测量容器样式，确保和实际显示一致
	measure.value.style.display = '-webkit-box'
	measure.value.style.webkitBoxOrient = 'vertical'
	measure.value.style.webkitLineClamp = String(lineClamp)
	measure.value.style.overflow = 'hidden'
	measure.value.style.textOverflow = 'ellipsis'
	measure.value.style.fontSize = '15px'
	measure.value.style.lineHeight = '22px'
	measure.value.style.maxHeight = getMaxHeight() + 'px'
	measure.value.style.wordBreak = 'break-all'
	measure.value.style.whiteSpace = 'normal'
	measure.value.innerText = props.text + '...' + readMoreText + imgSpace

	await nextTick()
	await new Promise(r => setTimeout(r, 0))

	if (measure.value.scrollHeight <= getMaxHeight() + 2) {
		// 不溢出
		displayText.value = props.text
		isClamped.value = false
		return
	}

	// 二分法裁剪
	let left = 0
	let right = props.text.length
	let result = right
	while (left <= right) {
		const mid = Math.floor((left + right) / 2)
		measure.value.innerText = props.text.slice(0, mid) + '...' + readMoreText + imgSpace
		await nextTick()
		await new Promise(r => setTimeout(r, 0))
		if (measure.value.scrollHeight > getMaxHeight() + 2) {
			right = mid - 1
		} else {
			result = mid
			left = mid + 1
		}
	}
	displayText.value = props.text.slice(0, result)
	isClamped.value = true
}

onMounted(calcDisplayText)
watch(() => props.text, calcDisplayText)
watch(() => props.lineClamp, calcDisplayText)
</script>

<style scoped>
.ellipsis-js-wrap {
	font-size: 15px;
	color: #1C2158;
	line-height: 22px;
	position: relative;
	word-break: break-all;
}
.read-more {
	color: #4484FF;
	font-size: 14px;
	cursor: pointer;
	user-select: none;
	padding-left: 4px;
}
.measure-span {
	visibility: hidden;
	position: absolute;
	left: -9999px;
	top: 0;
	width: 100%;
	pointer-events: none;
	z-index: -1;
	white-space: normal;
}
</style>
