<template>
	<div class="bg-white rounded-2xl shadow-[0_0_0_2px_#b3baff22] font-sans w-[319px] lg:w-[360px] relative">
		<!-- 头部 -->
		<div class="h-16 py-2">
			<div class="flex justify-between items-center gap-8 lg:gap-9">
				<div class="flex items-center">
					<img src="@/assets/calendar/arrow-left.png" alt="calendar" class="w-[42px] h-[42px] lg:w-12 lg:h-12 cursor-pointer" @click="prevMonth" />
					<div class="relative">
						<button class="text-black text-[14px] lg:text-r-0.9 font-[500] px-1 py-1 rounded hover:bg-indigo-100 transition flex items-center" @click="toggleMonthDropdown">
							<span class="mr-2">{{ monthNames[currentDate.getMonth()] }}</span>
							<img src="@/assets/calendar/arrow-down.png" alt="calendar" class="w-[18px] h-[18px] cursor-pointer" />
						</button>
					</div>
					<img src="@/assets/calendar/arrow-right.png" alt="calendar" class="w-[42px] h-[42px] lg:w-12 lg:h-12 cursor-pointer" @click="nextMonth" />
				</div>
				<div class="flex items-center">
					<img src="@/assets/calendar/arrow-left.png" alt="calendar" class="w-[42px] h-[42px] lg:w-12 lg:h-12 cursor-pointer" @click="prevYear" />
					<div class="relative">
						<button class="text-black text-[14px] lg:text-r-0.9 font-[500] px-1 py-1 rounded hover:bg-indigo-100 transition flex items-center" @click="toggleYearDropdown">
							<span class="mr-2">{{ currentDate.getFullYear() }}</span>
							<img src="@/assets/calendar/arrow-down.png" alt="calendar" class="w-[18px] h-[18px] cursor-pointer" />
						</button>
					</div>
					<img src="@/assets/calendar/arrow-right.png" alt="calendar" class="w-[42px] h-[42px] lg:w-12 lg:h-12 cursor-pointer" @click="nextYear" />
				</div>
			</div>
		</div>
		<!-- 星期 -->
		<div class="grid grid-cols-7 px-3 h-[48px] items-center">
			<div v-for="day in weekDays" :key="day" class="text-center text-indigo-900 text-base select-none">{{ day }}</div>
		</div>
		<!-- 日期格子 -->
		<div class="grid grid-cols-7 px-3">
			<div v-for="(day, index) in calendarDays" :key="index" class="flex items-center justify-center aspect-square text-base cursor-pointer select-none transition p-1"
				@click="selectDate($event, day)">
				<div class="w-full h-full rounded-full flex items-center justify-center"
					:class="[
						day.currentMonth ? 'text-[#1C2158]' : 'text-gray-400',
						isSelected(day.date) ? 'bg-[#4484FF] text-[#fff]' : '',
						isToday(day.date) ? (isSelected(day.date) ? 'ring-2 ring-white' : 'ring-2 ring-blue-500') : '',
						isDisabled(day.date) ? 'opacity-40 cursor-not-allowed' : ''
					]">
					{{ day.dayNumber }}
				</div>
			</div>
		</div>
		<!-- 底部按钮 -->
		<div class="flex justify-end items-center h-[48px] px-3 py-1">
			<button class="text-md font-bold text-[#1C2158] px-6 py-2 rounded-lg" @click="onCancel">Cancel</button>
			<button class="text-md font-bold text-[#1C2158] py-2 rounded-lg" @click="onDone">Done</button>
		</div>
		<!-- 下拉层 -->
		<Transition enter-active-class="transition duration-200 ease-out" enter-from-class="opacity-0 scale-95" enter-to-class="opacity-100 scale-100" leave-active-class="transition duration-150 ease-in" leave-from-class="opacity-100 scale-100" leave-to-class="opacity-0 scale-95">
			<div v-show="showDropdown" class="absolute inset-0 z-20 flex flex-col items-start justify-center h-full rounded-2xl bg-white overflow-hidden" @click.self="showDropdown = false">
				<div class="w-full h-[48px] flex items-center justify-between px-[52px]">
					<button class="text-black text-r-0.9 font-[500] px-4 py-1 rounded transition flex items-center" :class="currentSelectMode === 'year' ? 'text-gray-400' : 'text-[#49454F]'" @click="toggleMonthDropdown">
						<span class="mr-2">{{ monthNames[currentDate.getMonth()] }}</span>
						<img src="@/assets/calendar/arrow-down.png" alt="calendar" class="w-[18px] h-[18px] cursor-pointer" v-show="currentSelectMode === 'month'" />
					</button>
					<button class="text-black text-r-0.9 font-[500] px-4 py-1 rounded transition flex items-center" :class="currentSelectMode === 'month' ? 'text-gray-400' : 'text-[#49454F]'" @click="toggleYearDropdown">
						<span class="mr-2">{{ currentDate.getFullYear() }}</span>
						<img src="@/assets/calendar/arrow-down.png" alt="calendar" class="w-[18px] h-[18px] cursor-pointer" v-show="currentSelectMode === 'year'" />
					</button>
				</div>
				<div class="flex-1 w-full overflow-auto bg-white rounded-b-2xl shadow-lg py-2 overflow-y-auto border-t border-[#CAC4D0]">
					<div class="border-r" v-if="currentSelectMode === 'month'">
						<div v-for="(month, idx) in monthNames" :key="month" 
							class="relative flex items-center h-[48px] pl-14 text-[#1C2158] text-base mr-4 cursor-pointer transition" 
							:class="[
								idx === currentDate.getMonth() ? 'bg-indigo-100 text-blue-600' : 'hover:bg-indigo-50 text-indigo-900',
								`custom-select-month-${idx}`
							]" 
							@click="selectMonth(idx)">
							<img src="@/assets/calendar/select.png" alt="calendar" class="absolute left-3 w-[24px] h-[24px] cursor-pointer" v-show="idx === currentDate.getMonth()" />
							{{ month }}
						</div>
					</div>
					<div class="border-r" v-else>
						<div v-for="year in yearRange" :key="year" 
							class="relative flex items-center h-[48px] pl-14 text-base text-[#1C2158] mr-4 cursor-pointer transition" 
							:class="[
								year === currentDate.getFullYear() ? 'bg-indigo-100 text-blue-600' : 'hover:bg-indigo-50 text-indigo-900',
								`custom-select-year-${year}`
							]" 
							@click="selectYear(year)">
							<img src="@/assets/calendar/select.png" alt="calendar" class="absolute left-3 w-[24px] h-[24px] cursor-pointer" v-show="year === currentDate.getFullYear()" />
							{{ year }}
						</div>
					</div>
				</div>
			</div>
		</Transition>
	</div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'

const props = defineProps<{
	modelValue?: Date,
	disabledDate?: (date: Date) => boolean
}>()

const emit = defineEmits(['update:modelValue', 'cancel', 'confirm'])

const currentDate = ref(props.modelValue ? new Date(props.modelValue) : new Date())
const selectedDate = ref(props.modelValue ? new Date(props.modelValue) : null)
const currentSelectMode = ref<'month' | 'year'>('month')
const weekDays = ['S', 'M', 'T', 'W', 'T', 'F', 'S']
const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']

const showDropdown = ref(false)

const yearRange = computed(() => {
	const years = []
	const current = new Date().getFullYear()
	for (let y = current - 50; y <= current + 50; y++) {
		years.push(y)
	}
	return years
})

const calendarDays = computed(() => {
	const year = currentDate.value.getFullYear()
	const month = currentDate.value.getMonth()

	const firstDay = new Date(year, month, 1)
	const lastDay = new Date(year, month + 1, 0)

	const days = []

	// Add days from previous month
	const firstDayWeekday = firstDay.getDay()
	for (let i = firstDayWeekday - 1; i >= 0; i--) {
		const date = new Date(year, month, -i)
		days.push({
			date,
			dayNumber: date.getDate(),
			currentMonth: false,
		})
	}

	// Add days from current month
	for (let i = 1; i <= lastDay.getDate(); i++) {
		const date = new Date(year, month, i)
		days.push({
			date,
			dayNumber: i,
			currentMonth: true,
		})
	}

	// Add days from next month
	const remainingDays = 42 - days.length // 6 rows * 7 days
	for (let i = 1; i <= remainingDays; i++) {
		const date = new Date(year, month + 1, i)
		days.push({
			date,
			dayNumber: date.getDate(),
			currentMonth: false,
		})
	}

	return days
})

function prevMonth() {
	currentDate.value = new Date(currentDate.value.getFullYear(), currentDate.value.getMonth() - 1, 1)
	showDropdown.value = false
}

function nextMonth() {
	currentDate.value = new Date(currentDate.value.getFullYear(), currentDate.value.getMonth() + 1, 1)
	showDropdown.value = false
}

function prevYear() {
	currentDate.value = new Date(currentDate.value.getFullYear() - 1, currentDate.value.getMonth(), 1)
	showDropdown.value = false
}

function nextYear() {
	currentDate.value = new Date(currentDate.value.getFullYear() + 1, currentDate.value.getMonth(), 1)
	showDropdown.value = false
}

function selectMonth(idx: number) {
	currentDate.value = new Date(currentDate.value.getFullYear(), idx, 1)
	showDropdown.value = false
}

function selectYear(year: number) {
	currentDate.value = new Date(year, currentDate.value.getMonth(), 1)
	showDropdown.value = false
}

function selectDate(e: Event, day: { date: Date; currentMonth: boolean }) {
	e.preventDefault()
	e.stopPropagation()
	if (isDisabled(day.date)) return
	if (!day.currentMonth) {
		currentDate.value = new Date(day.date.getFullYear(), day.date.getMonth(), 1)
	}
	selectedDate.value = day.date
	emit('update:modelValue', day.date)
}

function isSelected(date: Date) {
	if (!selectedDate.value) return false
	return date.toDateString() === selectedDate.value.toDateString()
}

function isToday(date: Date) {
	const today = new Date()
	return date.toDateString() === today.toDateString()
}

function onCancel() {
	emit('cancel')
}

function onDone() {
	emit('confirm', selectedDate.value)
}

function toggleMonthDropdown(e: Event) {
	e.preventDefault()
	e.stopPropagation()
	currentSelectMode.value = 'month'
	showDropdown.value = !showDropdown.value
	
	// 在下拉菜单显示后滚动到当前月份
	if (showDropdown.value) {
		nextTick(() => {
			const selectedMonthEl = document.querySelector(`.custom-select-month-${currentDate.value.getMonth()}`)
			if (selectedMonthEl) {
				selectedMonthEl.scrollIntoView({ block: 'center', behavior: 'auto' })
			}
		})
	}
}

function toggleYearDropdown(e: Event) {
	e.preventDefault()
	e.stopPropagation()
	currentSelectMode.value = 'year'
	showDropdown.value = !showDropdown.value
	
	// 在下拉菜单显示后滚动到当前年份
	if (showDropdown.value) {
		nextTick(() => {
			const selectedYearEl = document.querySelector(`.custom-select-year-${currentDate.value.getFullYear()}`)
			if (selectedYearEl) {
				selectedYearEl.scrollIntoView({ block: 'center', behavior: 'auto' })
			}
		})
	}
}

function isDisabled(date: Date) {
	return props.disabledDate ? props.disabledDate(date) : false
}

// 点击外部关闭下拉
watch([showDropdown], ([d]) => {
	if (d) {
		const handler = (e: MouseEvent) => {
			showDropdown.value = false
			window.removeEventListener('click', handler)
		}
		setTimeout(() => window.addEventListener('click', handler), 0)
	}
})
</script>
