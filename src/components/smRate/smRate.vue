<!--  -->
<template>
    <div>
        <el-rate :model-value="modelValue" :icons="icons" @update:modelValue="onUpdate" allow-half :show-score="true"
            v-bind="$attrs" :text-color="textColor" :class="{ 'show-small': showSmall }" />
    </div>
</template>

<script lang="ts">
import { markRaw } from 'vue'
import UnChecked from './UnChecked.vue'
import HalfChecked from './HalfChecked.vue'
import Checked from './Checked.vue'
export default {
    name: 'smRate',
    props: {
        modelValue: {
            type: Number,
            default: 0,
        },
        textColor: {
            type: String,
            default: '#FF7C44'
        },
        showSmall: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            icons: markRaw([UnChecked, HalfChecked, Checked]),
            // texts: ['1.0', '2.0', '3.0', '4.0', '5.0']
        }
    },
    methods: {
        onUpdate(val: number) {
            this.$emit('update:modelValue', val)
        }
    }
}
</script>
<style lang="scss" scoped>
:deep(.el-rate__text) {
    color: #1C2158;
    font-weight: 600;
    font-size: 20px;
}

.show-small {
    :deep(.el-rate__text) {
        color: #1C2158;
        font-weight: 600;
        font-size: 20px;

        @media screen and (max-width:640px) {
            font-weight: 400;
            font-size: 12px;
        }
    }
}
</style>