<template>
    <div ref="containerRef" class="load-more-container">
        <slot></slot>
        <div v-if="loading" class="load-more-loading">loading...</div>
        <div v-if="finished" class="load-more-finished">no more</div>
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from 'vue'
defineOptions({
    name: 'LoadMore',
    inheritAttrs: false
})
interface Props {
    threshold?: number // 距离底部多少px触发加载
    throttle?: number // 节流时间(ms)
    loading?: boolean // 是否正在加载
    finished?: boolean // 是否加载完成
}

const props = withDefaults(defineProps<Props>(), {
    threshold: 100,
    throttle: 300,
    loading: false,
    finished: false
})

const emit = defineEmits<{
    (e: 'loadMore'): void
    (e: 'loadEnd'): void
}>()

const containerRef = ref<HTMLElement | null>(null)
let lastCall = 0
let ticking = false

function onScroll() {
    if (props.loading || props.finished) return
    const now = Date.now()
    if (now - lastCall < props.throttle) return
    lastCall = now

    const el = containerRef.value
    if (!el) return

    const scrollTop = el.scrollTop
    const scrollHeight = el.scrollHeight
    const clientHeight = el.clientHeight

    if (scrollHeight - scrollTop - clientHeight <= props.threshold) {
        emit('loadMore')
    }
}

function handleScrollEvent() {
    if (ticking) return
    ticking = true
    window.requestAnimationFrame(() => {
        onScroll()
        ticking = false
    })
}

onMounted(() => {
    const el = containerRef.value
    if (!el) return
    el.addEventListener('scroll', handleScrollEvent)
})

onUnmounted(() => {
    const el = containerRef.value
    if (!el) return
    el.removeEventListener('scroll', handleScrollEvent)
})
</script>

<style scoped>
.load-more-container {
    width: 100%;
    height: 100%;
    min-height: 20px;
    position: relative;
    overflow: auto;
}

.load-more-loading {
    text-align: center;
    color: #888;
    padding: 12px 0;
    font-size: 14px;
}

.load-more-finished {
    text-align: center;
    color: #bbb;
    padding: 12px 0;
    font-size: 14px;
}
</style>