<!--  -->
<template>
    <div>
        <!--登录-->
        <el-dialog :model-value="dialogVisible" @update:model-value="$emit('update:dialogVisible', $event)"
            :before-close="handleClose" :show-close="false"
            class="login-dialog flex flex-col w-[347px] h-[570px] rounded-[40px] lg:w-[1200px] p-0 lg:h-[670px]">
            <div class="login-content flex w-full h-full">
                <div class="login-left relative w-0 lg:w-1/2 hidden lg:block">
                    <div class="absolute left-[50px] bottom-[50px] text-white">
                        <div class="login-title flex gap-2">
                            <span class="text-[28px] font-bold">Welcome to</span>
                            <span class="text-[32px] font-bold italic font-[playFair]">StarMet.</span>
                        </div>
                        <div class="mt-3.5 text-6 font-bold">100% privacy and security guaranteed</div>
                    </div>
                </div>
                <div
                    class="login-right relative w-full pt-[57px] px-3 rounded-[20px] lg:pt-20 lg:px-10 lg:w-1/2 lg:rounded-none lg:rounded-r-[40px] lg:px-20 lg:pt-20">
                    <div class="flex flex-col items-center lg:flex-row lg:px-10">
                        <img src="@/assets/common/logo.png" alt="starMet" class="w-[70px] h-[70px]" />
                        <div class="flex flex-col justify-center items-center lg:items-start lg:ml-4">
                            <span class="logo-title text-[26px] lg:text-[30px] mt-2 lg:mt-0">StarMet</span>
                            <span
                                class="text-[14px] mt-[11px] font-semibold text-[#1C2158] opacity-70 lg:text-4 lg:mt-1.5">Get
                                insights from real psychics.</span>
                        </div>
                    </div>
                    <!--登录菜单选择-->
                    <div class="pt-9 px-0">
                        <div class="w-full h-[52px] flex items-center pl-[47px] cursor-pointer rounded-[8px] hover:bg-[#f5f5f5] transition-all duration-300 bg-white border border-solid mt-[10px] lg:mt-5 lg:h-[68px] lg:pl-[123px]"
                            style="border-color: rgba(28, 33, 88, 0.2)" v-for="item in loginMenu" :key="item.title"
                            @click="handleTherrPartyLogin(item.value)">
                            <div class="flex items-center">
                                <img :src="item.icon" alt="" class="w-[36px] h-[36px] mr-[10px]" />
                                <span class="text-2 font-semibold lg:text-[18px]">{{ item.title }}</span>
                            </div>
                        </div>
                        <div class="agress flex mt-5 lg:mt-6">
                            <img src="@/assets/login/agress.png" alt="" class="w-[14px] h-[14px] mr-1 mt-1" />
                            <span class="text-[14px] text-[#1C215899] leading-[18px]">By continuing you accept our <span
                                    class="text-[#4484FF]">Terms</span> and <span class="text-[#4484FF]">Privacy
                                    Policy.</span> </span>
                        </div>
                    </div>
                    <!---关闭按钮-->
                    <img src="@/assets/common/close.png" alt=""
                        class="absolute w-[34px] h-[34px] top-1.5 right-1.5 opacity-50 lg:w-[44px] lg:h-[44px] lg:top-5 lg:right-5 cursor-pointer"
                        @click="handleClose" />
                </div>
            </div>
        </el-dialog>
        <!--创建账号-->
        <el-dialog :model-value="dialogAccountVisible"
            @update:model-value="$emit('update:dialogAccountVisible', $event)" :before-close="handleAccountClose"
            :close-on-click-modal="false" :show-close="false"
            class="top-[90px] login-dialog flex flex-col w-[347px] rounded-[40px] lg:w-[1000px] p-0" :top="'90px'">
            <div
                class="account-content max-h-[630px] relative flex flex-col overflow-y-hidden w-full h-full rounded-[20px] py-[33px] px-[14px] lg:px-[60px] lg:pt-[50px] lg:max-h-[650px]">
                <!--顶部的背景图-->
                <img src="@/assets/login/account-lg.png" alt=""
                    class="absolute top-0 left-0 hidden w-full h-[270px] lg:block" />
                <img src="@/assets/login/account-md.png" alt=""
                    class="absolute top-0 left-0 block w-full h-[140px] lg:hidden" />
                <div class="account-content-box relative">
                    <div class="account-content-box-title flex flex-col text-[#1C2158]">
                        <span class="text-[24px] font-bold font-[Philosopher] lg:text-[32px]">Create an account</span>
                        <span class="mt-[10px] font-['Open_Sans'] font-semblod text-[14px] lg:text-[18px]">Complete
                            information for precise answers from advisors</span>
                    </div>
                </div>
                <!--登录表单选择-->
                <div class="account-content-form relative mt-[30px] flex-1 overflow-y-auto">
                    <el-form :model="accountForm" label-position="top" :rules="accountFormRules" ref="accountFormRef">
                        <div class="lg:grid grid-cols-2 gap-10">
                            <el-form-item :label="'First Name'" prop="firstName">
                                <el-input :placeholder="'Fill your name'" v-model="accountForm.firstName"
                                    :clearable="true"></el-input>
                            </el-form-item>
                            <el-form-item :label="'last Name'" prop="lastName">
                                <el-input :placeholder="'Provide your last name'" v-model="accountForm.lastName"
                                    :clearable="true"></el-input>
                            </el-form-item>
                        </div>
                        <div class="lg:grid grid-cols-2 gap-10">
                            <el-form-item :label="'Birth Date'" prop="birthDate">
                                <div class="relative w-full" ref="birthDateRef">
                                    <el-input :placeholder="'Select your birth date'" v-model="accountForm.birthDate"
                                        readonly @click="toggleCalendar"></el-input>
                                    <Teleport to="body">
                                        <Transition enter-active-class="transition duration-200 ease-out"
                                            enter-from-class="opacity-0 transform scale-95 -translate-y-2"
                                            enter-to-class="opacity-100 transform scale-100 translate-y-0"
                                            leave-active-class="transition duration-150 ease-in"
                                            leave-from-class="opacity-100 transform scale-100 translate-y-0"
                                            leave-to-class="opacity-0 transform scale-95 -translate-y-2">
                                            <div v-if="showCalendar" :style="calendarStyle"
                                                class="fixed z-[4000] shadow-lg origin-top calendar-dropdown rounded-[20px]">
                                                <CustomCalendar v-model="selectedDate" @confirm="handleDateConfirm"
                                                    @cancel="showCalendar = false" />
                                            </div>
                                        </Transition>
                                    </Teleport>
                                </div>
                            </el-form-item>
                            <el-form-item :label="'Birth Time'" prop="birthTime">
                                <div class="relative w-full" ref="birthTimeRef">
                                    <el-input :placeholder="'Fill your birth time'" v-model="accountForm.birthTime"
                                        readonly @click="toggleTimePicker"> </el-input>
                                    <!-- 时间选择器弹出层 -->
                                    <Teleport to="body">
                                        <Transition enter-active-class="transition duration-200 ease-out"
                                            enter-from-class="opacity-0 transform scale-95 -translate-y-2"
                                            enter-to-class="opacity-100 transform scale-100 translate-y-0"
                                            leave-active-class="transition duration-150 ease-in"
                                            leave-from-class="opacity-100 transform scale-100 translate-y-0"
                                            leave-to-class="opacity-0 transform scale-95 -translate-y-2">
                                            <div v-if="showTimePicker" :style="timePickerStyle"
                                                class="fixed z-[4000] shadow-lg origin-top calendar-dropdown">
                                                <CustomTimePicker v-model="selectedTime" @confirm="handleTimeConfirm"
                                                    @cancel="showTimePicker = false" />
                                            </div>
                                        </Transition>
                                    </Teleport>
                                </div>
                            </el-form-item>
                        </div>
                        <div class="lg:grid grid-cols-2 gap-10">
                            <el-form-item :label="'Gender'" prop="gender">
                                <CustomeSelect v-model="accountForm.gender" :options="genderOptions"
                                    placeholder="Select your gender" />
                            </el-form-item>
                            <el-form-item :label="'Birthplace'" prop="birthPlace">
                                <SmSearchRemote v-model="accountForm.birthPlace" :remote-api="searchBirthPlace"
                                    @changePlace="handleChangePlace" />
                            </el-form-item>
                        </div>
                    </el-form>
                </div>
                <div class="login_button flex justify-center lg:mt-[16px]">
                    <button @click="handleSubmit(accountFormRef)"
                        class="submit-button h-12 text-4.5 rounded-[12px] max-w-[480px] font-[Philosopher] font-bold text-white lg:h-[68px]">Explore
                        Answers within Yourself</button>
                </div>
                <!---关闭按钮-->
                <!-- <img src="@/assets/common/close.png" alt=""
                    class="absolute w-[34px] h-[34px] top-1.5 right-1.5 opacity-50 lg:w-[44px] lg:h-[44px] lg:top-5 lg:right-5 cursor-pointer"
                    @click="handleAccountClose" /> -->
            </div>
        </el-dialog>
        <!-- <button @click="googleLogin">Google 一键登录</button> -->
    </div>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, nextTick, computed, onMounted } from 'vue'
import CustomCalendar from '@/components/CustomCalendar.vue'
import CustomeSelect from '@/components/customeSelect/customeSelect.vue'
import SmSearchRemote from '@/components/smSearchRemote/smSearchRemote.vue'
import CustomTimePicker from '@/components/CustomTimePicker.vue'
import { initializeApp } from 'firebase/app'
import { getAuth, GoogleAuthProvider, FacebookAuthProvider, signInWithPopup } from 'firebase/auth'


import { firsebaseLogin } from '@/api/login'
import { getUserInfo, updateUserInfo } from '@/api/user'
import { searchBirthPlace } from '@/api/common'
import { ElMessage } from 'element-plus'
import type { FormInstance } from 'element-plus'
import { useUserStore } from '@/stores/user'
interface ChangePlaceData {
    latitude: string
    longitude: string
}
const useUser = useUserStore()
const firebaseConfig = {
    apiKey: 'AIzaSyDr9maLYBB50ESsJr4dh0wsvdt9lBF0JdE',
    authDomain: 'starmet-46f74.firebaseapp.com',
    projectId: 'starmet-46f74',
    storageBucket: 'starmet-46f74.firebasestorage.app',
    messagingSenderId: '1077559420093',
    appId: '1:1077559420093:web:8f4e01a5163f617507c3d4',
}
const firebaseConfigTest = {
    apiKey: 'AIzaSyAe8eEjnpGc1Jxjmp8RAIfCwTCT3cwS4hs',
    authDomain: 'helloflutter-7c591.firebaseapp.com',
    projectId: 'helloflutter-7c591',
    storageBucket: 'helloflutter-7c591.firebasestorage.app',
    messagingSenderId: '************',
    appId: '1:************:web:604054920062efc741f85d',
    measurementId: 'G-3CNJ1EFZWL',
}
// Initialize Firebase
const app = initializeApp(import.meta.env.VITE_APP_NODE_ENV != 'production' ? firebaseConfigTest : firebaseConfig)

const googleProvider = new GoogleAuthProvider()
const facebooProvider = new FacebookAuthProvider()
const auth = getAuth()

const props = defineProps<{
    dialogVisible: boolean
    dialogAccountVisible: boolean
}>()
const accountFormRef = ref<FormInstance | undefined>()
const accountForm = reactive({
    firstName: '',
    lastName: '',
    username: '',
    latitude: '',
    longitude: '',
    gender: '',
    birthDate: '',
    birthTime: '',
    birthPlace: '',
    birthday: ''
})
const accountFormRules = {
    firstName: [{ required: true, message: 'Please input first name', trigger: 'blur' }],
    lastName: [{ required: true, message: 'Please input last name', trigger: 'blur' }],
    birthDate: [{ required: true, message: 'Please select birth date', trigger: 'change' }],
    birthTime: [{ required: true, message: 'Please input birth time', trigger: 'change' }],
    birthPlace: [{ required: true, message: 'Please select birth place', trigger: 'change' }],
    gender: [{ required: true, message: 'Please select gender', trigger: 'blur' }],
}
const showCalendar = ref(false)
const selectedDate = ref<Date>()
const birthDateRef = ref<HTMLElement | null>(null)
const calendarPosition = ref({ top: 0, left: 0 })

// 时间选择器相关状态
const showTimePicker = ref(false)
const selectedTime = ref<{ hour: number; minute: number } | undefined>(undefined)
const timePickerStyle = ref({})
const birthTimeRef = ref()
const loginMenu = ref([
    // {
    // 	title: 'Continue with Facebook',
    // 	icon: new URL('@/assets/login/facebook.png', import.meta.url).href,
    // 	value: 'facebook',
    // },
    {
        title: 'Continue with Google',
        icon: new URL('@/assets/login/google.png', import.meta.url).href,
        value: 'google',
    },
    // {
    // 	title: 'Continue with Apple',
    // 	icon: new URL('@/assets/login/apple.png', import.meta.url).href,
    // },
    {
        title: 'Continue with Phone',
        icon: new URL('@/assets/login/phone.png', import.meta.url).href,
        value: 'phone',
    },
])

const genderOptions = [
    { label: 'Male', value: '1' },
    { label: 'Female', value: '2' },
    { label: 'Non-binary', value: '3' },
]
// 计算日历样式
const calendarStyle = computed(() => {
    return {
        top: `${calendarPosition.value.top}px`,
        left: `${calendarPosition.value.left}px`,
    }
})

// 计算日历位置
const updateCalendarPosition = () => {
    if (!birthDateRef.value) return

    const rect = birthDateRef.value.getBoundingClientRect()
    const inputHeight = rect.height
    const windowHeight = window.innerHeight

    if (document.documentElement.clientWidth < 1330) {
        // 直接在输入框上方显示
        calendarPosition.value = {
            top: rect.top - 412 + window.scrollY, // 日历的高度
            left: rect.left + window.scrollX,
        }
    } else {
        // 直接在输入框上方显示
        calendarPosition.value = {
            top: rect.top - 448 + window.scrollY, // 日历的高度
            left: rect.left + window.scrollX,
        }
    }
}

// 切换日历显示并计算位置
const toggleCalendar = () => {
    if (!showCalendar.value) {
        // 延迟一下计算位置，确保DOM更新
        nextTick(() => {
            updateCalendarPosition()
        })
    }
    showCalendar.value = !showCalendar.value
}

// 处理日期确认
const handleDateConfirm = (date: Date) => {
    if (date) {
        accountForm.birthDate = date.toLocaleDateString()
    }
    showCalendar.value = false
}

// 处理滚动事件
const handleScroll = () => {
    if (showCalendar.value) {
        updateCalendarPosition()
    }
}

// 监听点击事件，当点击组件外部时关闭日历
const closeCalendarOnClickOutside = (e: MouseEvent) => {
    const target = e.target as HTMLElement
    if (showCalendar.value && birthDateRef.value && !birthDateRef.value.contains(target) && !target.closest('.calendar-dropdown')) {
        showCalendar.value = false
    }
}

// 添加/移除全局点击事件和滚动事件监听器
watch(showCalendar, (val) => {
    if (val) {
        setTimeout(() => {
            document.addEventListener('click', closeCalendarOnClickOutside)
            window.addEventListener('scroll', handleScroll, true)
            window.addEventListener('resize', updateCalendarPosition)
        }, 0)
    } else {
        document.removeEventListener('click', closeCalendarOnClickOutside)
        window.removeEventListener('scroll', handleScroll, true)
        window.removeEventListener('resize', updateCalendarPosition)
    }
})

// 更新时间选择器位置
function updateTimePickerPosition() {
    if (!birthTimeRef.value) return
    const rect = birthTimeRef.value.getBoundingClientRect()
    timePickerStyle.value = {
        left: rect.left + 'px',
        top: rect.bottom + 8 + 'px',
        position: 'fixed',
    }
}
const handleSubmit = async (formRef: FormInstance | undefined) => {
    if (!formRef) return
    const isValid = await formRef.validate()
    if (isValid) {
        console.log('accountForm', accountForm)
        accountForm.username = accountForm.firstName + ' ' + accountForm.lastName
        accountForm.birthday = String(new Date(accountForm.birthDate).getTime() / 1000)
        updateUserInfo(accountForm).then(res => {
            getUserInfo().then((res: any) => {
                useUser.setUserInfo(res.data)
                console.log('res', res)
                emit('update:dialogAccountVisible', false)
                
                // window.location.reload()
            })
        })
    } else {
        console.log('error submit!!')
        return false
    }
}
// 监听时间选择器显示状态变化
watch(showTimePicker, (val) => {
    if (val) {
        setTimeout(() => {
            window.addEventListener('scroll', updateTimePickerPosition, true)
            window.addEventListener('resize', updateTimePickerPosition)
        }, 0)
        nextTick(() => {
            updateTimePickerPosition()
        })
    } else {
        window.removeEventListener('scroll', updateTimePickerPosition, true)
        window.removeEventListener('resize', updateTimePickerPosition)
    }
})

// 切换时间选择器显示状态
function toggleTimePicker() {
    showTimePicker.value = !showTimePicker.value
    nextTick(() => {
        if (showTimePicker.value) {
            updateTimePickerPosition()
        }
    })
}

// 处理时间确认
function handleTimeConfirm(val: { hour: number; minute: number }) {
    accountForm.birthTime = `${val.hour.toString().padStart(2, '0')}:${val.minute.toString().padStart(2, '0')}`
    showTimePicker.value = false
}

const emit = defineEmits(['update:dialogVisible', 'update:dialogAccountVisible', 'loginWithPhone'])

const handleClose = () => {
    emit('update:dialogVisible', false)
}
const handleAccountClose = () => {
    emit('update:dialogAccountVisible', false)
}

/**
 * @description 三方登录
 * @param channel 登录渠道
 */
const handleTherrPartyLogin = (channel: string) => {
    if (channel === 'google') {
        signInWithPopup(auth, googleProvider)
            .then((result) => {
                // This gives you a Google Access Token. You can use it to access the Google API.
                const credential = GoogleAuthProvider.credentialFromResult(result)
                // 用户token
                const token = credential?.accessToken

                // 用户登录后所获得的信息 The signed-in user info.
                const user: any = result.user
                firsebaseLogin({
                    accessToken: user.accessToken as string,
                    appId: '46',
                }).then((response: any) => {
                    console.log(response)
                    useUser.setToken(response.data.token)
                    // localStorage.setItem('token',response.data.token)
                    getUserInfo().then((res: any) => {
                        useUser.setisLogin(true)
                        useUser.setUserInfo({ ...user, ...res.data })
                        console.log('res', res)
                        if (!res.data.birthday || !res.data.birthTime || !res.data.birthPlace) {
                            emit('update:dialogAccountVisible', true)
                        }else{
                            window.location.reload()
                        }
                        
                    })


                    handleClose()
                    ElMessage.success('登录成功')
                })

                // IdP data available using getAdditionalUserInfo(result)
                // ...
            })
            .catch((error) => {
                // Handle Errors here.
                const errorCode = error.code
                const errorMessage = error.message
                // The email of the user's account used.
                const email = error.customData.email
                // The AuthCredential type that was used.
                const credential = GoogleAuthProvider.credentialFromError(error)
                // ...
            })
    } else if (channel === 'facebook') {
        signInWithPopup(auth, facebooProvider)
            .then((result) => {
                // The signed-in user info.
                const user = result.user

                // This gives you a Facebook Access Token. You can use it to access the Facebook API.
                const credential = FacebookAuthProvider.credentialFromResult(result)
                const accessToken = credential?.accessToken

                if (accessToken) {
                    firsebaseLogin({
                        accessToken: accessToken,
                        appId: '46',
                    }).then((response: any) => {
                        console.log(response)
                    })
                }
                // IdP data available using getAdditionalUserInfo(result)
                // ...
            })
            .catch((error) => {
                // Handle Errors here.
                const errorCode = error.code
                const errorMessage = error.message
                // The email of the user's account used.
                const email = error.customData.email
                // The AuthCredential type that was used.
                const credential = FacebookAuthProvider.credentialFromError(error)

                // ...
            })
    } else if (channel == 'phone') {
        emit('loginWithPhone')
    }
}

const handleChangePlace = (data: ChangePlaceData) => {
    accountForm.latitude = data.latitude
    accountForm.longitude = data.longitude
}
// function googleLogin() {
//     if (window.google && window.google.accounts && window.google.accounts.id) {
//         window.google.accounts.id.prompt()
//     } else {
//         alert('Google 登录脚本还未加载，请稍后再试')
//     }
// }

// onMounted(() => {
//     window.google.accounts.id.initialize({
//         client_id: '************-lk82jj93odgr9mrdo60sc6lhkrlqeg80.apps.googleusercontent.com', // 替换成你的
//         callback: (response) => {
//             console.log('Google 登录成功', response)
//         },
//         ux_mode: 'popup',
//     })
// })
</script>
<style lang="scss" scoped>
:deep(.el-dialog) {
    padding: 0;
    display: flex;
    flex-direction: column;

    .el-dialog__header {
        display: none;
    }

    .el-dialog__body {
        flex: 1;
        padding: 0 !important;
    }
}

.login-content {
    height: 100%;

    .login-left {
        background: url('@/assets/login/login-bg.png') no-repeat;
        background-size: cover;
    }

    .login-right {
        background: linear-gradient(180deg, #efefff 0%, #fff8f4 100%);

        .logo-title {
            width: max-content;
            background: linear-gradient(91.29deg, #7aafff 1.1%, #7a87ff 95.36%);
            background-clip: text;
            -webkit-background-clip: text;
            color: transparent;
        }
    }
}

.account-content {
    background: linear-gradient(180deg, #efefff 0%, #fff8f4 100%);

    :deep(.el-form) {
        .el-form-item {
            .el-form-item__label {
                font-size: 16px;
                font-weight: 700;
                font-family: 'Philosopher';
                margin-bottom: 12px;
            }

            .el-form-item__content {

                .el-input__wrapper,
                .el-select__wrapper {
                    height: 48px;
                    font-size: 16px;
                    border-radius: 8px;
                    font-weight: 400;
                    color: #1c2158;
                    font-family: 'Open Sans';
                    background-color: transparent;
                    box-shadow: 0 0 0 1px rgba(28, 33, 88, .5) inset;
                }

                .el-input__inner {
                    color: #1c2158;
                }
            }
        }

        @media screen and (min-width: 1330px) {
            .el-form-item {
                margin-bottom: 24px;

                .el-form-item__label {
                    font-size: 22px;
                    font-weight: 700;
                    font-family: 'Philosopher';
                    margin-bottom: 24px;
                }

                .el-form-item__content {

                    .el-input__wrapper,
                    .el-select__wrapper {
                        height: 62px;
                        font-size: 20px;
                        border-radius: 12px;
                        background-color: transparent;
                        font-weight: 600;
                        color: #1c2158;
                        font-family: 'Open Sans';
                    }
                }
            }
        }
    }

    .submit-button {
        width: 100%;
        background: linear-gradient(91.29deg, #7aafff 1.1%, #7a87ff 95.36%);

        &:disabled {
            background: linear-gradient(91.29deg, #bdc6d2 1.1%, #b4b7cc 95.36%);
        }
    }
}
</style>
