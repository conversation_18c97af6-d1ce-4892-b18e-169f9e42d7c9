<!--  -->
<template>
	<div class="">
		<!--登录-->
		<el-dialog :model-value="dialogPhoneVisible" @update:model-value="$emit('update:dialogPhoneVisible', $event)" :before-close="handleClose" :show-close="false" class="login-dialog flex flex-col w-[347px] h-[500px] rounded-[40px] md:w-[530px] md:h-[598px]">
			<div class="phone-login-content flex flex-col items-center pt-[57px]" @click="showPhoneSelect = false">
				<div class="flex flex-col justify-center items-center lg:items-start lg:ml-4">
					<img src="@/assets/common/logo.png" alt="starMet" class="w-[55px] h-[55px]" />
					<span class="logo-title text-[#1C2158] text-[24px] font-['Philosopher'] md:text-[28px] mt-3 md:mt-[14px] font-bold">StarMet Login</span>
				</div>
				<!--表单类型-->
				<div class="phone-login-content-form w-full flex flex-col mt-[30px] px-[15px] md:px-[55px]">
					<div class="phone-login-content-form-item">
						<div class="phone-login-content-form-item-title text-[16px] font-['Philosopher'] font-bold leading-[18px] text-[#1C2158]">
							<span>Phone</span>
						</div>
						<div class="phone-login-content-form-item-content relative w-full h-[54px] md:h-[64px] flex items-center mt-2 border border-solid border-[rgba(28,33,88,.5)] rounded-[8px] px-[10px]">
							<div class="flex items-center justify-between pr-1 border-r border-solid border-[rgba(28, 33, 88, .5)]" @click.stop="showPhoneSelect = !showPhoneSelect">
								<img src="@/assets/common/national-flag.png" alt="" class="w-[24px] h-[18px]" />
								<div class="text-[17px] font-['Source_San_3'] leading-[21px] text-[#1C2158] w-[60px] ml-1">+86</div>
								<img src="@/assets/common/arrow-down.png" alt="" class="w-[14px] h-[9px] translation duration-500" :class="{ 'rotate-180': showPhoneSelect }" />
							</div>
							<div class="flex-1 pl-[10px] md:pl-[35px]">
								<input type="text" v-model="phoneNumber" class="w-full h-full outline-none text-[16px] md:text-[20px] bg-transparent font-['Source_San_3'] leading-[21px] text-[#1C2158] placeholder:text-[#1C2158] placeholder:opacity-50" placeholder="Phone Number" />
							</div>
							<Transition enter-active-class="transition duration-300 ease-out" enter-from-class="opacity-0 scale-95" enter-to-class="opacity-100 scale-100" leave-active-class="transition duration-300 ease-in" leave-from-class="opacity-100 scale-100" leave-to-class="opacity-0 scale-95">
								<div class="phone-popver-select absolute z-[100] top-[calc(100%+8px)] left-0 w-full bg-white rounded-[10px] pt-[21px] pb-[20px] max-h-[257px]" v-if="showPhoneSelect">
									<div class="phone-popver-select-content max-h-[236px] overflow-y-auto">
										<div class="phone-popver-select-item flex items-center justify-between pr-[20px] pl-[14px] py-[14px] cursor-pointer hover:bg-[rgba(68,132,255,.2)]" v-for="item in 10" :class="item === 1 ? 'bg-[rgba(68,132,255,.2)]' : ''" @click="selectPhone(item)">
											<div class="phone-popver-select-item-title flex items-center">
												<img src="@/assets/common/national-flag.png" alt="" class="w-[20px] h-[20px]" />
												<span class="ml-[10px]">China</span>
											</div>
											<div class="phone-popver-select-item-content text-[#1C2158] text-[16px] font-['Source_San_3'] leading-[20px]">
												<span>86</span>
											</div>
										</div>
									</div>
								</div>
							</Transition>
						</div>
					</div>
					<!--发生验证码-->
					<div class="phone-login-content-form-item mt-[25px]">
						<div class="phone-login-content-form-item-title text-[16px] font-['Philosopher'] font-bold leading-[18px] text-[#1C2158]">
							<span>Code</span>
						</div>
						<!---->
						<div class="phone-login-content-form-item-content relative w-full h-[54px] md:h-[64px] flex box-border items-center mt-2 border border-solid border-[rgba(28,33,88,.5)] rounded-[8px] p-[10px] md:p-[12px]">
							<div class="flex-1">
								<input type="text" v-model="code" class="w-full h-full outline-none text-[16px] md:text-[20px] bg-transparent font-['Source_San_3'] leading-[21px] text-[#1C2158] placeholder:text-[#1C2158] placeholder:opacity-50" placeholder="verification code" />
							</div>
							<button class="send-code w-max h-full bg-[#4484FF] px-[14px] rounded-[6px] text-white text-[14px] md:text-[16px] font-['Source_San_3'] leading-[32px] ml-[20px]" :disabled="!phoneNumber || disabledSend" @click="sendCode">
								{{ sendCodeText }}
							</button>
						</div>
					</div>
					<!--登录按钮-->
					<button class="login-button w-full h-[48px] md:h-[68px] font-['Philosopher'] text-[18px] md:text-[24px] font-bold leading-[20px] text-white rounded-[8px] mt-[30px]" :disabled="!phoneNumber || !code">Login</button>
					<div class="text-[rgba(28,33,88,0.4)] text-[13px] leading-[16px] mt-[11px] md:text-[14px]">By tapping “Verify Phone Number", an SMS may be sent. Message & data rates may apply</div>
				</div>
			</div>
		</el-dialog>
	</div>
</template>

<script lang="ts" setup name="PhoneLoginDialog">
import { watch, ref } from 'vue'

const props = defineProps<{
	isOpen: boolean
}>()
const emit = defineEmits(['update:dialogPhoneVisible'])

const handleClose = () => {
	emit('update:dialogPhoneVisible', false)
}
const showPhoneSelect = ref(false)

const sendCodeText = ref('Send Code')

const phoneNumber = ref('')

const code = ref('')
const dialogPhoneVisible = ref(props.isOpen)
const disabledSend = ref(false)

const selectPhone = (item: number) => {
	console.log('Selected phone item:', item)
	// 这里可以添加选择手机号的逻辑
	showPhoneSelect.value = false
}
const sendCode = () => {
	if (disabledSend.value) return
	let during = 60
	let timer = window.setInterval(() => {
		disabledSend.value = true
		during--
		if (during <= 0) {
			window.clearInterval(timer)
			sendCodeText.value = 'Send Code'
			disabledSend.value = false
		} else {
			sendCodeText.value = `Resend Code(${during < 10 ? '0' + during : during}s)`
		}
	}, 1000)
}
watch(
	() => props.isOpen,
	(newVal) => {
		console.log('newVal', newVal)
		dialogPhoneVisible.value = newVal
	}
)

watch(
	() => dialogPhoneVisible.value,
	(newVal) => {
		emit('update:dialogPhoneVisible', newVal)
	}
)
</script>
<style lang="scss" scoped>
:deep(.el-dialog) {
	padding: 0;
	display: flex;
	flex-direction: column;

	.el-dialog__header {
		display: none;
	}

	.el-dialog__body {
		flex: 1;
		padding: 0 !important;
		background: linear-gradient(180deg, #efefff 0%, #fff8f4 100%);
		border-radius: 20px;
	}
	.phone-popver-select {
		box-shadow: 0px 2px 12px 0px #5f6bd38c;
		&::before {
			content: '';
			position: absolute;
			left: 94px;
			width: 0;
			height: 0;
			top: -8px;
			border-bottom: 8px solid white;
			border-left: 8px solid transparent;
			border-right: 8px solid transparent;
			border-top: none;
			z-index: 1;
		}
	}
	.send-code {
		// font-family: 'Source_San_3';
		font-variant-numeric: tabular-nums;
		&:disabled {
			opacity: 0.5;
		}
	}
	.login-button {
		background: linear-gradient(91.29deg, #7aafff 1.1%, #7a87ff 95.36%);
		&:disabled {
			opacity: 0.5;
		}
	}
}
</style>
