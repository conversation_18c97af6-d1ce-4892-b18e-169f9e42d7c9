<template>
	<el-date-picker v-model="innerValue" type="date" :editable="false" :clearable="false" :format="format" :popper-class="'custom-calendar-popper'" @change="onChange" :teleported="false">
		<template #footer>
			<div class="calendar-footer">
				<el-button size="small" @click="onCancel">Cancel</el-button>
				<el-button size="small" type="primary" @click="onConfirm">Done</el-button>
			</div>
		</template>
	</el-date-picker>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'

const props = defineProps<{
	modelValue: string | Date | null
	format?: string
}>()
const emit = defineEmits(['update:modelValue', 'confirm', 'cancel'])

const innerValue = ref(props.modelValue)
const format = props.format || 'MMM d, yyyy'

watch(
	() => props.modelValue,
	(val) => {
		innerValue.value = val
	}
)

function onChange(val: any) {
	emit('update:modelValue', val)
}
function onConfirm() {
	emit('confirm', innerValue.value)
}
function onCancel() {
	emit('cancel')
}
</script>

<style scoped>
.custom-calendar-popper {
	border-radius: 24px;
	box-shadow: 0 0 0 2px #b3baff22;
	padding: 24px 16px 8px 16px;
	font-family: 'Inter', sans-serif;
	background: #f8f8fb;
	min-width: 340px;
}

.custom-calendar-popper .el-picker-panel__header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	border: none;
	box-shadow: none;
	margin-bottom: 8px;
	font-weight: 700;
	font-size: 18px;
	color: #23235f;
}

.custom-calendar-popper .el-picker-panel__icon-btn {
	background: none;
	border: none;
	color: #23235f;
	font-size: 20px;
	font-weight: 700;
	transition: color 0.2s;
}
.custom-calendar-popper .el-picker-panel__icon-btn:hover {
	color: #4d7cfe;
	background: none;
}

.custom-calendar-popper .el-date-table th {
	font-weight: 700;
	color: #23235f;
	text-transform: uppercase;
	font-size: 15px;
	padding-bottom: 4px;
}
.custom-calendar-popper .el-date-table td {
	font-weight: 600;
	color: #23235f;
	font-size: 16px;
	border-radius: 50%;
	transition: background 0.2s, color 0.2s;
}
.custom-calendar-popper .el-date-table td.current {
	background: #4d7cfe;
	color: #fff;
	border-radius: 50%;
}
.custom-calendar-popper .el-date-table td.available:hover {
	background: #e0e7ff;
	color: #23235f;
}
.custom-calendar-popper .el-date-table td.disabled {
	color: #bdbdbd;
	background: none;
}
</style>
