<!--  -->
<template>
	<div class="order-left w-[200px] h-[410px] py-[5px] flex flex-col justify-between rounded-[30px] bg-[#fff] mr-[20px]">
		<div class="order-left-item relative h-[80px] flex font-['Philosopher'] font-bold text-[20px] leading-[20px] text-[#1C2158] items-center justify-center cursor-pointer" :class="{ 'active text-[#7A87FF]': item.title === props.activeItem }" v-for="item in orderLeftList" :key="item.title" @click="handleClick(item.name)">
			<div class="order-left-item-title">{{ item.title }}</div>
		</div>
	</div>
</template>

<script lang="ts" setup name="OrderLeft">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
const router = useRouter()
const orderLeftList = ref([
	{
		title: 'Profile Edit',
        name: 'ProfileEdit',
	},
	{
		title: 'My Orders',
        name: 'OrderList',
	},
	{
		title: 'My Card',
        name: 'My<PERSON><PERSON>',
	},
	{
		title: 'Follow List',
        name: 'FollowList',
	},
	{
		title: 'FAQ',
        name: 'FAQ',
	},
])
interface Props {
  activeItem: string
 
}
// const activeItem = ref('My Orders')
// const {activeItem} = defineProps<{
// 	activeItem: string
// }>()
const props = withDefaults(defineProps<Props>(), {
	activeItem: 'My Orders',
})
const handleClick = (name: string) => {
	router.push({
		name: name,
	})
}
</script>
<style lang="scss" scoped>
.active {
	&::before {
		content: '';
		position: absolute;
		top: 0;
		bottom: 0;
		left: 0;
		margin: auto;
		width: 4px;
		height: 20px;
		background-color: #4484ff;
	}
}
</style>
