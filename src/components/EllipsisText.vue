<template>
	<div class="ellipsis-text-wrapper">
		<div class="ellipsis-text" :class="{ expanded }" ref="textRef" :style="expanded ? {} : { '-webkit-line-clamp': lineClamp }">
			<slot />
			<span v-if="!expanded && isOverflow" class="read-more" @click.stop="expanded = true">Read more</span>
		</div>
		<span v-if="expanded && isOverflow" class="read-more read-more-collapse" @click="expanded = false">Collapse</span>
	</div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, watch } from 'vue'

const props = defineProps<{
	lineClamp?: number
}>()

const expanded = ref(false)
const isOverflow = ref(false)
const textRef = ref<HTMLElement | null>(null)
const lineClamp = props.lineClamp ?? 2

const checkOverflow = () => {
	if (!textRef.value) return
	if (expanded.value) {
		isOverflow.value = false
		return
	}
	// 计算理论最大高度
	const style = window.getComputedStyle(textRef.value)
	const lineHeight = parseFloat(style.lineHeight)
	const maxHeight = lineHeight * lineClamp
	// 只要内容高度大于最大高度就算溢出
	isOverflow.value = textRef.value.scrollHeight - 1 > maxHeight
}

onMounted(() => {
	nextTick(checkOverflow)
})
watch(expanded, () => {
	nextTick(checkOverflow)
})
watch(
	() => props.lineClamp,
	() => {
		nextTick(checkOverflow)
	}
)
</script>

<style scoped>
.ellipsis-text-wrapper {
	width: 100%;
	position: relative;
}
.ellipsis-text {
	font-size: 15px;
	color: rgba(28, 33, 88, 0.7);
	line-height: 19px;
	overflow: hidden;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
	text-overflow: ellipsis;
	word-break: break-all;
	position: relative;
}
.ellipsis-text.expanded {
	-webkit-line-clamp: unset;
	overflow: visible;
	display: block;
}
.read-more {
	color: #4484FF;
	font-size: 14px;
	cursor: pointer;
	user-select: none;
	position: absolute;
	right: 0;
	bottom: 0;
	background: linear-gradient(to right, rgba(255,255,255,0) 0%, #fff 60%);
	padding-left: 10px;
}
.read-more-collapse {
	position: static;
	margin-left: 8px;
}
</style>
