<template>
	<div class="atom-loading" :class="{ 'atom-loading--overlay': overlay }">
		<div class="atom-loading__container">
			<div class="atom-loading__core"></div>
			<div v-for="n in 8" :key="n" class="atom-loading__electron-wrapper" :style="getOrbitStyle(n)">
				<div class="atom-loading__electron" :style="getElectronStyle(n)"></div>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import type { CSSProperties } from 'vue'
const { overlay } = defineProps<{ overlay?: boolean }>()
const electronCount = 8
const baseRadius = 22 // 轨道半径
const getOrbitStyle = (n: number): CSSProperties => {
	const angle = (360 / electronCount) * (n - 1)
	return {
		position: 'absolute',
		top: '50%',
		left: '50%',
		width: '0px',
		height: '0px',
		transform: `rotate(${angle}deg)`
	}
}
const getElectronStyle = (n: number): CSSProperties => {
	const delay = (n - 1) * 0.12
	return {
		position: 'absolute',
		left: `${baseRadius + (n % 2 === 0 ? 6 : 0)}px`,
		top: '-6px',
		width: '12px',
		height: '12px',
		background: '#fff',
		borderRadius: '50%',
		boxShadow: '0 0 8px #fff',
		animation: `atom-electron-spin 1.2s linear infinite, atom-electron-pulse 0.8s ease-in-out infinite`,
		animationDelay: `${delay}s, ${delay}s`
	}
}
</script>

<style lang="scss" scoped>
.atom-loading {
	display: inline-flex;
	align-items: center;
	justify-content: center;
	
	&--overlay {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.5);
		backdrop-filter: blur(2px);
		z-index: 1000;
	}

	&__container {
		position: relative;
		width: 60px;
		height: 60px;
	}

	&__core {
		position: absolute;
		top: 50%;
		left: 50%;
		width: 16px;
		height: 16px;
		margin: -8px 0 0 -8px;
		background: radial-gradient(circle at center, #fff 0%, #432b62 80%, transparent 100%);
		border-radius: 50%;
		box-shadow: 0 0 12px #fff, 0 0 24px #432b62;
		animation: atom-core 1.5s infinite;
	}

	&__electron-wrapper {
		/* 由js控制角度和半径 */
	}

	&__electron {
		/* 由js控制位置和动画 */
	}
}

@keyframes atom-core {
	0% {
		transform: scale(1);
		opacity: 1;
	}
	50% {
		transform: scale(1.15);
		opacity: 0.7;
	}
	100% {
		transform: scale(1);
		opacity: 1;
	}
}

@keyframes atom-electron-spin {
	0% {
		filter: brightness(1.2);
	}
	100% {
		filter: brightness(1.2);
	}
}

@keyframes atom-electron-pulse {
	0% {
		transform: scale(1);
		opacity: 1;
	}
	50% {
		transform: scale(1.3);
		opacity: 0.7;
	}
	100% {
		transform: scale(1);
		opacity: 1;
	}
}
</style> 