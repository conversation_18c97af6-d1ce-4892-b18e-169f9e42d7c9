import { type App, type Directive, createApp } from 'vue'
import Loading from './Loading.vue'

const loadingDirective: Directive = {
    mounted(el, binding) {
        
        const app = createApp(Loading, {
            loading: binding.value,
            // borderRadius: binding.arg,
            background:el.getAttribute('loading-background') == 'false' ? 'transparent' : 'rgba(0, 0, 0, 0.3)',
            borderRadius:el.getAttribute('loading-border-radius')
        })
        const instance = app.mount(document.createElement('div'))
        el.instance = instance
        el.style.position = 'relative'
        el.appendChild(instance.$el)
    },
    updated(el, binding) {
        el.instance.loading = binding.value
        if (binding.modifiers.background) {
            el.instance.background = binding.value?.background
        }
    },
    unmounted(el) {
        el.instance.$el.remove()
        el.instance = null
    }
}

export function setupLoadingDirective(app: App) {
    app.directive('loading-mask', loadingDirective)
} 