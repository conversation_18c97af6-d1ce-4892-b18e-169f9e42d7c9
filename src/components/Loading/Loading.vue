<template>
    <div class="loading-overlay" v-show="loadingState" :style="{ backgroundColor: background,borderRadius:borderRadius }">
        <!-- <img src="@/assets/Common/loading.png" class="loading-icon" alt="loading" /> -->

        <div class="loading-spinner">
            <div class="loading-spinner-inner">
                <div class="loading-spinner-circle"></div>
                <div class="loading-spinner-circle"></div>
                <div class="loading-spinner-circle"></div>
                <div class="loading-spinner-circle"></div>
            </div>
        </div>
        <div class="loading-text" v-if="text">{{ text }}</div>
    </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';

defineOptions({
    name: 'AppLoading'
});

const props = defineProps<{
    loading: boolean;
    text?: string;
    background?: string;
    borderRadius?: string;
}>();

const loadingState = ref(props.loading);

watch(() => props.loading, (newVal) => {
    loadingState.value = newVal;
});

defineExpose({
    loading: loadingState,
    text: props.text
});

</script>

<style scoped>
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.3);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    /* border-radius: 32px; */
}

.loading-spinner {
    width: 40px;
    height: 40px;
    position: relative;
}
.loading-icon{
    width: 40px;
    height: 40px;
    animation: loading-icon 1.2s infinite ease-in-out;
}
.loading-spinner-inner {
    width: 100%;
    height: 100%;
    position: relative;
}

.loading-spinner-circle {
    position: absolute;
    width: 8px;
    height: 8px;
    background-color: #fff;
    border-radius: 50%;
    animation: loading-spinner 1.2s infinite ease-in-out;
}

.loading-spinner-circle:nth-child(1) {
    top: 0;
    left: 50%;
    margin-inline-start: -4px;
    animation-delay: -0.32s;
}

.loading-spinner-circle:nth-child(2) {
    top: 50%;
    right: 0;
    margin-top: -4px;
    animation-delay: -0.16s;
}

.loading-spinner-circle:nth-child(3) {
    bottom: 0;
    left: 50%;
    margin-inline-start: -4px;
}

.loading-spinner-circle:nth-child(4) {
    top: 50%;
    left: 0;
    margin-top: -4px;
}

.loading-text {
    margin-top: 10px;
    color: #fff;
    font-size: 14px;
}

@keyframes loading-icon {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}
@keyframes loading-spinner {
    0%, 80%, 100% {
        transform: scale(0);
    }
    40% {
        transform: scale(1);
    }
}
</style> 