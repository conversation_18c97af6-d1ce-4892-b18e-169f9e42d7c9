<template>
  <div class="bg-white rounded-2xl shadow-lg w-[260px] p-4">
    <div class="flex justify-between">
      <!-- 小时 -->
      <div class="flex-1 mr-2">
        <div class="text-xs text-gray-400 mb-2 text-center">Hour</div>
        <div class="h-[180px] overflow-y-auto">
          <div
            v-for="h in 24"
            :key="h"
            class="h-[32px] flex items-center justify-center rounded cursor-pointer mb-1"
            :class="h-1 === hour ? 'bg-[#4484FF] text-white font-bold' : 'hover:bg-indigo-50 text-[#1C2158]'"
            @click="selectHour(h-1)"
          >
            {{ pad(h-1) }}
          </div>
        </div>
      </div>
      <!-- 分钟 -->
      <div class="flex-1 ml-2">
        <div class="text-xs text-gray-400 mb-2 text-center">Minute</div>
        <div class="h-[180px] overflow-y-auto">
          <div
            v-for="m in 60"
            :key="m"
            class="h-[32px] flex items-center justify-center rounded cursor-pointer mb-1"
            :class="m-1 === minute ? 'bg-[#4484FF] text-white font-bold' : 'hover:bg-indigo-50 text-[#1C2158]'"
            @click="selectMinute(m-1)"
          >
            {{ pad(m-1) }}
          </div>
        </div>
      </div>
    </div>
    <!-- 底部按钮 -->
    <div class="flex justify-end items-center mt-4">
      <button class="text-md font-bold text-[#1C2158] px-4 py-1 rounded-lg" @click="onCancel">Cancel</button>
      <button class="text-md font-bold text-[#4484FF] px-4 py-1 rounded-lg ml-2" @click="onDone">Done</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const props = defineProps<{
  modelValue?: { hour: number, minute: number }
}>()
const emit = defineEmits(['update:modelValue', 'cancel', 'confirm'])

const hour = ref(props.modelValue?.hour ?? 0)
const minute = ref(props.modelValue?.minute ?? 0)

function pad(n: number) {
  return n < 10 ? '0' + n : n
}
function selectHour(h: number) {
  hour.value = h
}
function selectMinute(m: number) {
  minute.value = m
}
function onCancel() {
  emit('cancel')
}
function onDone() {
  emit('confirm', { hour: hour.value, minute: minute.value })
  emit('update:modelValue', { hour: hour.value, minute: minute.value })
}
</script> 