import { createI18n } from 'vue-i18n'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import en from 'element-plus/dist/locale/en.mjs'

const i18n = createI18n({
    legacy: false,
    locale: 'en',
    messages: {
        'zh': {
            message: {
                hello: '你好'
            },
            element: zhCn
        },
        'en': {
            message: {
                hello: 'hello'
            },
            element: en
        }
    }
})

export default i18n