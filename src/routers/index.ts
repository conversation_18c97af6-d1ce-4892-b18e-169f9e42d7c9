import { createRouter, createWebHistory } from 'vue-router';
import type { RouteRecordRaw } from 'vue-router';
import layout from '@/layout/layout.vue';
import { restoreScroll } from '@/utils/scroll';
import { nextTick } from 'vue';

const routes: Array<RouteRecordRaw> = [
    {
        path: '',
        redirect: '/home'
    },
    {
        path: '',
        component: layout,
        children: [
            {
                path: 'home',
                name: 'Home',
                component: () => import('@/views/home/<USER>'),
            },
            {
                path: '/familyfriends',
                name: 'Family&Friends',
                component: () => import('@/views/familyFriends/index.vue'),
            },
            {
                path: '/freeBonus',
                name: 'FreeBonus',
                component: () => import('@/views/freeBonus/index.vue'),
            },
            {
                path: '/createOrder',
                name: 'CreateOrder',
                component: () => import('@/views/order/createOrder.vue'),
            },
            {
                path: '/freeBonus',
                name: 'FreeBonus',
                component: () => import('@/views/freeBonus/index.vue'),
            },
            {
                path: '/message',
                name: 'Message',
                component: () => import('@/views/message/index.vue'),
            },
            {
                path: '/chatToAdvisor',
                name: 'ChatToAdvisor',
                component: () => import('@/views/message/chatToAdvisor.vue'),
            },
            {
                path: '/publicReading',
                name: 'PublicReading',
                component: () => import('@/views/publicReading/index.vue'),
            },
            {
                path: '/submitPublicReading',
                name: 'SubmitPublicReading',
                component: () => import('@/views/publicReading/submitPublicReading.vue'),
            },
            {
                path: '/onlineOrder',
                name: 'OnlineOrder',
                component: () => import('@/views/order/onlineOrder.vue'),
            },
            {
                path: '/orderList',
                name: 'OrderList',
                component: () => import('@/views/order/orderList.vue'),
            },
            {
                path: '/orderDetail/:orderNo',
                name: 'OrderDetail',
                component: () => import('@/views/order/orderDetail.vue'),
            },
            {
                path: '/offlinePlaceOrder',
                name: 'OfflinePlaceOrder',
                component: () => import('@/views/order/offlinePlaceOrder.vue'),
            },
            {
                path: '/profileEdit',
                name: 'ProfileEdit',
                component: () => import('@/views/user/profileEdit.vue'),
            },
            {
                path: '/myCard',
                name: 'MyCard',
                component: () => import('@/views/user/myCard.vue'),
            },
            {
                path: '/followList',
                name: 'FollowList',
                component: () => import('@/views/user/followList.vue'),
            },
            {
                path: '/faq',
                name: 'FAQ',
                component: () => import('@/views/user/faq.vue'),
            },
            {
                path: '/cardPurchase',
                name: 'CardPurchase',
                component: () => import('@/views/order/cardPurchase.vue'),
            },
            {
                path: '/advisor/:type',
                name: 'Advisor',
                component: () => import('@/views/advisor/index.vue'),
            },
            {
                path: 'searchAdvisor',
                name: 'searchAdvisor',
                component: () => import('@/views/advisor/searchAdvisor.vue'),
            },
            // {
            //     path: '/profileEdit',
            //     name: 'ProfileEdit',
            //     component: () => import('@/views/user/profileEdit.vue'),
            // },
        ],
    },

    {
        path: '/detail',
        name: 'Detail',
        component: () => import('@/views/detail/index.vue'),
    },
];

const router = createRouter({
    history: createWebHistory(),
    routes,
    scrollBehavior(to, from, savedPosition) {
        // 如果是通过浏览器的前进后退按钮导航，则恢复到保存的位置
        if (savedPosition) {
            return savedPosition
        } else {
            return { top: 0 }
        }
    }
});

// 全局路由守卫，确保在路由切换时恢复滚动状态
router.beforeEach((to, from, next) => {
    // 确保滚动状态被恢复
    // restoreScroll()
    next()
})

// 全局后置守卫，确保路由切换完成后滚动到正确位置
router.afterEach((to, from) => {
    // 如果路由没有锚点，确保滚动到顶部
    if (!to.hash) {
        // 使用 nextTick 确保 DOM 更新完成后再滚动
        nextTick(() => {
            document.body.scrollTo({
                top: 0,
                // behavior: 'smooth'
            })
        })
    }
})

export default router;