/* Source Sans 3 */
@font-face {
  font-family: 'Source Sans 3';
  src: url('./font/Source_Sans_3/SourceSans3-Regular.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: 'Source Sans 3';
  src: url('./font/Source_Sans_3/SourceSans3-Italic.ttf') format('truetype');
  font-weight: 400;
  font-style: italic;
}

@font-face {
  font-family: 'Source Sans 3';
  src: url('./font/Source_Sans_3/SourceSans3-Light.ttf') format('truetype');
  font-weight: 300;
  font-style: normal;
}

@font-face {
  font-family: 'Source Sans 3';
  src: url('./font/Source_Sans_3/SourceSans3-LightItalic.ttf') format('truetype');
  font-weight: 300;
  font-style: italic;
}

@font-face {
  font-family: 'Source Sans 3';
  src: url('./font/Source_Sans_3/SourceSans3-Medium.ttf') format('truetype');
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: 'Source Sans 3';
  src: url('./font/Source_Sans_3/SourceSans3-MediumItalic.ttf') format('truetype');
  font-weight: 500;
  font-style: italic;
}

@font-face {
  font-family: 'Source Sans 3';
  src: url('./font/Source_Sans_3/SourceSans3-SemiBold.ttf') format('truetype');
  font-weight: 600;
  font-style: normal;
}

@font-face {
  font-family: 'Source Sans 3';
  src: url('./font/Source_Sans_3/SourceSans3-SemiBoldItalic.ttf') format('truetype');
  font-weight: 600;
  font-style: italic;
}

@font-face {
  font-family: 'Source Sans 3';
  src: url('./font/Source_Sans_3/SourceSans3-Bold.ttf') format('truetype');
  font-weight: 700;
  font-style: normal;
}

@font-face {
  font-family: 'Source Sans 3';
  src: url('./font/Source_Sans_3/SourceSans3-BoldItalic.ttf') format('truetype');
  font-weight: 700;
  font-style: italic;
}

@font-face {
  font-family: 'Source Sans 3';
  src: url('./font/Source_Sans_3/SourceSans3-ExtraBold.ttf') format('truetype');
  font-weight: 800;
  font-style: normal;
}

@font-face {
  font-family: 'Source Sans 3';
  src: url('./font/Source_Sans_3/SourceSans3-ExtraBoldItalic.ttf') format('truetype');
  font-weight: 800;
  font-style: italic;
}

@font-face {
  font-family: 'Source Sans 3';
  src: url('./font/Source_Sans_3/SourceSans3-Black.ttf') format('truetype');
  font-weight: 900;
  font-style: normal;
}

@font-face {
  font-family: 'Source Sans 3';
  src: url('./font/Source_Sans_3/SourceSans3-BlackItalic.ttf') format('truetype');
  font-weight: 900;
  font-style: italic;
}

/* Philosopher */
@font-face {
  font-family: 'Philosopher';
  src: url('./font/Philosopher/Philosopher-Regular.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: 'Philosopher';
  src: url('./font/Philosopher/Philosopher-Italic.ttf') format('truetype');
  font-weight: 400;
  font-style: italic;
}

@font-face {
  font-family: 'Philosopher';
  src: url('./font/Philosopher/Philosopher-Bold.ttf') format('truetype');
  font-weight: 700;
  font-style: normal;
}

@font-face {
  font-family: 'Philosopher';
  src: url('./font/Philosopher/Philosopher-BoldItalic.ttf') format('truetype');
  font-weight: 700;
  font-style: italic;
}

/* Open Sans */
@font-face {
  font-family: 'Open_Sans';
  src: url('./font/Open_Sans/OpenSans-Regular.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: 'Open_Sans';
  src: url('./font/Open_Sans/OpenSans-Italic.ttf') format('truetype');
  font-weight: 400;
  font-style: italic;
}

@font-face {
  font-family: 'Open_Sans';
  src: url('./font/Open_Sans/OpenSans-Light.ttf') format('truetype');
  font-weight: 300;
  font-style: normal;
}

@font-face {
  font-family: 'Open_Sans';
  src: url('./font/Open_Sans/OpenSans-LightItalic.ttf') format('truetype');
  font-weight: 300;
  font-style: italic;
}

@font-face {
  font-family: 'Open_Sans';
  src: url('./font/Open_Sans/OpenSans-Medium.ttf') format('truetype');
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: 'Open_Sans';
  src: url('./font/Open_Sans/OpenSans-MediumItalic.ttf') format('truetype');
  font-weight: 500;
  font-style: italic;
}

@font-face {
  font-family: 'Open_Sans';
  src: url('./font/Open_Sans/OpenSans-SemiBold.ttf') format('truetype');
  font-weight: 600;
  font-style: normal;
}

@font-face {
  font-family: 'Open_Sans';
  src: url('./font/Open_Sans/OpenSans-SemiBoldItalic.ttf') format('truetype');
  font-weight: 600;
  font-style: italic;
}

@font-face {
  font-family: 'Open_Sans';
  src: url('./font/Open_Sans/OpenSans-Bold.ttf') format('truetype');
  font-weight: 700;
  font-style: normal;
}

@font-face {
  font-family: 'Open_Sans';
  src: url('./font/Open_Sans/OpenSans-BoldItalic.ttf') format('truetype');
  font-weight: 700;
  font-style: italic;
}

@font-face {
  font-family: 'Open_Sans';
  src: url('./font/Open_Sans/OpenSans-ExtraBold.ttf') format('truetype');
  font-weight: 800;
  font-style: normal;
}

@font-face {
  font-family: 'Open_Sans';
  src: url('./font/Open_Sans/OpenSans-ExtraBoldItalic.ttf') format('truetype');
  font-weight: 800;
  font-style: italic;
}

/* Open Sans Condensed */
@font-face {
  font-family: 'Open Sans Condensed';
  src: url('./font/Open_Sans/OpenSans_Condensed-Regular.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: 'Open Sans Condensed';
  src: url('./font/Open_Sans/OpenSans_Condensed-Italic.ttf') format('truetype');
  font-weight: 400;
  font-style: italic;
}

@font-face {
  font-family: 'Open Sans Condensed';
  src: url('./font/Open_Sans/OpenSans_Condensed-Light.ttf') format('truetype');
  font-weight: 300;
  font-style: normal;
}

@font-face {
  font-family: 'Open Sans Condensed';
  src: url('./font/Open_Sans/OpenSans_Condensed-LightItalic.ttf') format('truetype');
  font-weight: 300;
  font-style: italic;
}

@font-face {
  font-family: 'Open Sans Condensed';
  src: url('./font/Open_Sans/OpenSans_Condensed-Medium.ttf') format('truetype');
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: 'Open Sans Condensed';
  src: url('./font/Open_Sans/OpenSans_Condensed-MediumItalic.ttf') format('truetype');
  font-weight: 500;
  font-style: italic;
}

@font-face {
  font-family: 'Open Sans Condensed';
  src: url('./font/Open_Sans/OpenSans_Condensed-SemiBold.ttf') format('truetype');
  font-weight: 600;
  font-style: normal;
}

@font-face {
  font-family: 'Open Sans Condensed';
  src: url('./font/Open_Sans/OpenSans_Condensed-SemiBoldItalic.ttf') format('truetype');
  font-weight: 600;
  font-style: italic;
}

@font-face {
  font-family: 'Open Sans Condensed';
  src: url('./font/Open_Sans/OpenSans_Condensed-Bold.ttf') format('truetype');
  font-weight: 700;
  font-style: normal;
}

@font-face {
  font-family: 'Open Sans Condensed';
  src: url('./font/Open_Sans/OpenSans_Condensed-BoldItalic.ttf') format('truetype');
  font-weight: 700;
  font-style: italic;
}

@font-face {
  font-family: 'Open Sans Condensed';
  src: url('./font/Open_Sans/OpenSans_Condensed-ExtraBold.ttf') format('truetype');
  font-weight: 800;
  font-style: normal;
}

@font-face {
  font-family: 'Open Sans Condensed';
  src: url('./font/Open_Sans/OpenSans_Condensed-ExtraBoldItalic.ttf') format('truetype');
  font-weight: 800;
  font-style: italic;
}

/* Open Sans SemiCondensed */
@font-face {
  font-family: 'Open Sans SemiCondensed';
  src: url('./font/Open_Sans/OpenSans_SemiCondensed-Regular.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: 'Open Sans SemiCondensed';
  src: url('./font/Open_Sans/OpenSans_SemiCondensed-Italic.ttf') format('truetype');
  font-weight: 400;
  font-style: italic;
}

@font-face {
  font-family: 'Open Sans SemiCondensed';
  src: url('./font/Open_Sans/OpenSans_SemiCondensed-Light.ttf') format('truetype');
  font-weight: 300;
  font-style: normal;
}

@font-face {
  font-family: 'Open Sans SemiCondensed';
  src: url('./font/Open_Sans/OpenSans_SemiCondensed-LightItalic.ttf') format('truetype');
  font-weight: 300;
  font-style: italic;
}

@font-face {
  font-family: 'Open Sans SemiCondensed';
  src: url('./font/Open_Sans/OpenSans_SemiCondensed-Medium.ttf') format('truetype');
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: 'Open Sans SemiCondensed';
  src: url('./font/Open_Sans/OpenSans_SemiCondensed-MediumItalic.ttf') format('truetype');
  font-weight: 500;
  font-style: italic;
}

@font-face {
  font-family: 'Open Sans SemiCondensed';
  src: url('./font/Open_Sans/OpenSans_SemiCondensed-SemiBold.ttf') format('truetype');
  font-weight: 600;
  font-style: normal;
}

@font-face {
  font-family: 'Open Sans SemiCondensed';
  src: url('./font/Open_Sans/OpenSans_SemiCondensed-SemiBoldItalic.ttf') format('truetype');
  font-weight: 600;
  font-style: italic;
}

@font-face {
  font-family: 'Open Sans SemiCondensed';
  src: url('./font/Open_Sans/OpenSans_SemiCondensed-Bold.ttf') format('truetype');
  font-weight: 700;
  font-style: normal;
}

@font-face {
  font-family: 'Open Sans SemiCondensed';
  src: url('./font/Open_Sans/OpenSans_SemiCondensed-BoldItalic.ttf') format('truetype');
  font-weight: 700;
  font-style: italic;
}

@font-face {
  font-family: 'Open Sans SemiCondensed';
  src: url('./font/Open_Sans/OpenSans_SemiCondensed-ExtraBold.ttf') format('truetype');
  font-weight: 800;
  font-style: normal;
}

@font-face {
  font-family: 'Open Sans SemiCondensed';
  src: url('./font/Open_Sans/OpenSans_SemiCondensed-ExtraBoldItalic.ttf') format('truetype');
  font-weight: 800;
  font-style: italic;
} 
/* TTChocolates */
@font-face {
  font-family: 'TTChocolates';
  src: url('./font/TTChocolatesTrial.otf') format('opentype');
}
/* Lora */
@font-face {
  font-family: 'Lora';
  src: url('./font/Lora-Medium-5.ttf') format('truetype');
}