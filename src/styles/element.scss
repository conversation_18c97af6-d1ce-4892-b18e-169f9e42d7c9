/* 头像弹窗 */
.avator-popover {
	width: 300px !important;
	height: 414px;
	background: url('@/assets/user/avator-sm.png') no-repeat !important;
	background-size: cover !important;
	background-position: top !important;
	padding: 26px 0px 0 !important;
	border: none !important;
	// box-shadow: none !important;
    border-radius: 20px;
    box-shadow: 0px 20px 20px 0px rgba(95, 107, 211, 0.2) !important;
	@media screen and (min-width: 1330px) {
		width: 396px !important;
		height: 563px !important;
		background: url('@/assets/user/avator-lg.png') no-repeat !important;
		background-size: cover !important;
		background-position: top !important;
		padding: 36px 0px 0 !important;
		border: none !important;
		border-radius: 20px;
		box-shadow: 0px 20px 20px 0px rgba(95, 107, 211, 0.2) !important;
	}
	.avator-popover-content {
		display: flex;
		flex-direction: column;
		align-items: center;
	}
	.el-popover__title {
		display: none;
	}
}
.sm-search-remote-popper{
    // display: block!important;
    box-shadow: 0px 4px 8px 0px rgba(95, 107, 211, 0.19);
    max-width:360px;
    max-height: 320px;
    overflow-y: auto;
    overflow-x: hidden;
    .el-select-dropdown__list{
        .el-select-dropdown__item{
            padding: 16px 20px 16px 56px;
            height:auto;
            position: relative;
            font-size: 18px;
            color: #1C2158;
            &.is-selected{
                 background: rgba(68, 132, 255, .25);
                 &::before{
                    content:'';
                    position: absolute;
                    left: 16px;
                    top: 50%;
                    transform: translateY(-50%);
                    width: 24px;
                    height: 24px;
                    background: url('@/assets/common/select.png') no-repeat center center;
                    background-size: contain;
                 }
            }
        }
    }
    .el-popper__arrow{
        display: none;
    }
    
}
