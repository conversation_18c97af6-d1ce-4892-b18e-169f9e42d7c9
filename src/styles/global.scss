::-webkit-scrollbar {
	width: 0;
	height: 0;
}

// Hide scrollbar for IE, Edge and Firefox
* {
	-ms-overflow-style: none; /* IE and Edge */
	scrollbar-width: none !important; /* Firefox */
	-webkit-overflow-scrolling: touch;
}

// 定义基础颜色变量
$text-color: #1c2158;

// 定义透明度 mixin
@mixin text-opacity($opacity) {
	color: $text-color;
	opacity: $opacity;
}

// 生成所有透明度类（从10到100，步长为10）
@for $i from 1 through 10 {
	$opacity: $i * 10;
	.text-normal-opacity-#{$opacity} {
		@include text-opacity(calc($opacity / 100));
	}
}

// 基础文本样式
.text-normal {
	color: $text-color;
}
.el-breadcrumb{
    display: flex;
    flex-wrap: nowrap;
    overflow-x: auto;
    .el-breadcrumb__item {
        flex-shrink: 0;
    }
}
.el-breadcrumb__item {
    &:last-child{
        opacity: .6;
    }
}
.el-breadcrumb__inner {
    color: #1c2158;
    font-family: 'Philosopher';
    
}


