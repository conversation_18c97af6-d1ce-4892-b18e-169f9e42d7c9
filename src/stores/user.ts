import { defineStore } from 'pinia'

export const useUserStore = defineStore('user', {
    state: () => ({
        token: localStorage.getItem('token') || '',
        userInfo: localStorage.getItem('userInfo') ? JSON.parse(localStorage.getItem('userInfo') as string) : {

        },
        userID: localStorage.getItem('userID')||'',
        userSig: localStorage.getItem('userSig')||'',
        username: '',
        password: '',
        isLogin: localStorage.getItem('isLogin') === 'true' ? true : false,
        showFree: localStorage.getItem('userInfo') ? JSON.parse(localStorage.getItem('userInfo') as string).hasExperienceCard == true : false,
        showCustomerSupport: false,
        deviceId: '',
        dialogPhoneVisible: false,
        orderList: 0,
    }),
    actions: {
        // 设置设备ID
        setDeviceId(id: string) {
            this.deviceId = id
            localStorage.deviceId = id
        },
        // 设置token
        setToken(token: string) {
            this.token = token
            localStorage.token = token
        },
        // 设置用户名
        setShowFree(showFree: boolean) {
            this.showFree = showFree
        },

        setUserInfo(userInfo: any) {
            this.userInfo = userInfo
            this.setShowFree(userInfo.hasExperienceCard ? true : false)
            localStorage.setItem('userInfo', JSON.stringify(userInfo))
            localStorage.setItem('userID',userInfo.uid)
        },
        setShowCustomerSupport(showCustomerSupport: boolean) {
            this.showCustomerSupport = showCustomerSupport
        },
        setDialogPhoneVisible(dialogPhoneVisible: boolean) {
            this.dialogPhoneVisible = dialogPhoneVisible
        },
        setisLogin(isLogin: boolean) {
            this.isLogin = isLogin
            localStorage.setItem('isLogin', isLogin.toString())
        },
        loginOut(){
            this.setToken('')
            this.setisLogin(false)
            this.setUserInfo({})
            localStorage.removeItem('userID')
            localStorage.removeItem('userSig')
        },
        setOrderList(orderList: number) {
            this.orderList = orderList
        },
        setUserSig(userSig: string) {
            this.userSig = userSig
            localStorage.setItem('userSig',userSig)
        }
    },
    getters: {
        getDeviceId: (state) => state.deviceId ? state.deviceId : localStorage.deviceId ? localStorage.deviceId : new DeviceUUID().get()
    }
})
