import { defineStore } from 'pinia'

export const useThemeStore = defineStore('theme', {
    state: () => ({
        isDark: localStorage.getItem('theme') === 'dark' 
            ? true 
            : window.matchMedia('(prefers-color-scheme: dark)').matches
    }),
    actions: {
        toggleTheme() {
            this.isDark = !this.isDark
            // 保存主题设置到 localStorage
            localStorage.setItem('theme', this.isDark ? 'dark' : 'light')
            this.applyTheme()
        },
        applyTheme() {
            document.documentElement.classList.toggle('dark', this.isDark)
        },
        init() {
            this.applyTheme()
            // 监听系统主题变化
            window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
                // 只有当用户没有手动设置过主题时，才跟随系统主题
                if (!localStorage.getItem('theme')) {
                    this.isDark = e.matches
                    this.applyTheme()
                }
            })
        }
    }
})
