// im.ts
import { defineStore } from "pinia";
import { ref, computed, watch } from "vue";
import TIM from "@tencentcloud/chat";
import { ElMessage } from 'element-plus';
import timService from "@/services/timService";
import { eventemitter } from "@/utils";
// 从TIM SDK中获取准确的类型 (如果可用)
// 使用 any 是因为某些复杂类型在SDK中未明确导出

// IM 信令的三个状态
// 3. 接受
// 4. 拒绝
// 5. 超时
type Conversation = any;
type Message = any;
type SignalingInvitation = any; // 为信令邀请定义一个类型

export const useIMStore = defineStore("im", () => {
    // --- State ---
    const isSDKReady = ref(false);
    const isLogin = ref(false);
    const conversationList = ref<Conversation[]>([]);
    const messageList = ref<Record<string, Message[]>>({});
    const currentConversationID = ref<string | null>(null);
    const userInfo = ref<{ userID: string; userSig: string } | null>(null);
    const isAdvisorInRoom = ref(false);
    const advisorUserID = ref<string | null>(null);
    const activeInvitation = ref<SignalingInvitation | null>(null); // 存储当前活动的邀请
    const targetGroupId = ref<string | null>(null); // 等待被拉入的目标群组ID

    // --- Getters ---
    const currentMessages = computed(() => {
        if (!currentConversationID.value) return [];
        return messageList.value[currentConversationID.value] || [];
    });

    // --- Watchers ---
    watch([currentConversationID, isSDKReady], ([convID, sdkReady]) => {
        // 当会话ID存在且SDK准备就绪时，将该会话标记为已读
        if (convID && sdkReady) {
            timService.setMessageRead(convID).catch(err => {
                console.error('Failed to set message as read:', err);
            });
        }
    }, { immediate: true });

    watch(conversationList, (newList) => {
        if (!targetGroupId.value) return;

        const expectedConvID = `GROUP${targetGroupId.value}`;
        const foundConversation = newList.find(conv => conv.conversationID === expectedConvID);

        if (foundConversation) {
            console.log(`[IM] Detected being added to target group: ${targetGroupId.value}. Emitting event.`);
            eventemitter.emit('group-joined', { conversationID: foundConversation.conversationID });
            targetGroupId.value = null; // Reset after finding to prevent re-triggering
        }
    }, { deep: true });

    // --- Actions ---

    const handleSDKReady = () => {
        isSDKReady.value = true;
        console.log("IM SDK is ready.");
    };

    const handleMessageReceived = (event: { data: Message[] }) => {
        const messages = event.data;
        const regularMessages: Message[] = [];
        console.log('messages', messages)

        messages.forEach((message: Message) => {
            // 拦截并处理 App 自定义的信令消息
            if (message.type === TIM.TYPES.MSG_CUSTOM && message.payload.data) {
                try {
                    const appPayload = JSON.parse(message.payload.data);
                    console.log('[IM] Parsed App Custom Payload:', appPayload);

                    // 使用 App 端最终确认的 actionType
                    // 4 代表 "拒绝"
                    if (appPayload.actionType === 4) {
                        console.log('[IM] Received App-defined Signal: REJECT', message);
                        ElMessage.warning('The advisor has declined your invitation.');
                        eventemitter.emit('invitation-status-update', { status: 'rejected', payload: appPayload });
                        return; // 拦截控制消息
                    }

                    // 3 代表 "接受"
                    if (appPayload.actionType === 3) {
                        console.log('[IM] Received App-defined Signal: ACCEPT', message);
                        ElMessage.success('The advisor has accepted your invitation. Waiting to be added to the group...');
                        eventemitter.emit('invitation-status-update', { status: 'accepted', payload: appPayload });
                        return; // 拦截控制消息
                    }

                    // 5 代表 "超时"
                    if (appPayload.actionType === 5) {
                        console.log('[IM] Received App-defined Signal: TIMEOUT', message);
                        ElMessage.error('The invitation to the advisor has timed out.');
                        eventemitter.emit('invitation-status-update', { status: 'timeout', payload: appPayload });
                        return; // 拦截控制消息
                    }

                } catch (e) {
                    console.warn('[IM] Could not parse app custom payload, or it is not JSON:', e);
                }
            }
            // 检查是否是群组notification系统通知
            if (message.type === TIM.TYPES.MSG_GRP_TIP) {
                const { operationType, memberList } = message.payload;
                if (operationType === TIM.TYPES.GRP_TIP_MBR_JOIN) {
                    if (Array.isArray(memberList)) {
                        console.log(`[IM] Members joined: ${memberList.map((m: any) => m.userID).join(', ')}`);
                        if (advisorUserID.value && memberList.some((member: any) => member.userID === advisorUserID.value)) {
                            isAdvisorInRoom.value = true;
                            console.log(`[IM] Advisor (${advisorUserID.value}) has joined the room.`);
                        }
                    }
                } else if (operationType === TIM.TYPES.GRP_TIP_MBR_QUIT) {
                    if (Array.isArray(memberList)) {
                        console.log(`[IM] Members left: ${memberList.map((m: any) => m.userID).join(', ')}`);
                        if (advisorUserID.value && memberList.some((member: any) => member.userID === advisorUserID.value)) {
                            isAdvisorInRoom.value = false;
                            console.log(`[IM] Advisor (${advisorUserID.value}) has left the room.`);
                        }
                    }
                }
                // 不将群系统消息计入聊天记录
                return;
            } else if (message.type === TIM.TYPES.MSG_GRP_SYS_NOTICE) {
                //检查群组group system message消息
                const { operationType } = message.payload;
                if (operationType === TIM.TYPES.GRP_TIP_MBR_CANCELED_ADMIN) {
                    // 兼容不同 SDK 版本，群聊解散通常为 'DISBAND'
                    console.warn('[IM] 群聊已被解散:', message.conversationID);
                    eventemitter.emit('group-disbanded', { groupId: message.conversationID });
                    return;
                }
            }
            regularMessages.push(message);
        });

        // 将普通消息添加到消息列表
        regularMessages.forEach((message: Message) => {
            const conversationID = message.conversationID;
            if (!messageList.value[conversationID]) {
                messageList.value[conversationID] = [];
            }
            messageList.value[conversationID].push(message);
        });
    };

    const handleConversationListUpdate = (event: { data: Conversation[] }) => {
        conversationList.value = event.data;
    };

    // --- 信令事件处理 ---
    const handleSignalingEvents = (event: { type: string, data: SignalingInvitation }) => {
        // 这条日志非常重要，用于捕获所有信令事件
        console.log(
            '%c[IM] Signaling Event Fired!',
            'color: #00ff00; font-weight: bold; font-size: 14px;',
            { type: event.type, details: event.data }
        );
        // 添加一个通用的UI反馈，用于调试，以确认事件是否到达
        ElMessage.info(`[Debug] Received signaling event: ${event.type}`);

        const invitation = event.data;

        switch (event.type) {
            case 'signalingInvitationReceived':
                // 通常，发起方也会收到此事件作为确认
                // 这里可以根据invitation.inviter === userInfo.value.userID判断是自己发出的还是别人发来的
                activeInvitation.value = invitation;
                console.log(`[IM] Invitation ${invitation.inviteID} from ${invitation.inviter} received.`);
                break;
            case 'signalingInvitationAccepted':
                console.log(`[IM] Invitation ${invitation.inviteID} accepted by ${invitation.invitee}. Data: ${invitation.data}`);
                // 在这里可以触发"占卜师已接单，等待入群"的UI提示
                break;
            case 'signalingInvitationRejected':
                ElMessage.warning(`The advisor has declined your invitation. (data: ${invitation.data})`);
                console.log(`[IM] Invitation ${invitation.inviteID} rejected by ${invitation.invitee}. Data: ${invitation.data}`);
                // 在这里可以触发"占卜师已拒绝"的UI提示
                activeInvitation.value = null;
                break;
            case 'signalingInvitationCancelled':
                console.log(`[IM] Invitation ${invitation.inviteID} cancelled by ${invitation.inviter}.`);
                activeInvitation.value = null;
                break;
            case 'signalingInvitationTimeout':
                ElMessage.error(`The invitation to the advisor has timed out. Please try again later.`);
                console.log(`[IM] Invitation ${invitation.inviteID} timed out.`);
                activeInvitation.value = null;
                break;
        }
    };

    /**
     * 初始化并注册TIM事件监听器
     * @param sdkReadyResolve Promise的resolve回调，用于在SDK ready时通知外部
     */
    const initializeListeners = (sdkReadyResolve: () => void) => {
        // 包装原始的SDK Ready处理器
        const sdkReadyHandler = () => {
            handleSDKReady();
            sdkReadyResolve(); // 当SDK准备好时，解决Promise
        };

        timService.on(TIM.EVENT.SDK_READY, sdkReadyHandler);
        timService.on(TIM.EVENT.MESSAGE_RECEIVED, handleMessageReceived);
        timService.on(TIM.EVENT.CONVERSATION_LIST_UPDATED, handleConversationListUpdate);

        // 注册所有信令相关的事件，并添加日志以确认
        console.log('[IM] Registering all signaling event listeners...');
        timService.on('signalingInvitationReceived', handleSignalingEvents);
        timService.on('signalingInvitationAccepted', handleSignalingEvents);
        timService.on('signalingInvitationRejected', handleSignalingEvents);
        timService.on('signalingInvitationCancelled', handleSignalingEvents);
        timService.on('signalingInvitationTimeout', handleSignalingEvents);
        console.log('[IM] All signaling event listeners have been registered.');
    };

    /**
     * 移除TIM事件监听器
     */
    const destroyListeners = () => {
        timService.off(TIM.EVENT.SDK_READY, handleSDKReady);
        timService.off(TIM.EVENT.MESSAGE_RECEIVED, handleMessageReceived);
        timService.off(TIM.EVENT.CONVERSATION_LIST_UPDATED, handleConversationListUpdate);
        // 注销所有信令相关的事件
        timService.off('signalingInvitationReceived', handleSignalingEvents);
        timService.off('signalingInvitationAccepted', handleSignalingEvents);
        timService.off('signalingInvitationRejected', handleSignalingEvents);
        timService.off('signalingInvitationCancelled', handleSignalingEvents);
        timService.off('signalingInvitationTimeout', handleSignalingEvents);
    };

    /**
     * 新增：自动登录
     */
    function tryAutoLogin() {
        const userID = localStorage.getItem('im_userID')
        const userSig = localStorage.getItem('userSig')
        if (userID && userSig && !isLogin.value) {
            login(userID, userSig)
        }
    }

    /**
     * 登录IM
     * @param userID
     * @param userSig
     */
    async function login(userID: string, userSig: string) {
        if (isLogin.value) {
            console.warn("IM already logged in.");
            return Promise.resolve();
        }

        return new Promise<void>(async (resolve, reject) => {
            try {
                await timService.login(userID, userSig);
                isLogin.value = true;
                userInfo.value = { userID, userSig };
                // 新增
                localStorage.setItem('userID', userID);
                localStorage.setItem('userSig', userSig);
                initializeListeners(resolve);
            } catch (error) {
                console.error("IM login failed:", error);
                reject(error);
            }
        });
    }

    /**
     * 登出IM
     */
    async function logout() {
        if (!isLogin.value) return;
        try {
            await timService.logout();
        } catch (error) {
            console.error("IM logout failed:", error);
        } finally {
            localStorage.removeItem('userID');
            localStorage.removeItem('userSig');
            destroyListeners();
            reset();
        }
    }

    /**
     * 发送文本消息
     * @param toID 接收方ID (user or group)
     * @param text 消息内容
     * @param convType 会话类型
     */
    async function sendMessage(toID: string, text: string, convType: 'C2C' | 'GROUP' = 'C2C') {
        if (!isLogin.value) {
            throw new Error('IM not logged in.');
        }
        try {
            const conversationType = convType === 'GROUP' ? TIM.TYPES.CONV_GROUP : TIM.TYPES.CONV_C2C;
            const message = timService.createTextMessage(toID, text, conversationType);
            // 生成本地消息对象，增加 sendStatus 字段
            const localMsg = { ...message, sendStatus: 'pending' };
            const conversationID = message.conversationID;
            if (!messageList.value[conversationID]) {
                messageList.value[conversationID] = [];
            }
            messageList.value[conversationID].push(localMsg);

            try {
                const result = await timService.sendMessage(message);
                // 发送成功，更新本地消息状态
                localMsg.sendStatus = 'success';
                return localMsg;
            } catch (error) {
                // 发送失败，更新本地消息状态
                localMsg.sendStatus = 'fail';
                throw error;
            }
        } catch (error) {
            console.error('Send message failed:', error);
            throw error;
        }
    }

    /**
     * 重发失败的消息
     * @param msg 失败的本地消息对象
     */
    async function resendMessage(msg: any) {
        if (!isLogin.value) {
            throw new Error('IM not logged in.');
        }
        msg.sendStatus = 'pending';
        try {
            const result = await timService.sendMessage(msg);
            msg.sendStatus = 'success';
            return msg;
        } catch (error) {
            msg.sendStatus = 'fail';
            throw error;
        }
    }

    /**
     * 发送通话信令邀请 (C2C)
     * @param advisorId 占卜师的用户ID
     * @param orderNo 订单号
     * @param groupId 预期的群组ID
     */
    async function sendCallInvitation(advisorId: string, orderNo: string, groupId: string) {
        if (!isLogin.value) {
            throw new Error('IM not logged in.');
        }

        const invitationData = {
            type: 'call_invitation',
            orderId: orderNo,
            roomId: groupId,
            toUid: advisorId,
            clinentAppId: 46,
            excludeFromHistoryMessage: true
        };

        try {
            const dataString = JSON.stringify(invitationData);
            console.log(`[IM] Sending signaling invitation to ${advisorId} with data: ${dataString}`);

            const response = await timService.invite(advisorId, dataString);

            console.log('[IM] Signaling invitation sent successfully, response:', response);
            return response;

        } catch (error) {
            console.error('Send signaling invitation failed:', error);
            throw error;
        }
    }

    /**
     * 设置需要关注的占卜师ID
     * @param id 占卜师的用户ID
     */
    function setAdvisor(id: string) {
        advisorUserID.value = id;
    }

    /**
     * 设置当前会话
     * @param conversationID
     */
    async function setCurrentConversation(conversationID: string) {
        currentConversationID.value = conversationID;
        // The `watch` effect above will now handle marking messages as read.
    }

    /**
     * 添加用户到群组
     * @param groupID 群组ID
     * @param userIDList 用户ID列表
     */
    async function addGroupMember(groupID: string, userIDList: string[]) {
        if (!isLogin.value || !isSDKReady.value) {
            throw new Error('IM not logged in or SDK not ready.');
        }
        try {
            return await timService.addGroupMember(groupID, userIDList);
        } catch (error) {
            console.error('Failed to add group member:', error);
            throw error;
        }
    }

    /**
     * 设置等待加入的目标群组ID
     * @param groupId The group ID to wait for.
     */
    function setTargetGroupId(groupId: string) {
        targetGroupId.value = groupId;
        console.log(`[IM] Now waiting to be added to group: ${groupId}`);
    }

    /**
     * 接受信令邀请
     * @param inviteID 邀请ID
     * @param data 自定义回复
     */
    async function acceptInvitation(inviteID: string, data = "Accepted") {
        if (!isLogin.value) throw new Error('IM not logged in.');
        try {
            console.log(`[IM] Accepting invitation ${inviteID}`);
            return await timService.accept(inviteID, data);
        } catch (error) {
            console.error(`Accept invitation ${inviteID} failed:`, error);
            throw error;
        }
    }

    /**
     * 拒绝信令邀请
     * @param inviteID 邀请ID
     * @param data 自定义回复
     */
    async function rejectInvitation(inviteID: string, data = "Rejected") {
        if (!isLogin.value) throw new Error('IM not logged in.');
        try {
            console.log(`[IM] Rejecting invitation ${inviteID}`);
            return await timService.reject(inviteID, data);
        } catch (error) {
            console.error(`Reject invitation ${inviteID} failed:`, error);
            throw error;
        }
    }
    async function getConversationList() {
        if (!isLogin.value) throw new Error('IM not logged in.');
        try {
            return await timService.getConversationList();
        } catch (error) {
            console.error('Failed to get conversation list:', error);
            throw error;
        }
    }

    /**
     * 重置store状态
     */
    function reset() {
        isSDKReady.value = false;
        isLogin.value = false;
        isAdvisorInRoom.value = false;
        advisorUserID.value = null;
        currentConversationID.value = null;
        conversationList.value = [];
        messageList.value = {};
        userInfo.value = null;
    }

    /**
     * 发送图片消息
     * @param toID 接收方ID (user or group)
     * @param file 图片文件
     * @param convType 会话类型
     */
    async function sendImageMessage(toID: string, file: File, convType: 'C2C' | 'GROUP' = 'C2C') {
        if (!isLogin.value) {
            throw new Error('IM not logged in.');
        }
        try {
            const conversationType = convType === 'GROUP' ? TIM.TYPES.CONV_GROUP : TIM.TYPES.CONV_C2C;
            const message = timService.createImageMessage(toID, file, conversationType);
            // 生成本地消息对象，增加 sendStatus 字段
            const localMsg = { ...message, sendStatus: 'pending' };
            const conversationID = message.conversationID;
            if (!messageList.value[conversationID]) {
                messageList.value[conversationID] = [];
            }
            messageList.value[conversationID].push(localMsg);

            try {
                const result = await timService.sendMessage(message);
                // 用新对象替换，确保响应式
                const idx = messageList.value[conversationID].indexOf(localMsg);
                if (idx !== -1) {
                    messageList.value[conversationID][idx] = { ...localMsg, sendStatus: 'success' };
                }
                return localMsg;
            } catch (error) {
                const idx = messageList.value[conversationID].indexOf(localMsg);
                if (idx !== -1) {
                    messageList.value[conversationID][idx] = { ...localMsg, sendStatus: 'fail' };
                }
                throw error;
            }
        } catch (error) {
            console.error('Send image message failed:', error);
            throw error;
        }
    }

    /**
     * 拉取历史消息（支持分页）
     * @param conversationID 会话ID
     * @param nextReqMessageID 分页游标
     * @returns { messageList, nextReqMessageID, isCompleted }
     */
    async function getMessageList(conversationID: string, nextReqMessageID?: string) {
        if (!isLogin.value) throw new Error('IM not logged in.');
        try {
            const res = await timService.getMessageList(conversationID, nextReqMessageID)
            console.log('原始数据',res)
            if (!messageList.value[conversationID]) {
                messageList.value[conversationID] = []
            }
            // 历史消息前置（只保留图文消息，且文字需非空）
            const filteredList = res.data.messageList.filter((msg: any) => {
                if (msg.type === 'TIMTextElem') {
                    return msg.payload && msg.payload.text && msg.payload.text.trim() !== ''
                }
                if (msg.type === 'TIMImageElem') {
                    return true
                }
                return false
            })
            messageList.value[conversationID] = filteredList.concat(messageList.value[conversationID])
            return {
                messageList: res.data.messageList,
                nextReqMessageID: res.data.nextReqMessageID,
                isCompleted: res.data.isCompleted // true 表示已无更多历史
            }
        } catch (error) {
            console.error('Failed to get message list:', error)
            throw error
        }
    }

    return {
        // State
        isSDKReady,
        isLogin,
        currentConversationID,
        conversationList,
        messageList,
        userInfo,
        isAdvisorInRoom,
        advisorUserID,
        targetGroupId,
        // Getters
        currentMessages,
        // Actions
        tryAutoLogin,
        login,
        logout,
        sendMessage,
        resendMessage,
        sendCallInvitation,
        acceptInvitation,
        rejectInvitation,
        addGroupMember,
        setAdvisor,
        setCurrentConversation,
        setTargetGroupId,
        reset,
        sendImageMessage,
        getConversationList,
        getMessageList
    };
});

