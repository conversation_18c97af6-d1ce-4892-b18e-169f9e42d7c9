import TIM from '@tencentcloud/chat'
import TIMUploadPlugin from 'tim-upload-plugin'

// 配置信息
const timConfig = {
    SDKAppID: Number(import.meta.env.VITE_APP_IM_SDK_APP_ID)
}

/**
 * 封装了腾讯云 IM SDK 的单例服务类
 */
class TIMService {
    private static instance: TIMService | null = null
    public tim: ReturnType<typeof TIM.create>

    private constructor() {
        this.tim = TIM.create({
            SDKAppID: timConfig.SDKAppID
        })

        this.tim.setLogLevel(1) // 0:普通日志, 1:详细日志, 2:警告, 3:错误, 4:无日志
        this.tim.registerPlugin({ 'tim-upload-plugin': TIMUploadPlugin })
    }

    /**
     * 获取 TIMService 的单例
     */
    public static getInstance(): TIMService {
        if (!TIMService.instance) {
            TIMService.instance = new TIMService()
        }
        return TIMService.instance
    }

    /**
     * 登录
     * @param userID
     * @param userSig
     */
    public login(userID: string, userSig: string) {
        return this.tim.login({ userID, userSig })
    }

    /**
     * 登出
     */
    public logout() {
        return this.tim.logout()
    }

    /**
     * 加入群组
     * @param groupID 群组ID
     */
    public joinGroup(groupID: string) {
        return this.tim.joinGroup({
            groupID,
            // type: TIM.TYPES.GRP_AVCHATROOM, // 恢复群组类型，AVChatRoom最适合即时会话，无需审批
            // applyMessage:''
        });
    }

    /**
     * 添加群成员
     * @param groupID 群组ID
     * @param userIDList 要添加的用户ID列表
     */
    public addGroupMember(groupID: string, userIDList: string[]) {
        return this.tim.addGroupMember({
            groupID,
            userIDList
        });
    }

    /**
     * 注册事件监听
     * @param eventName 事件名
     * @param handler 事件处理函数
     */
    public on(eventName: string, handler: (event: any) => void) {
        this.tim.on(eventName, handler)
    }

    /**
     * 注销事件监听
     * @param eventName 事件名
     * @param handler 事件处理函数
     */
    public off(eventName: string, handler?: (event: any) => void) {
        this.tim.off(eventName, handler as any)
    }

    /**
     * 创建文本消息
     * @param to 接收方 userID 或 groupID
     * @param text 消息文本
     * @param conversationType 会话类型，默认为 C2C
     */
    public createTextMessage(to: string, text: string, conversationType: TIM.TYPES.CONV_C2C | TIM.TYPES.CONV_GROUP = TIM.TYPES.CONV_C2C) {
        return this.tim.createTextMessage({
            to,
            conversationType,
            payload: { text }
        })
    }

    /**
     * 创建自定义消息
     * @param to 接收方 userID 或 groupID
     * @param payload 消息内容
     * @param conversationType 会话类型，默认为 C2C
     */
    public createCustomMessage(to: string, payload: { data: string, description?: string, extension?: string }, conversationType: TIM.TYPES.CONV_C2C | TIM.TYPES.CONV_GROUP = TIM.TYPES.CONV_C2C) {
        return this.tim.createCustomMessage({
            to,
            conversationType,
            payload: payload
        })
    }

    /**
     * 发送消息
     * @param message 消息实例
     */
    public sendMessage(message: any) {
        return this.tim.sendMessage(message)
    }
    /**
     * 发送信令邀请
     * @param userID 被邀请人 ID
     * @param data 自定义数据
     * @param timeout 超时时间（秒），0表示永不超时
     */
    public invite(userID: string, data: string, timeout = 90) {
        return this.tim.invite({
            userID,
            data,
            timeout,
            // offlinePushInfo: {}, // 可选，用于离线推送
        });
    }

    /**
     * 接受信令邀请
     * @param inviteID 邀请 ID
     * @param data 自定义回复数据
     */
    public accept(inviteID: string, data: string) {
        return this.tim.accept({
            inviteID,
            data,
        });
    }

    /**
     * 拒绝信令邀请
     * @param inviteID 邀请 ID
     * @param data 自定义回复数据
     */
    public reject(inviteID: string, data: string) {
        return this.tim.reject({
            inviteID,
            data,
        });
    }

    /**
     * 获取会话列表
     */
    public getConversationList(options?:any) {
        return this.tim.getConversationList(options)
    }

    /**
     * 获取历史消息
     * @param conversationID 会话ID
     * @param count 消息数量
     */
    public getMessageList(conversationID: string, nextReqMessageID?:string) {
        return this.tim.getMessageList({
            conversationID,
            nextReqMessageID
        } as any)
    }

    /**
     * 将消息设置为已读
     * @param conversationID 会话ID
     */
    public setMessageRead(conversationID: string) {
        return this.tim.setMessageRead({ conversationID })
    }

    /**
     * 创建图片消息
     * @param to 接收方 userID 或 groupID
     * @param file 图片文件
     * @param conversationType 会话类型，默认为 C2C
     */
    public createImageMessage(to: string, file: File, conversationType: TIM.TYPES.CONV_C2C | TIM.TYPES.CONV_GROUP = TIM.TYPES.CONV_C2C) {
        return this.tim.createImageMessage({
            to,
            conversationType,
            payload: { file }
        })
    }
}

// 导出单例
export default TIMService.getInstance() 