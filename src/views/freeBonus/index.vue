<template>
    <div class="wheel-container">
        <div class="wheel-content">
            <div class="wheel-content-header">
                <img src="@/assets/free-bonus/lucky.png" alt="" class="lucky-image">
            </div>
            <!--游戏部分-->
            <div class="wheel-content-game">
                <div class="cards-container" :class="{ paused: isPaused, spinning: isSpinning, stopped: isStopped }">
                    <div class="cards-container-women">
                        <!-- 中间人物图片 -->
                        <div class="cards-container-women-layer"></div>
                        <img src="@/assets/free-bonus/image.png" alt="" class="center-image" />
                    </div>
                    <!-- 旋转卡片部分 - 8张卡片围绕中心 -->
                    <div class="cards-container-item card" v-for="item in 8" :key="item" :class="`card-${item}`"
                        :style="`--angle: ${45 * (item - 1)}deg;`">
                        <img src="@/assets/free-bonus/rotate-card.png" alt="" class="rotate-card-image" />
                    </div>
                </div>
            </div>
            <!--按钮和规则-->
            <div class="button-rules-container">
                <div class="start-button-wrapper" @click="startSpin" :class="{ disabled: isSpinning }">
                    <div class="start-button-inner">
                        <div class="start-button-text">{{ isSpinning ? 'Spinning...' : 'Start' }}</div>
                    </div>
                </div>

                <!-- 测试按钮 - 可以指定中奖索引 -->
                <!-- <div class="test-buttons" v-if="!isSpinning"
                    style="margin-top: 1em; display: flex; gap: 0.5em; flex-wrap: wrap; justify-content: center;">
                    <button v-for="i in 8" :key="i" @click="startSpin(i)"
                        style="padding: 0.5em; background: rgba(255,255,255,0.2); border: 1px solid rgba(255,255,255,0.3); border-radius: 0.5em; color: white; font-size: 0.8em; cursor: pointer;">
                        卡片{{ i }}
                    </button>
                </div> -->

                <!-- 缓动效果选择 -->
                <!-- <div class="easing-selector" v-if="!isSpinning" style="margin-top: 0.5em; text-align: center;">
                    <div style="color: rgba(255,255,255,0.7); font-size: 0.8em; margin-bottom: 0.5em;">缓动效果:</div>
                    <select v-model="selectedEasing"
                        style="padding: 0.3em; background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.3); border-radius: 0.3em; color: white; font-size: 0.8em;">
                        <option value="cubic-bezier(0.15, 0.85, 0.35, 1)">从快到慢 (推荐)</option>
                        <option value="cubic-bezier(0.7, 0, 0.3, 1)">强烈减速</option>
                        <option value="ease-out">标准减速</option>
                        <option value="cubic-bezier(0.25, 0.46, 0.45, 0.94)">平滑减速</option>
                        <option value="linear">匀速</option>
                    </select>
                </div> -->

                <!-- 调试按钮 -->
                <!-- <div v-if="!isSpinning" style="margin-top: 0.5em; text-align: center;">
                    <button @click="debugCurrentAngles"
                        style="padding: 0.3em; background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.3); border-radius: 0.3em; color: white; font-size: 0.8em; cursor: pointer;">
                        查看当前角度
                    </button>
                </div> -->

                <!--gift rule-->
                <div class="gift-rule-container">
                    <div class="gift-claimed-item" @click="openGiftClaimDialog">
                        <img src="@/assets/free-bonus/gift.png" alt="" class="gift-icon" />
                        <span class="gift-text">Gift Claimed</span>
                    </div>
                    <div class="rule-item" @click="openRuleDialog">Rule</div>
                </div>
            </div>
            <!--rewards preview--->
            <div class="reward-preview">
                <div class="reward-preview-header">
                    <img src="@/assets/free-bonus/rewards-preview.png" alt="" class="rewards-preview-image" />
                </div>
                <div class="reward-preview-list">
                    <div class="reward-item" v-for="reward in details" :key="reward.id">
                        <div class="reward-chance-label">{{ reward.winningRate * 100 }}% Chance</div>
                        <div class="reward-card">
                            <div class="reward-image-container">
                                <img :src="reward.awardIcon" alt="" class="reward-icon" />
                            </div>
                            <span class="reward-text">{{ reward.awardName }}</span>
                        </div>
                    </div>
                </div>
            </div>
            <img src="@/assets/free-bonus/base.png" alt="" class="base-circle" />
        </div>
        <!--左右装饰图片-->
        <img src="@/assets/free-bonus/left-vector.png" alt="" class="left-vector left-vector-1" />
        <img src="@/assets/free-bonus/left-vector.png" alt="" class="left-vector left-vector-2" />
        <img src="@/assets/free-bonus/right-vector.png" alt="" class="right-vector right-vector-1" />
        <img src="@/assets/free-bonus/right-vector.png" alt="" class="right-vector right-vector-2" />

        <!--规则弹窗-->
        <RuleDialog :dialogRuleVisible="dialogRuleVisible" @update:dialogRuleVisible="dialogRuleVisible = $event">
        </RuleDialog>
        <!--中奖弹窗-->
        <GiftDialog :dialog-gift-visible="dialogGiftVisible" :luckyGift="luckyGift"  @update:dialogGiftVisible="dialogGiftVisible = $event">
        </GiftDialog>
        <!--中奖list-->
        <GiftClaimedDialog :dialog-gift-claimend-visible="dialogGiftClaimedVisible"
            @update:dialogGiftClaimend="dialogGiftClaimedVisible = $event"></GiftClaimedDialog>
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch,onUnmounted } from 'vue'
import RuleDialog from './components/ruleDialog.vue'
import GiftDialog from './components/giftDialog.vue'
import GiftClaimedDialog from './components/giftClaimedDialog.vue'
import { getLuckyDrawConfig, checkInLuckyDraw } from '@/api/active'

//  奖品预览

const details = ref<any[]>([])
// 转盘状态
const isPaused = ref(false)
const isSpinning = ref(false)
const isStopped = ref(false) // 转盘是否已停止
const luckyGift = ref<any>({})
// 缓动效果选择
const selectedEasing = ref('cubic-bezier(0.25, 0.46, 0.45, 0.94)')

// 弹窗状态
const dialogRuleVisible = ref(false)
const dialogGiftVisible = ref(false)
const dialogGiftClaimedVisible = ref(false)
const glowingCardElement = ref<HTMLElement | null>(null)

// 奖励数据
const rewards = ref([
    { id: 1, name: '10% OFF Top-up Pack', chance: '10%', icon: '🎁' },
    { id: 2, name: '20% OFF Premium', chance: '15%', icon: '💎' },
    { id: 3, name: 'Free Reading', chance: '20%', icon: '📚' },
    { id: 4, name: '50 Coins', chance: '25%', icon: '🪙' },
    { id: 5, name: '100 Coins', chance: '15%', icon: '💰' },
    { id: 6, name: 'Mystery Gift', chance: '10%', icon: '🎉' },
    { id: 7, name: '5% OFF', chance: '5%', icon: '🏷️' },
])

// 获取每个cards-container-item的Y轴旋转角度
const getAllCardsContainerItemsRotateY = () => {
    const cardsRotateY = []
    for (let i = 1; i <= 8; i++) {
        const cardElement = document.querySelector(`.card-${i}`) as HTMLElement
        if (cardElement) {
            const computedStyle = window.getComputedStyle(cardElement)
            // 从style属性中读取卡片的静态基础角度
            const baseAngle = parseFloat(cardElement.style.getPropertyValue('--angle'))
            // 从计算样式中可靠地读取当前动画中的动态角度
            const dynamicAngle = parseFloat(computedStyle.getPropertyValue('--card-dynamic-angle'))
            // 总角度 = 基础角度 + 动态角度
            const currentRotateY = (baseAngle + dynamicAngle) % 360

            cardsRotateY.push({
                cardIndex: i,
                // 这里我们返回总的旋转角度
                currentRotateY: Math.round(currentRotateY * 100) / 100,
                element: cardElement
            })
        } else {
            console.warn(`未找到卡片元素: .card-${i}`)
        }
    }
    return cardsRotateY
}

const animateWinningCard = (element: HTMLElement) => {
    // 存储当前发光的元素，以便后续移除效果
    glowingCardElement.value = element;

    // 1. 给原始的中奖卡片加上辉光效果
    element.classList.add('has-glow');

    // 2. 克隆中奖卡片用于动画。
    const clone = element.cloneNode(true) as HTMLElement;
    // 关键：立即移除克隆体上的辉光类，确保只有原始卡片发光
    clone.classList.remove('has-glow');

    // 3. 将克隆体添加到同一个父容器。它会自动出现在原始卡片的顶部
    const parentContainer = element.parentElement;
    if (!parentContainer) return;
    parentContainer.appendChild(clone);

    // 4. 提升克隆体的堆叠顺序，并禁用其自身的无限循环动画
    clone.style.zIndex = '100';
    clone.style.animation = 'none';

    // 5. 使用transition来制作缩小动画
    setTimeout(() => {
        // 获取原始卡片精准的、计算后的transform值
        const currentTransform = window.getComputedStyle(element).transform;

        clone.style.transition = 'transform 1s cubic-bezier(0.6, -0.28, 0.735, 0.045)';
        // 在现有的transform矩阵上，追加一个缩放效果
        clone.style.transform = `${currentTransform} scale(0.7)`;

        // 6. 动画结束后，弹出对话框、移除克隆体
        setTimeout(() => {
            console.log('克隆卡片缩小动画结束，准备弹出中奖弹窗！');
            dialogGiftVisible.value = true;
            clone.remove();
            // 注意：辉光效果此时不会被移除
        }, 1000); // 动画时长1秒
    }, 50); // 短暂延迟确保初始样式已应用，可以触发transition
}
const checkInLuckyDrawFunc = ()=>{
    return new Promise((resolve,reject)=>{
        checkInLuckyDraw().then(response => {
            resolve(response.data)
        }).catch(error => {
            reject(error)
        })
    }) 
}
// 开始转盘 - 可以传入目标索引或处理点击事件
const startSpin = async (targetIndexOrEvent?: number | MouseEvent) => {
    // 在新一轮开始时，清除上一轮的辉光效果
    luckyGift.value = await checkInLuckyDrawFunc()
    if (glowingCardElement.value) {
        glowingCardElement.value.classList.remove('has-glow');
        glowingCardElement.value = null;
    }

    if (isSpinning.value) return

    // 判断传入的是索引还是事件
    let finalTargetIndex: number
    if (typeof targetIndexOrEvent === 'number') {
        finalTargetIndex = targetIndexOrEvent
    } else {
        // 如果是点击事件或没有传入参数，随机选择一个
        finalTargetIndex = Math.floor(Math.random() * 8) + 1
    }

    console.log('开始转盘，目标索引:', finalTargetIndex)

    // 获取每个cards-container-item的当前Y轴旋转角度
    const cardsRotateY = getAllCardsContainerItemsRotateY()
    console.log('当前所有卡片的rotateY值:', cardsRotateY)

    // 找到目标卡片的当前角度
    const targetCard = cardsRotateY.find(card => card.cardIndex === finalTargetIndex)
    if (!targetCard) {
        console.error('未找到目标卡片:', finalTargetIndex)
        return
    }

    const currentRotateY = targetCard.currentRotateY
    console.log(`目标卡片${finalTargetIndex}当前角度:`, currentRotateY)

    // 我们希望目标卡片当前的旋转角度(currentRotateY) 最终停在 360 (或0)的位置
    // 所以需要额外旋转的角度是 360 - currentRotateY, 再加上几圈
    const rotationNeeded = 5 * 360 + (360 - currentRotateY)
    console.log('需要旋转的角度:', rotationNeeded)

    isSpinning.value = true

    // 应用旋转动画到每个卡片
    applySpinToAllCards(cardsRotateY, finalTargetIndex, rotationNeeded)

    // 4秒后停止转盘 (匹配动画时间)
    const spinDuration = 4000
    setTimeout(() => {
        isSpinning.value = false
        isStopped.value = true
        console.log('转盘结束，目标卡片应该在0度位置')

        // 触发中奖卡片的缩小动画
        const winningCardElement = document.querySelector(`.card-${finalTargetIndex}`) as HTMLElement;
        if (winningCardElement) {
            animateWinningCard(winningCardElement);
        }

        // 验证结果
        setTimeout(() => {
            console.log('=== 验证最终结果 ===')
            const finalCardsRotateY = getAllCardsContainerItemsRotateY()
            const finalTargetCard = finalCardsRotateY.find(card => card.cardIndex === finalTargetIndex)
            if (finalTargetCard) {
                // 读取最终的transform值来验证
                const finalTransform = window.getComputedStyle(finalTargetCard.element).transform
                console.log(`最终目标卡片${finalTargetIndex}的角度(取模360):`, finalTargetCard.currentRotateY % 360)
                console.log(`最终目标卡片${finalTargetIndex}的transform:`, finalTransform)
            }
            console.log('=== 验证完成 ===')
        }, 100)
    }, spinDuration)
}

// 应用旋转动画到所有卡片
const applySpinToAllCards = (cardsRotateY: any[], targetIndex: number, rotationNeeded: number) => {
    console.log(`=== 开始计算，目标卡片索引: ${targetIndex} ===`)
    const spinDuration = 4 // s

    cardsRotateY.forEach(card => {
        const cardElement = card.element as HTMLElement
        if (cardElement) {
            // 暂停当前的无限循环动画
            cardElement.style.animationPlayState = 'paused'

            const currentRotateY = card.currentRotateY

            // 所有卡片都旋转相同的角度
            const finalRotation = currentRotateY + rotationNeeded

            // 设置CSS变量用于临时动画
            cardElement.style.setProperty('--card-current-rotation', `${currentRotateY}deg`)
            cardElement.style.setProperty('--card-final-rotation', `${finalRotation}deg`)
            cardElement.style.setProperty('--card-spin-duration', `${spinDuration}s`)

            // 应用选中的缓动效果
            cardElement.style.animationTimingFunction = selectedEasing.value

            // 强制重绘以确保动画重置
            void cardElement.offsetHeight

            // 添加目标旋转动画类
            cardElement.classList.add('card-spinning-to-target')

            // 动画结束后清理
            setTimeout(() => {
                cardElement.classList.remove('card-spinning-to-target')
                // 停止所有动画并将transform固定在最终状态
                cardElement.style.animation = 'none'

                const zTranslate = getZTranslateForScreen() // 获取屏幕对应的Z轴距离
                cardElement.style.transform = `rotateY(${finalRotation}deg) translateZ(${zTranslate}) rotateY(${-finalRotation}deg)`

                console.log(`卡片${card.cardIndex}动画结束，最终角度 ${finalRotation % 360}`)
            }, spinDuration * 1000)
        }
    })

    console.log(`=== 计算完成 ===`)
}

// 辅助函数，根据屏幕宽度获取正确的translateZ值
const getZTranslateForScreen = (): string => {
    const screenWidth = window.innerWidth;
    if (screenWidth >= 1120) {
        return '30em';
    }
    if (screenWidth >= 640) {
        return '20em';
    }
    return '9.5em';
}

// 调试函数 - 查看当前角度
const debugCurrentAngles = () => {
    const cardsRotateY = getAllCardsContainerItemsRotateY()
    console.log('=== 当前角度调试信息 ===')
    cardsRotateY.forEach(card => {
        console.log(`卡片${card.cardIndex}: 初始角度=${card.currentRotateY}°`)
    })
    console.log('========================')
}

// 打开规则弹窗
const openRuleDialog = () => {
    dialogRuleVisible.value = true
}

const openGiftClaimDialog = ()=>{
    dialogGiftClaimedVisible.value = true
}
//得到转盘下面的奖品
const getLuckyDrawConfigFunc = () => {
    getLuckyDrawConfig().then(response => {
        details.value = response.data.details
    }).catch(error => {

    })
}

onMounted(() => {
    const footer = document.getElementsByTagName('footer')[0]
    if (footer) {
        footer.style.marginTop = '0px'
    }
    getLuckyDrawConfigFunc()
    // onUnmounted(() => {
        
    // })
})

</script>

<style lang="scss" scoped>
// 基础字体大小设置，用于em单位计算
// 移动端: 16px, 平板: 18px, 桌面: 20px
html {
    font-size: 16px;

    @media (min-width: 640px) {
        font-size: 16px;
    }

    @media (min-width: 1120px) {
        font-size: 20px;
    }
}

.wheel-container {
    width: 100%;
    min-height: 100vh;
    background: linear-gradient(180deg, #0E1448 0%, #6A74C5 100%);
    position: relative;
    overflow: hidden;
    padding-bottom: 1.75em; // 28px -> 1.75em

    // 添加星空背景效果
    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-image:
            radial-gradient(2px 2px at 20px 30px, #fff, transparent),
            radial-gradient(2px 2px at 40px 70px, rgba(255, 255, 255, 0.8), transparent),
            radial-gradient(1px 1px at 90px 40px, #fff, transparent),
            radial-gradient(1px 1px at 130px 80px, rgba(255, 255, 255, 0.6), transparent),
            radial-gradient(2px 2px at 160px 30px, #fff, transparent);
        background-repeat: repeat;
        background-size: 200px 100px;
        opacity: 0.3;
        z-index: 0;
    }

    .wheel-content {
        position: relative;
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background-image: url('@/assets/free-bonus/free-header.png');
        background-repeat: no-repeat;
        background-position: center center;
        background-size: cover;

        .wheel-content-header {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 1.3em; // 21px -> 1.3em

            @media (min-width: 640px) {
                margin-top: 1.67em; // 30px -> 1.67em
            }

            @media (min-width: 768px) {
                margin-top: 2.78em; // 50px -> 2.78em
            }

            @media (min-width: 1120px) {
                margin-top: 4.15em; // 83px -> 4.15em
            }

            .lucky-image {
                height: 1.44em; // 23px -> 1.44em

                @media (min-width: 768px) {
                    height: 1.67em; // 30px -> 1.67em
                }

                @media (min-width: 1120px) {
                    height: 2.9em; // 58px -> 2.9em
                }
            }
        }

        .wheel-content-game {
            width: 100%;
            position: relative;
            height: 25em; // 大幅增加高度
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 1.5em; // 减少上边距，给转盘更多空间
            z-index: 1;

            @media (min-width: 640px) {
                height: 30em; // 增加高度
                margin-top: 1.25em;
            }

            @media (min-width: 768px) {
                height: 35em; // 进一步增加
                margin-top: 1em;
            }

            @media (min-width: 1120px) {
                height: 50em; // 大屏幕下显著增加
                margin-top: 2.5em;
            }

            // 添加发光效果 - 同步增大
            &::before {
                content: '';
                position: absolute;
                top: 50%;
                left: 50%;
                width: 20em; // 增大发光区域
                height: 20em;
                background: radial-gradient(circle, rgba(138, 180, 248, 0.3) 0%, transparent 70%);
                border-radius: 50%;
                transform: translate(-50%, -50%);
                z-index: -1;

                @media (min-width: 640px) {
                    width: 25em;
                    height: 25em;
                }

                @media (min-width: 768px) {
                    width: 30em;
                    height: 30em;
                }

                @media (min-width: 1120px) {
                    width: 40em; // 大屏幕下更大的发光效果
                    height: 40em;
                }
            }

            .cards-container {
                position: absolute;
                left: 50%;
                top: 50%;
                width: 100%;
                height: 25em; // 与游戏区域匹配
                transform: translate(-50%, -50%) rotateX(-10deg);
                perspective: 62.5em; // 增加透视距离
                pointer-events: none;
                z-index: 2;
                display: flex;
                justify-content: center;
                align-items: center;
                transform-style: preserve-3d;
                backface-visibility: visible; // 确保容器背面也可见

                @media (min-width: 640px) {
                    height: 30em; // 与游戏区域匹配
                    perspective: 75em;
                }

                @media (min-width: 768px) {
                    height: 35em; // 与游戏区域匹配
                    perspective: 87.5em;
                }

                @media (min-width: 1120px) {
                    height: 50em; // 与游戏区域匹配
                    perspective: 100em; // 更大的透视距离
                }

                &.paused .card {
                    animation-play-state: paused;
                }

                // 转盘停止后保持静止
                &.stopped .card {
                    animation-play-state: paused;
                    animation-fill-mode: forwards;
                }

                // 新的精确旋转动画
                &.spinning-to-target {
                    animation: spinToTarget var(--spin-duration, 4s) ease-out forwards;
                }

                .cards-container-women {
                    position: absolute;
                    width: 100%;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    height: 25em; // 与容器匹配
                    transform: rotateX(30deg);
                    transform-style: preserve-3d;

                    @media (min-width: 640px) {
                        height: 30em; // 与容器匹配
                    }

                    @media (min-width: 768px) {
                        height: 35em; // 与容器匹配
                    }

                    @media (min-width: 1120px) {
                        width: 37.5em; // 增加宽度
                        height: 50em; // 与容器匹配
                    }

                    .center-image {
                        position: relative;
                        z-index: 3;
                        height: 100%;
                        filter: drop-shadow(0 0 1.5em rgba(138, 180, 248, 0.5)); // 增强发光效果
                    }

                    &-layer {
                        position: absolute;
                        left: 50%;
                        top: 50%;
                        width: 8em; // 增加光环尺寸 128px -> 8em
                        height: 8em; // 128px -> 8em
                        border-radius: 50%;
                        transform: translate(-50%, -50%);
                        background: radial-gradient(circle, rgba(138, 180, 248, 0.8) 0%, rgba(66, 78, 194, 0.4) 100%);
                        filter: blur(2.5em); // 增加模糊效果 40px -> 2.5em
                        z-index: 1;

                        @media (min-width: 640px) {
                            width: 10em; // 180px -> 10em
                            height: 10em; // 180px -> 10em
                            filter: blur(3em); // 54px -> 3em
                        }

                        @media (min-width: 1120px) {
                            width: 16em; // 320px -> 16em (显著增加)
                            height: 16em; // 320px -> 16em
                            filter: blur(4em); // 80px -> 4em (更强的模糊效果)
                        }
                    }
                }

                .card {
                    position: absolute;
                    transform-style: preserve-3d;
                    backface-visibility: visible; // 确保背面也可见

                    // 单个卡片的精确旋转动画 - 从快到慢
                    &.card-spinning-to-target {
                        animation: cardSpinToTarget var(--card-spin-duration) forwards !important;
                        animation-play-state: running !important;
                    }

                    // 为停在原地的中奖卡片添加辉光边框效果
                    &.has-glow {
                        border: 2px solid rgba(255, 237, 183, 0.9);
                        border-radius: 6px;

                        &::before,
                        &::after {
                            content: '';
                            position: absolute;
                            z-index: -1;
                            border-radius: 1em; // 视觉上匹配卡片图片的圆角
                            pointer-events: none; // 确保不影响交互
                        }

                        // 内层辉光
                        &::before {
                            // inset: -6px;
                            // border: 6px solid rgba(255, 237, 183, 0.3);
                        }

                        // 外层辉光
                        &::after {
                            inset: -16px; // 6px (来自::before) + 10px (自身边框)
                            border: 10px solid rgba(255, 237, 183, 0.1);
                            z-index: -2; // 在::before的后面
                        }
                    }
                }

                .rotate-card-image {
                    width: 3.5em; // 显著增加尺寸 80px -> 5em
                    height: 6em; // 140px -> 8.75em
                    filter: drop-shadow(0 0 0.75em rgba(138, 180, 248, 0.4)); // 增强发光效果
                    transition: filter 0.3s ease;

                    @media (min-width: 640px) {
                        width: 5.83em; // 105px -> 5.83em
                        height: 10.28em; // 185px -> 10.28em
                    }

                    @media (min-width: 768px) {
                        width: 6.67em; // 120px -> 6.67em
                        height: 11.67em; // 210px -> 11.67em
                    }

                    @media (min-width: 1120px) {
                        width: 8em; // 160px -> 8em (大幅增加)
                        height: 14em; // 280px -> 14em
                        filter: drop-shadow(0 0 1.25em rgba(138, 180, 248, 0.6)); // 更强的发光
                    }
                }
            }

            // 移动端卡片动画样式
            @media (max-width: 639px) {
                .cards-container {
                    perspective: 62.5em;
                    transform: translate(-50%, -50%) rotateX(-30deg);
                }

                .cards-container-women {
                    transform: rotateX(30deg);
                }

                @for $i from 1 through 8 {
                    .card-#{$i} {
                        --z-translate: 9.5em;
                        top: 20%;
                        animation: card-rotate-prop 12s linear infinite;
                        transform: rotateY(calc(var(--angle) + var(--card-dynamic-angle))) translateZ(var(--z-translate)) rotateY(calc(-1 * (var(--angle) + var(--card-dynamic-angle))));
                    }
                }
            }

            // 中等屏幕样式优化
            @media (min-width: 640px) and (max-width: 1119px) {
                .cards-container {
                    perspective: 87.5em;
                    transform: translate(-50%, -50%) rotateX(-30deg);
                }

                .cards-container-women {
                    transform: rotateX(30deg);
                }

                @for $i from 1 through 8 {
                    .card-#{$i} {
                        --z-translate: 20em;
                        top: 15%;
                        animation: card-rotate-prop 12s linear infinite;
                        transform: rotateY(calc(var(--angle) + var(--card-dynamic-angle))) translateZ(var(--z-translate)) rotateY(calc(-1 * (var(--angle) + var(--card-dynamic-angle))));
                    }
                }
            }

            // 大屏幕样式
            @media (min-width: 1120px) {
                .cards-container {
                    perspective: 100em;
                    transform: translate(-50%, -50%) rotateX(-25deg);
                }

                .cards-container-women {
                    transform: rotateX(25deg);
                }

                @for $i from 1 through 8 {
                    .card-#{$i} {
                        --z-translate: 30em;
                        top: 20%;
                        animation: card-rotate-prop 12s linear infinite;
                        transform: rotateY(calc(var(--angle) + var(--card-dynamic-angle))) translateZ(var(--z-translate)) rotateY(calc(-1 * (var(--angle) + var(--card-dynamic-angle))));
                    }
                }
            }
        }

        // 按钮和规则容器
        .button-rules-container {
            position: relative;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            width: 100%;
            margin-top: -1.5em; // 增加负边距以适应更大的转盘
            z-index: 10;

            @media (min-width: 640px) {
                margin-top: -2em;
            }

            @media (min-width: 768px) {
                margin-top: -2.5em;
            }

            @media (min-width: 1120px) {
                margin-top: -5em; // 大屏幕下更大的负边距
            }
        }

        // 开始按钮样式
        .start-button-wrapper {
            width: 18.13em; // 290px -> 18.13em
            height: 4.25em; // 68px -> 4.25em
            border-radius: 2.63em; // 42px -> 2.63em
            padding: 0.53em; // 8.5px -> 0.53em
            background-color: rgba(179, 160, 255, 0.1);
            cursor: pointer;
            transition: all 0.3s ease;

            // 悬停效果
            &:hover:not(.disabled) {
                transform: translateY(-0.125em);
                background-color: rgba(179, 160, 255, 0.15);
                box-shadow: 0 0.5em 1em rgba(138, 180, 248, 0.2);
            }

            // 禁用状态
            &.disabled {
                opacity: 0.6;
                cursor: not-allowed;

                .start-button-inner {
                    background-color: rgba(179, 160, 255, 0.05);
                }

                .start-button-text {
                    color: #888;
                }
            }

            @media (min-width: 640px) {
                width: 17.78em; // 320px -> 17.78em
                height: 4.11em; // 74px -> 4.11em
                padding: 0.5em; // 9px -> 0.5em
            }

            @media (min-width: 1120px) {
                width: 27.8em; // 445px -> 22.25em
                height: 6.5em; // 104px -> 5.2em
                padding: 0.65em; // 13px -> 0.65em
            }

            .start-button-inner {
                border-radius: 2.63em; // 42px -> 2.63em
                width: 100%;
                height: 100%;
                padding: 0.43em 0.44em; // 12px 7px -> 0.75em 0.44em
                background-color: rgba(179, 160, 255, 0.1);

                @media (min-width: 640px) {
                    padding: 0.5em 0.44em; // 14px 8px -> 0.78em 0.44em
                }

                @media (min-width: 1120px) {
                    padding: 0.9em 0.65em; // 18px 13px -> 0.9em 0.65em
                }

                .start-button-text {
                    color: #553300;
                    font-size: 0.87em; // 18px -> 1.13em
                    line-height: 1.38em; // 22px -> 1.38em
                    font-family: 'Lora', serif;
                    width: 100%;
                    height: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    cursor: pointer;
                    background: url('@/assets/free-bonus/button-bg.png') no-repeat;
                    background-size: 100% 100%;

                    @media (min-width: 640px) {
                        font-size: 1.11em; // 20px -> 1.11em
                    }

                    @media (min-width: 1120px) {
                        font-size: 1.1em; // 22px -> 1.1em
                    }
                }
            }
        }

        // 礼品和规则容器
        .gift-rule-container {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            gap: 4.69em; // 75px -> 4.69em
            margin-top: 0.38em; // 6px -> 0.38em

            @media (min-width: 640px) {
                margin-top: 0.56em; // 10px -> 0.56em
            }

            @media (min-width: 1120px) {
                margin-top: 1em; // 20px -> 1em
                gap: 5em; // 100px -> 5em
            }

            .gift-claimed-item {
                display: flex;
                align-items: center;

                .gift-icon {
                    width: 1.13em; // 18px -> 1.13em
                    height: 1.13em; // 18px -> 1.13em

                    @media (min-width: 640px) {
                        width: 1.22em; // 22px -> 1.22em
                        height: 1.22em; // 22px -> 1.22em
                    }

                    @media (min-width: 1120px) {
                        width: 1.5em; // 30px -> 1.5em
                        height: 1.5em; // 30px -> 1.5em
                    }
                }

                .gift-text {
                    color: #fff;
                    font-size: 0.69em; // 11px -> 0.69em
                    line-height: 0.94em; // 15px -> 0.94em
                    margin-left: 0.31em; // 5px -> 0.31em
                    cursor: pointer;

                    @media (min-width: 640px) {
                        font-size: 0.72em; // 13px -> 0.72em
                    }

                    @media (min-width: 1120px) {
                        font-size: 0.8em; // 16px -> 0.8em
                    }
                }
            }

            .rule-item {
                display: flex;
                align-items: center;
                color: #fff;
                font-size: 0.69em; // 11px -> 0.69em
                line-height: 0.94em; // 15px -> 0.94em
                cursor: pointer;

                @media (min-width: 640px) {
                    font-size: 0.72em; // 13px -> 0.72em
                }

                @media (min-width: 1120px) {
                    font-size: 0.8em; // 16px -> 0.8em
                }
            }
        }
    }
}

// 注册CSS自定义属性，以便进行动画
@property --card-dynamic-angle {
    syntax: '<angle>';
    inherits: false;
    initial-value: 0deg;
}

// 无限循环动画 - 只改变自定义属性
@keyframes card-rotate-prop {
    from {
        --card-dynamic-angle: 0deg;
    }

    to {
        --card-dynamic-angle: 360deg;
    }
}

// 旋转到目标的临时动画
@keyframes cardSpinToTarget {
    from {
        transform: rotateY(var(--card-current-rotation)) translateZ(var(--z-translate)) rotateY(calc(-1 * var(--card-current-rotation)));
    }

    to {
        transform: rotateY(var(--card-final-rotation)) translateZ(var(--z-translate)) rotateY(calc(-1 * var(--card-final-rotation)));
    }
}

// 奖励预览区域
.reward-preview {
    margin-top: 1.5em; // 减少间距，因为转盘变大了
    width: 100%;
    position: relative;
    z-index: 1;

    @media (min-width: 640px) {
        margin-top: 1.25em;
    }

    @media (min-width: 768px) {
        margin-top: 1em;
    }

    @media (min-width: 1120px) {
        margin-top: 0.5em; // 大屏幕下进一步减少间距
    }

    .reward-preview-header {
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;

        .rewards-preview-image {
            width: 18.75em; // 300px -> 18.75em
            height: 1.44em; // 23px -> 1.44em

            @media (min-width: 640px) {
                width: 25em; // 450px -> 25em
                height: 1.94em; // 35px -> 1.94em
            }

            @media (min-width: 1120px) {
                width: 35.15em; // 703px -> 35.15em
                height: 2.85em; // 57px -> 2.85em
                margin-top: 2.3em; // 46px -> 2.3em
            }
        }
    }

    .reward-preview-list {
        width: 100%;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        gap: 0.63em; // 10px -> 0.63em
        overflow-x: auto;
        margin-top: 1.625em; // 20px -> 1.25em
        padding: 0 0.94em; // 15px -> 0.94em

        @media (min-width: 640px) {
            gap: 0.83em; // 15px -> 0.83em
        }

        @media (min-width: 1120px) {
            justify-content: center;
        }

        .reward-item {
            position: relative;
            flex-shrink: 0;
            padding-top: 0.38em; // 6px -> 0.38em

            .reward-chance-label {
                font-size: 10px;
                position: absolute;
                left: 0;
                top: 0;
                width: 7.8em; // 90px -> 5em
                    height: 1.9em; // 22px -> 1.22em
                color: #553300;
                // font-size: 0.63em; // 10px -> 0.63em
                line-height: 1.9em; // 14px -> 0.88em
                font-weight: 600;
                font-family: 'Open Sans', sans-serif;
                text-align: center;
                background: url('@/assets/free-bonus/chance-top.png') no-repeat;
                background-size: 100% 100%;
                z-index: 1;

                @media (min-width: 640px) {
                    font-size: 12px;
                    width: 7.8em; // 90px -> 5em
                    height: 1.9em; // 22px -> 1.22em
                    // font-size: 0.63em; // 12px -> 0.67em
                    line-height: 1.9em; // 16px -> 0.89em
                }

                @media (min-width: 1120px) {
                    width: 8em; // 114px -> 5.7em
                    height: 1.7em; // 27px -> 1.35em
                    font-size: 0.9em; // 14px -> 0.7em
                    line-height: 1.7em; // 20px -> 1em
                }
            }

            .reward-card {
                width: 5.63em; // 90px -> 5.63em
                height: 8.25em; // 132px -> 8.25em
                border-radius: 0.88em; // 14px -> 0.88em
                padding: 0.38em 0.38em 0.63em; // 6px 6px 10px -> 0.38em 0.38em 0.63em
                border: 1px solid rgba(159, 167, 242, 0.3);
                background: linear-gradient(180deg, rgba(78, 86, 156, 0.8) 0%, rgba(30, 36, 87, 0.9) 100%);
                backdrop-filter: blur(0.625em); // 添加毛玻璃效果
                box-shadow:
                    0 0.25em 1em rgba(0, 0, 0, 0.2),
                    0 0 1.5em rgba(138, 180, 248, 0.1),
                    inset 0 1px 0 rgba(255, 255, 255, 0.1);
                transition: all 0.3s ease;

                @media (min-width: 640px) {
                    width: 5.6em; // 110px -> 6.11em
                    height: 8.3em; // 160px -> 8.89em
                    padding: 0.39em 0.39em 0.39em; // 7px 7px 7px -> 0.39em 0.39em 0.39em
                }

                @media (min-width: 1120px) {
                    width: 8.75em; // 140px -> 7em
                    height: 11.9em; // 190px -> 9.5em
                    padding: 0.4em 0.4em 0.35em; // 8px 8px 7px -> 0.4em 0.4em 0.35em
                    box-shadow:
                        0 0.5em 1.5em rgba(0, 0, 0, 0.3),
                        0 0 2em rgba(138, 180, 248, 0.15),
                        inset 0 1px 0 rgba(255, 255, 255, 0.15);
                }

                // 悬停效果
                &:hover {
                    transform: translateY(-0.25em);
                    border-color: rgba(138, 180, 248, 0.6);
                    box-shadow:
                        0 0.5em 1.5em rgba(0, 0, 0, 0.3),
                        0 0 2em rgba(138, 180, 248, 0.3),
                        inset 0 1px 0 rgba(255, 255, 255, 0.2);

                    @media (min-width: 1120px) {
                        box-shadow:
                            0 0.75em 2em rgba(0, 0, 0, 0.4),
                            0 0 3em rgba(138, 180, 248, 0.4),
                            inset 0 1px 0 rgba(255, 255, 255, 0.25);
                    }
                }

                .reward-image-container {
                    width: 4.88em; // 78px -> 4.88em
                    height: 4.88em; // 78px -> 4.88em
                    border-radius: 0.5em; // 8px -> 0.5em
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    background: linear-gradient(179.23deg, rgba(54, 65, 168, 0.3) 0.7%, rgba(207, 211, 250, 0.3) 99.37%);

                    @media (min-width: 640px) {
                        width: 4.8em; // 96px -> 4.8em
                        height: 4.8em; // 96px -> 5.33em
                    }

                    @media (min-width: 1120px) {
                        width: 7.75em; // 124px -> 7.75em
                        height: 7.75em; // 124px -> 6.2em
                    }

                    .reward-icon {
                        width: 2.5em; // 40px -> 2.5em
                        height: 2.5em; // 40px -> 2.5em

                        @media (min-width: 640px) {
                            width: 3.33em; // 60px -> 3.33em
                            height: 3.33em; // 60px -> 3.33em
                        }

                        @media (min-width: 1120px) {
                            width: 4em; // 80px -> 4em
                            height: 4em; // 80px -> 4em
                        }
                    }
                }

                .reward-text {
                    display: block;
                    text-align: center;
                    color: white;
                    margin-top: 0.5em; // 6px -> 0.38em
                    font-size: 0.75em; // 12px -> 0.75em
                    line-height: 1em; // 16px -> 1em
                    font-weight: 600;

                    @media (min-width: 640px) {
                        margin-top: 0.39em; // 7px -> 0.39em
                        font-size: 0.78em; // 14px -> 0.78em
                        line-height: 1em; // 18px -> 1em
                    }

                    @media (min-width: 1120px) {
                        margin-top: 0.5em; // 8px -> 0.4em
                        font-size: 1em; // 16px -> 0.8em
                        line-height: 1.1em; // 22px -> 1.1em
                    }
                }
            }
        }
    }
}

// 基础圆形背景
.base-circle {
    position: absolute;
    width: 28em; // 进一步增加尺寸
    height: 28em;
    top: 11em; // 调整位置以适应更大的转盘
    left: 50%;
    transform: translateX(-50%);
    z-index: 0;
    opacity: 0.8;
    filter: drop-shadow(0 0 2em rgba(138, 180, 248, 0.3));

    @media (min-width: 640px) {
        width: 34em;
        height: 34em;
        top: 12em;
    }

    @media (min-width: 768px) {
        width: 40em;
        height: 40em;
        top: 14em;
    }

    @media (min-width: 1120px) {
        width: 70em; // 显著增加大屏幕尺寸
        height: 70em;
        top: 20em;
        filter: drop-shadow(0 0 4em rgba(138, 180, 248, 0.4));
    }
}

// 左侧装饰向量
.left-vector {
    position: absolute;
    left: 0;
    width: 6.25em; // 100px -> 6.25em
    height: 18.13em; // 290px -> 18.13em
    opacity: 0.7; // 增加透明度
    z-index: 0;

    @media (min-width: 640px) {
        width: 8.33em; // 150px -> 8.33em
        height: 24.17em; // 435px -> 24.17em
    }

    @media (min-width: 1120px) {
        width: 15em; // 300px -> 15em
        height: 43.5em; // 870px -> 43.5em
        opacity: 0.8;
    }

    &.left-vector-1 {
        top: 0;
    }

    &.left-vector-2 {
        top: 7.38em; // 118px -> 7.38em

        @media (min-width: 640px) {
            top: 9.78em; // 176px -> 9.78em
        }

        @media (min-width: 1120px) {
            top: 17.65em; // 353px -> 17.65em
        }
    }
}

// 右侧装饰向量
.right-vector {
    position: absolute;
    right: 0;
    width: 6.25em; // 100px -> 6.25em
    height: 18.13em; // 290px -> 18.13em
    opacity: 0.7; // 增加透明度
    z-index: 0;

    @media (min-width: 640px) {
        width: 8.33em; // 150px -> 8.33em
        height: 24.17em; // 435px -> 24.17em
    }

    @media (min-width: 1120px) {
        width: 15em; // 300px -> 15em
        height: 43.5em; // 870px -> 43.5em
        opacity: 0.8;
    }

    &.right-vector-1 {
        top: 0;
    }

    &.right-vector-2 {
        top: 7.38em; // 118px -> 7.38em

        @media (min-width: 640px) {
            top: 9.78em; // 176px -> 9.78em
        }

        @media (min-width: 1120px) {
            top: 17.65em; // 353px -> 17.65em
        }
    }
}

// 精确旋转到目标位置的动画
@keyframes spinToTarget {
    0% {
        transform: translate(-50%, -50%) rotateX(-10deg) rotateY(0deg);
    }

    100% {
        transform: translate(-50%, -50%) rotateX(-10deg) rotateY(var(--target-rotation, 0deg));
    }
}

// 不同屏幕尺寸的精确旋转动画
@media (min-width: 640px) and (max-width: 1119px) {
    @keyframes spinToTarget {
        0% {
            transform: translate(-50%, -50%) rotateX(-30deg) rotateY(0deg);
        }

        100% {
            transform: translate(-50%, -50%) rotateX(-30deg) rotateY(var(--target-rotation, 0deg));
        }
    }
}

@media (min-width: 1120px) {
    @keyframes spinToTarget {
        0% {
            transform: translate(-50%, -50%) rotateX(-25deg) rotateY(0deg);
        }

        100% {
            transform: translate(-50%, -50%) rotateX(-25deg) rotateY(var(--target-rotation, 0deg));
        }
    }
}

// 按钮发光动画
@keyframes button-shine {
    0% {
        transform: translateX(-100%) translateY(-100%) rotate(45deg);
    }

    50% {
        transform: translateX(100%) translateY(100%) rotate(45deg);
    }

    100% {
        transform: translateX(-100%) translateY(-100%) rotate(45deg);
    }
}

// 卡片发光动画 - 增强效果
@keyframes card-glow {

    0%,
    100% {
        filter: drop-shadow(0 0 0.75em rgba(138, 180, 248, 0.4));
    }

    50% {
        filter: drop-shadow(0 0 1.5em rgba(138, 180, 248, 0.8));
    }
}

// 为大屏幕添加更强的发光动画
@media (min-width: 1120px) {
    @keyframes card-glow {

        0%,
        100% {
            filter: drop-shadow(0 0 1.25em rgba(138, 180, 248, 0.6));
        }

        50% {
            filter: drop-shadow(0 0 2.5em rgba(138, 180, 248, 0.9));
        }
    }
}
</style>
