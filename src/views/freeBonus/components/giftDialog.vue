<!-- 取消订单二次确认弹窗 -->
<template>
    <div class="">
        <el-dialog :model-value="dialogGiftVisible" @update:model-value="$emit('update:dialogGiftVisible', $event)"
            :close-on-click-modal="false" :before-close="handleRuleClose" :show-close="false"
            class="h-max w-[300px] rounded-[13px] md:w-[500px] p-0">
            <div class="gift-header w-full h-[76px] flex justify-center items-center relative md:h-[126px]">
                <img src="@/assets/common/gift-header.png" alt=""
                    class="gift-header-img absolute h-full top-[6px] md:top-[10px]">
            </div>
            <div
                class="gift-content py-[24px] px-[20px] rounded-[22px] border border-solid border-[#9FA7F2] md:px-[37px] md:py-[30px]">
                <div
                    class="gift-title font-['Philosopher'] text-[20px] leading-[22px] text-white text-center font-bold md:text-[26px] md:leading-[28px]">
                    Daily Check-In Reward</div>
                <div class="gift-detail flex flex-col jusitift-center items-center mt-[20px] md:mt-[40px]">
                    <img :src="luckyGift.awardIcon" 
                        class="w-[72px] h-[72px] md:w-[120px]  md:h-[120px]">
                    <span
                        class="mt-[10px] text-[14px] font-semibold leading-[19px] text-white md:text-[18px] md:leading-[25px] md:mt-[21px]">
                        {{ luckyGift.awardName }} X {{ luckyGift.awardNum }}</span>
                </div>
                <!--确认按钮-->
                <div class="confirm-btn px-[22px] mt-[20px] md:px-[33px] md:mt-[40px]">
                    <div
                        class="confirm-btn-text w-full h-[44px] rounded-[22px] bg-[#4484FF] text-[#1A1C29] text-[16px] leading-[44px] text-center font-bold font-['Philosopher'] md:h-[50px] md:text-[18px] md:leading-[50px]">
                        Use Now</div>
                </div>
            </div>
            <div class="mt-[20px] flex justify-center">
                <img src="@/assets/common/close-dialog.png" alt="" class="close-dialog w-[32px] h-[32px] cursor-pointer"
                    @click="handleRuleClose">
            </div>

        </el-dialog>
    </div>
</template>

<script lang="ts" setup name="GiftDialog">
import { ref } from 'vue'
const emit = defineEmits(['update:dialogGiftVisible'])
const props = defineProps({
    dialogGiftVisible: {
        type: Boolean,
        default: false,
    },
    luckyGift:{
        type:Object,
        default:()=>{
            return {}
        }
    }
})
const handleRuleClose = () => {
    emit('update:dialogGiftVisible', false)
}
</script>
<style lang="scss" scoped>
:deep(.el-dialog) {
    padding: 0;
    display: flex;
    flex-direction: column;
    background: none;
    box-shadow: none;

    .el-dialog__header {
        display: none;
    }

    .el-dialog__body {
        flex: 1;
        padding: 0 !important;

        .gift-header-img {
            // animation: rotateImg 3s linear infinite;
        }

        @keyframes rotateImg {
            0% {
                transform: rotate(0deg)
            }

            100% {
                transform: rotate(360deg)
            }
        }

        .gift-content {
            width: 100%;
            height: 100%;
            background: linear-gradient(180deg, #282b49 0%, #1a1f4c 100%);

            .gift-title {
                background: linear-gradient(180deg, #FFED8B 0%, #FFFFFF 100%);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
            }

            .confirm-btn-text {
                background: linear-gradient(180deg, #F6CD83 0%, #FBEFB5 100%);

            }
        }

        .close-dialog {
            justify-content: b;
        }
    }
}
</style>
