<!-- 取消订单二次确认弹窗 -->
<template>
	<div class="">
		<el-dialog :model-value="dialogRuleVisible" @update:model-value="$emit('update:dialogRuleVisible', $event)" :close-on-click-modal="false" :before-close="handleRuleClose" :show-close="false" class="h-max w-[300px] rounded-[13px] md:w-[500px] p-0">
			<div class="rule-content py-[24px] px-[20px] rounded-[22px] border border-solid border-[#9FA7F2] md:px-[37px] md:py-[30px]">
				<div class="rule-title font-['Philosopher'] text-[20px] leading-[22px] text-white text-center font-bold md:text-[26px] md:leading-[28px]">Rules</div>
				<div class="rule-list mt-[10px] md:mt-[18px]">
					<div class="rule-list-item flex mt-[12px] md:mt-[16px]">
						<div class="rule-list-item-num shrink-0 w-[19px] h-[19px] rounded-[4px] bg-[#4484FF] text-white text-[12px] leading-[18px] text-center md:text-[13px]">1</div>
						<div class="text-[14px] leading-[18px] text-white ml-[8px] md:text-[16px] md:leading-[20px]">Win free gifts daily through daily check-in.</div>
					</div>
					<div class="rule-list-item flex mt-[12px] md:mt-[16px]">
						<div class="rule-list-item-num shrink-0 w-[19px] h-[19px] rounded-[4px] bg-[#4484FF] text-white text-[12px] leading-[18px] text-center md:text-[13px]">2</div>
						<div class="text-[14px] leading-[18px] text-white ml-[8px] md:text-[16px] md:leading-[20px]">The timer resets at 24:00:00 every day. Missing a day means you'll have to start over.</div>
					</div>
					<div class="rule-list-item flex mt-[12px] md:mt-[16px]">
						<div class="rule-list-item-num shrink-0 w-[19px] h-[19px] rounded-[4px] bg-[#4484FF] text-white text-[12px] leading-[18px] text-center md:text-[13px]">3</div>
						<div class="text-[14px] leading-[18px] text-white ml-[8px] md:text-[16px] md:leading-[20px]">If you have any questions during the event, please contact Team Official.</div>
					</div>
				</div>
                <!--确认按钮-->
                <div class="confirm-btn px-[22px] mt-[20px] md:px-[33px] md:mt-[40px] cursor-pointer" @click="handleRuleClose">
                    <div class="confirm-btn-text w-full h-[44px] rounded-[22px] bg-[#4484FF] text-[#1A1C29] text-[16px] leading-[44px] text-center font-bold font-['Philosopher'] md:h-[50px] md:text-[18px] md:leading-[50px]">Confirm</div>
                </div>
			</div>
            <div class="mt-[20px] flex justify-center">
                <img src="@/assets/common/close-dialog.png" alt="" class="close-dialog w-[32px] h-[32px] cursor-pointer" @click="handleRuleClose">
            </div>
            
		</el-dialog>
	</div>
</template>

<script lang="ts" setup name="RuleDialog">
import { ref } from 'vue'
const emit = defineEmits(['update:dialogRuleVisible'])
const props = defineProps({
	dialogRuleVisible: {
		type: Boolean,
		default: false,
	},
})
const handleRuleClose = () => {
	emit('update:dialogRuleVisible', false)
}
</script>
<style lang="scss" scoped>
:deep(.el-dialog) {
	padding: 0;
	display: flex;
	flex-direction: column;
	background: none;
	.el-dialog__header {
		display: none;
	}
	.el-dialog__body {
		flex: 1;
		padding: 0 !important;
		.rule-content {
			width: 100%;
			height: 100%;
			background: linear-gradient(180deg, #282b49 0%, #1a1f4c 100%);
            .confirm-btn-text{
                background: linear-gradient(180deg, #F6CD83 0%, #FBEFB5 100%);

            }
		}
        .close-dialog{
           justify-content: b;
        }
	}
}
</style>
