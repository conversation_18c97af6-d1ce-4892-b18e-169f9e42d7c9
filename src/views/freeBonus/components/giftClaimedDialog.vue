<!-- 取消订单二次确认弹窗 -->
<template>
    <div class="">
        <el-dialog :model-value="dialogGiftClaimendVisible"
            @update:model-value="$emit('update:dialogGiftClaimend', $event)" :close-on-click-modal="false"
            :before-close="handleRuleClose" :show-close="false" class=" w-[300px] rounded-[13px] md:w-[500px] p-0">

            <div
                class="gift-content flex flex-col py-[24px] px-[20px] rounded-[22px] border border-solid border-[#9FA7F2] md:px-[37px] h-[305px] md:py-[30px] md:h-[380px] md:max-h-[380px] overflow-hidden">
                <div
                    class="gift-title font-['Philosopher'] text-[20px] leading-[22px] text-white text-center font-bold md:text-[26px] md:leading-[28px]">
                    Gifts Claimed</div>
                <div class="gift-list mt-[15px]  flex-1 overflow-hidden md:mt-[30px] pb-[10px]">
                    <LoadMore :loading="loading" :finished="finished" :threshold="120" :throttle="300"
                        @loadMore="loadMoreFunc" @loadEnd="onLoadEnd">
                        <div class="gift-item flex justify-between items-center pb-[12px] mt-[12px]  border-b border-solid border-[#4d506e] md:pb-[14px] md:mt-[14px]"
                            v-for="item in list" :key="item">
                            <div class="gift-item-left flex items-center">
                                <img :src="item.awardIcon" alt=""
                                    class="w-[40px] h-[40px] mr-[8px] md:w-[50px] md:h-[50px] md:mr-[10px]">
                                <div>
                                    <div
                                        class="font-['Philosopher'] text-[#FFFFFF] text-[15px] leading-[17px] font-bold md:text-[16px]">
                                        {{ item.title }}
                                    </div>
                                    <div class="text-[#FFFFFFB2] text-[13px] leading-[16px] mt-[4px] md:mt-[7px]">
                                        {{ item.subTitle }}
                                    </div>
                                </div>
                            </div>
                            <div class="gift-item-right font-[13px] leading-[16px] text-[#FFFFFF66] text-right">
                                <div class="gift-item-right-time">
                                    {{ formatTimestampToString(item.timestamp).substring(12) }}
                                </div>
                                <div class="gift-item-right-date">
                                    {{ formatTimestampToString(item.timestamp).substring(0,12) }}
                                </div>
                            </div>
                        </div>
                    </LoadMore>

                </div>
            </div>
            <div class="mt-[20px] flex justify-center">
                <img src="@/assets/common/close-dialog.png" alt="" class="close-dialog w-[32px] h-[32px] cursor-pointer"
                    @click="handleRuleClose">
            </div>

        </el-dialog>
    </div>
</template>

<script lang="ts" setup name="GiftClaimedDialog">
import { ref, watch } from 'vue'
import LoadMore from '@/components/LoadMore.vue'
import { getLuckyDrawRewardList } from '@/api/user'
import {formatTimestampToString} from '@/utils'
const emit = defineEmits(['update:dialogGiftClaimend'])
const props = defineProps({
    dialogGiftClaimendVisible: {
        type: Boolean,
        default: false,
    },
})
const loading = ref(false)
const finished = ref(false)
const current = ref(1)
const list = ref<any[]>([])
const loadMoreFunc = () => {
    loading.value = true
    getLuckyDrawRewardList({ current: current.value, size: 10 }).then(response => {
        list.value = [...list.value, ...response.data]
        current.value++
        if (list.value.length >= response.count) finished.value = true
    }).catch(() => {
        finished.value = true
    }).finally(() => {
        loading.value = false
    })
}
const onLoadEnd = () => {
    finished.value = true
}
const handleRuleClose = () => {
    emit('update:dialogGiftClaimend', false)
}

watch(() => props.dialogGiftClaimendVisible, (newVal) => {
    if (newVal) {
        current.value = 1
        finished.value = false
        list.value.length = 0
        loadMoreFunc()
    }
})
</script>
<style lang="scss" scoped>
:deep(.el-dialog) {
    padding: 0;
    display: flex;
    flex-direction: column;
    background: none;

    .el-dialog__header {
        display: none;
    }

    .el-dialog__body {
        flex: 1;
        padding: 0 !important;

        .gift-content {
            width: 100%;
            // height: 100%;
            background: linear-gradient(180deg, #282b49 0%, #1a1f4c 100%);

            .confirm-btn-text {
                background: linear-gradient(180deg, #F6CD83 0%, #FBEFB5 100%);

            }
        }

        .close-dialog {
            justify-content: b;
        }
    }
}
</style>
