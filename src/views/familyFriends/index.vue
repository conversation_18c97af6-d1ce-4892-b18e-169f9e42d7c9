<!--  -->
<template>
    <div class="app-container" v-loading="pageLoading" element-loading-background="rgba(122, 122, 122, 0.8)"
        ref="appContainer">
        <!-- 吸顶 observe 元素，动态 top 距离 Header -->
        <StickyHeader :textReading="textReading" :liveTextChat="liveTextChat" :augurDetail="augurDetail"
            :showFree="showFree" :showObserve="showObserve" @createOrder="createOrder" />
        <div class="main-content px-[15px] lg:px-[120px]">
            <el-breadcrumb :separator-icon="ArrowRight"
                class="font-['Open_Sans_3'] font-[600] text-[14px] pt-[8px] mb-[14px] md:pt-6 lg:pt-6 md:mb-[30px] lg:mb-[30px]">
                <el-breadcrumb-item class="text-[#1C2158]">Home</el-breadcrumb-item>
                <el-breadcrumb-item class="text-[#1C2158]">Family &Friends</el-breadcrumb-item>
                <el-breadcrumb-item>{{ augurDetail.username }}</el-breadcrumb-item>
            </el-breadcrumb>
            <div
                class="md:bg-white lg:bg-white md:px-[40px] md:pb-[70px] md:pt-[40px] lg:px-[40px] lg:pb-[70px] lg:pt-[40px] rounded-[30px]">
                <div class="family-friends rounded-xl md:hidden lg:hidden" id="family-friends">
                    <!--具体的占卜师信息-->
                    <div class="family-friends-box relative w-full h-[345px]">
                        <Loading v-if="loadingImg" class="absolute inset-0 flex items-center justify-center"
                            :loading="true" />
                        <img v-if="augurDetail.avatar" v-show="!loadingImg" :src="augurDetail.avatar" alt=""
                            class="h-[345px] w-full object-cover object-top rounded-tl-xl rounded-tr-xl"
                            @load="loadingImg = false" @error="loadingImg = false" />
                        <div class="absolute inset-0 flex flex-col justify-between p-4 w-full">
                            <div class="top flex items-center justify-between w-full">
                                <img src="@/assets/common/not-like.png" alt="" class="w-[30px] h-[30px] cursor-pointer"
                                    v-loading="loading" v-if="!augurDetail.isFollowed"
                                    @click="handleFollowAugur(true)" />
                                <img src="@/assets/common/like.png" alt="" class="w-[30px] h-[30px] cursor-pointer"
                                    v-loading="loading" v-else @click="handleFollowAugur(false)" />
                                <div class="online border border-solid border-white w-3.5 h-3.5 bg-[#3AD953] rounded-[50%]"
                                    v-if="augurDetail.state == 4 "></div>
                                <div class="offline border border-solid border-white w-3.5 h-3.5 bg-[#BFC3C5] rounded-[50%]"
                                    v-else-if="augurDetail.state == 1 || augurDetail.state == 2"></div>
                                <div class="busy border border-solid border-white w-3.5 h-3.5 bg-[#EA3232] rounded-[50%]"
                                    v-else-if="augurDetail.state == 3"></div>
                            </div>
                            <div class="bottom">
                                <p class="text-[24px] font-['Philosopher'] font-bold text-white">{{ augurDetail.username
                                }}</p>
                                <p class="font-['Open_Sans_3'] text-[14px] mt-[7px] text-white">{{
                                    augurDetail.introduction }}</p>
                                <SmRate :modelValue="augurDetail.grade.toFixed(1)" :disabled="true" 
                                    class="mt-[5px]"></SmRate>
                            </div>
                        </div>
                    </div>
                    <!--占卜师信息-->
                    <div
                        class="family-friends-detail py-[16px] flex justify-around bg-white rounded-bl-xl rounded-br-xl">
                        <div class="family-friends-detail-item">
                            <p class="text-center font-['Open_Sans_3'] text-[18px] font-semibold">{{
                                augurDetail.commentTimes }}</p>
                            <span class="block mt-[9px] text-xs text-normal-opacity-60">Readings</span>
                        </div>
                        <div class="family-friends-detail-item">
                            <p class="text-center font-['Open_Sans_3'] text-[18px] font-semibold">{{
                                getServeiceHours(augurDetail.fortuneDuration) + 'hr' }}</p>
                            <span class="block mt-[9px] text-xs text-normal-opacity-60">Service Hours</span>
                        </div>
                        <div class="family-friends-detail-item">
                            <p class="text-center font-['Open_Sans_3'] text-[18px] font-semibold">{{
                                augurDetail.employmentAge }}</p>
                            <span class="block mt-[9px] text-xs text-normal-opacity-60">Exp（years）</span>
                        </div>
                    </div>
                    <div class="family-friends-button">
                        <button class="free-button relative flex justify-between py-[12px] pr-[12px] mt-[15px]"
                            v-if="liveTextChat?.coins == 0"
                            @click="createOrder({ augurFortuneGroupId: liveTextChat.augurFortuneGroupId, type: 'liveTextChat' })">
                            <div class="flex items-center">
                                <img src="@/assets/common/free-chart.png" alt="" class="chart-img w-[60px]" />
                                <span class="font-['Open_Sans_3'] text-[18px] font-semibold whitespace-nowrap">Live Text
                                    Chat</span>
                            </div>
                            <div class="free-button-text w-[90px] rounded-[8px] h-full flex items-center justify-center font-['Philosopher'] font-bold text-[24px] text-white"
                                :class="{ 'busy-button': liveTextChat.status === 0 || augurDetail.state != 4 }">
                                Free</div>

                        </button>
                        <button class="text-button relative mt-[10px] flex justify-between py-[12px] pr-[12px]" v-else
                            @click="createOrder({ augurFortuneGroupId: liveTextChat.augurFortuneGroupId, type: 'liveTextChat' })">
                            <div class="flex items-center">
                                <img src="@/assets/common/live-text-chat.png" alt="" class="chart-img w-[60px]" />
                                <div class="flex flex-col ml-[7px]">
                                    <p class="text-[18px] font-['Open_Sans_3'] font-semibold">Live Text Chat</p>
                                    <p class="text-[14px] font-['Open_Sans_3'] opacity-50 mt-[1px] leading-[18px] text-left">Deliver within 1h
                                    </p>
                                </div>
                            </div>
                            <div class="text-button-text w-[90px] h-full rounded-[8px] flex flex-col items-center"
                                :class="{'busy-button': liveTextChat.status === 0 || augurDetail.state != 4 }">
                                <div class="flex items-center h-full justify-center">
                                    <span class="text-[24px] font-['Philosopher'] font-bold text-[#FFEB39] mr-1">{{
                                        liveTextChat.discountCoins < liveTextChat.coins ? liveTextChat.discountCoins :
                                            liveTextChat.coins }}</span>
                                            <img src="@/assets/common/money.png" alt="" class="w-[16px] h-[16px]" />
                                </div>
                                <div class="line-through text-[#fff] text-xs mt-0.5 opacity-60"
                                    v-if="liveTextChat.discountCoins < liveTextChat.coins">{{ liveTextChat.coins }}
                                </div>
                            </div>
                            <div class="discount-top-sm w-[55px] text-[11px] h-[19px] flex top-[-7px] right-[-5px]  font-['Lora'] text-[white]  justify-center leading-[16px]"
                                v-if="liveTextChat.discountCoins < liveTextChat.coins">
                                {{ (liveTextChat.coins - liveTextChat.discountCoins) / liveTextChat.coins * 100 }}% OFF
                            </div>
                            <div class="absolute w-[38px] h-[38px] top-0 left-0" v-if="augurDetail.state===3">
                                <img src="@/assets/common/busy.png" class="w-full h-full">
                            </div>
                        </button>
                        <button class="free-button flex justify-between py-[12px] pr-[12px] mt-[15px]"
                            v-if="textReading.coins == 0"
                            @click="createOrder({ augurFortuneGroupId: textReading.augurFortuneGroupId, type: 'textReading' })">
                            <div class="flex items-center">
                                <img src="@/assets/common/free-chart.png" alt="" class="chart-img w-[60px]" />
                                <span class="font-['Open_Sans_3'] text-[18px] font-semibold whitespace-nowrap">Text
                                    Reading</span>
                            </div>
                            <div class="free-button-text w-[90px]  rounded-[8px] h-full flex items-center justify-center font-['Philosopher'] font-bold text-[24px] text-white"
                                :class="{ 'busy-button': textReading.status === 0 }">Free</div>
                        </button>
                        <button class="text-button relative mt-[10px] flex justify-between py-[12px] pr-[12px]" v-else
                            @click="createOrder({ augurFortuneGroupId: textReading.augurFortuneGroupId, type: 'textReading' })">
                            <div class="flex items-center">
                                <img src="@/assets/common/text.png" alt="" class="chart-img w-[60px]" />
                                <div class="flex flex-col ml-[7px]">
                                    <p class="text-[18px] font-['Open_Sans_3'] font-semibold">Text Reading</p>
                                    <p class="text-[14px] font-['Open_Sans_3'] opacity-50 mt-[1px] leading-[18px] text-left">Deliver within 1h
                                    </p>
                                </div>
                            </div>
                            <div class="text-button-text w-[90px] h-full rounded-[8px] flex flex-col items-center"
                                :class="{ 'busy-button': textReading.status === 0 }">
                                <div class="flex items-center h-full justify-center">
                                    <span class="text-[24px] font-['Philosopher'] font-bold text-[#FFEB39] mr-1">{{
                                        textReading.discountCoins < textReading.coins ? textReading.discountCoins :
                                            textReading.coins }}</span>
                                            <img src="@/assets/common/money.png" alt="" class="w-[16px] h-[16px]" />
                                </div>
                                <div class="line-through text-[#fff] text-xs mt-0.5 opacity-60"
                                    v-if="textReading.discountCoins < textReading.coins">{{ textReading.coins }}</div>
                            </div>
                            <div class="discount-top-sm w-[55px] text-[11px] h-[19px] flex top-[-7px] right-[-5px]  font-['Lora'] text-[white]  justify-center leading-[16px]"
                                v-if="textReading.discountCoins < textReading.coins">
                                {{ (textReading.coins - textReading.discountCoins) / textReading.coins * 100 }}% OFF
                            </div>
                        </button>
                    </div>
                </div>
                <!--在lg 和md 的情况下展示-->
                <div class="family-friends rounded-xl hidden md:block lg:block" id="family-friends-lg">
                    <div class="flex">
                        <div class="left relative w-max shrink-0">
                            <Loading v-if="loadingImg" class="absolute inset-0 flex items-center justify-center"
                                :loading="true" />
                            <img v-if="augurDetail.avatar" v-show="!loadingImg" :src="augurDetail.avatar" alt=""
                                class="shrink-0 w-[240px] h-[240px] object-cover object-top rounded-[20px]"
                                @load="loadingImg = false" @error="loadingImg = false" />
                            <!-- <img :src="augurDetail.avatar" alt="" class="shrink-0 w-[240px] h-[240px] object-cover object-top rounded-[20px]" /> -->
                            <div class="offline absolute top-[15px] right-[15px] w-[20px] h-[20px] rounded-[20px] bg-[#BFC3C5] border-2 border-white border-solid"
                                v-if="augurDetail.state == 1 || augurDetail.state == 2"></div>
                            <div class="online absolute top-[15px] right-[15px] w-[20px] h-[20px] rounded-[20px] bg-[#3AD953] border-2 border-white border-solid"
                                v-else-if="augurDetail.state == 4 "></div>
                            <div class="busy absolute top-[15px] right-[15px] w-[20px] h-[20px] rounded-[20px] bg-[#EA3232] border-2 border-white border-solid"
                                v-else-if="augurDetail.state == 3"></div>
                        </div>
                        <!--中间的具体解释和时间信息-->
                        <div class="bottom flex flex-col justify-between ml-5 flex-1">
                            <div>
                                <p class="text-[34px] font-['Philosopher'] font-bold text-[#1C2158]">{{
                                    augurDetail.username }}</p>
                                <p
                                    class="font-['Open_Sans_3'] text-[22px] mt-[7px] text-[#1C2158] opacity-80 leading-[30px] lg:mt-[10px]">
                                    {{ augurDetail.introduction }}</p>
                                <SmRate :modelValue="augurDetail.grade.toFixed(1)" :disabled="true" class="mt-[16px]">
                                </SmRate>
                            </div>
                            <div
                                class="family-friends-detail py-[16px] flex justify-between bg-white rounded-bl-xl rounded-br-xl">
                                <div class="family-friends-detail-item">
                                    <p class="text-center font-['Open_Sans_3'] text-[24px] font-semibold">{{
                                        augurDetail.commentTimes }}</p>
                                    <span class="block mt-[9px] text-base opacity-60">Readings</span>
                                </div>
                                <div class="family-friends-detail-item">
                                    <p class="text-center font-['Open_Sans_3'] text-[24px] font-semibold">{{
                                        getServeiceHours(augurDetail.fortuneDuration) + 'hr' }}</p>
                                    <span class="block mt-[9px] text-base opacity-60">Service Hours</span>
                                </div>
                                <div class="family-friends-detail-item">
                                    <p class="text-center font-['Open_Sans_3'] text-[24px] font-semibold">{{
                                        augurDetail.employmentAge }}</p>
                                    <span class="block mt-[9px] text-base opacity-60">Exp（years）</span>
                                </div>
                            </div>
                        </div>
                        <!--按钮-->
                        <div class="right flex shrink-0 flex-col ml-[40px] items-end">

                            <div class="flex items-center gap-[36px]">
                                <!--不喜欢-->
                                <svg width="28" height="24" viewBox="0 0 28 24" fill="none"
                                    xmlns="http://www.w3.org/2000/svg" v-if="!augurDetail.isFollowed"
                                    @click="handleFollowAugur(true)">
                                    <foreignObject x="-2.1917" y="-2.19242" width="32.3834" height="28.3931">
                                        <div xmlns="http://www.w3.org/1999/xhtml"
                                            style="backdrop-filter: blur(1.41px); clip-path: url(#bgblur_0_3478_47002_clip_path); height: 100%; width: 100%">
                                        </div>
                                    </foreignObject>
                                    <path data-figma-bg-blur-radius="2.82842"
                                        d="M12.5 3.49994C10.0147 1.01469 5.9853 1.01469 3.50003 3.49994V3.49994C1.01474 5.98521 1.01474 10.0147 3.50003 12.4999L12.9286 21.9284C13.5203 22.5201 14.4797 22.5202 15.0714 21.9284L24.5 12.4999C26.9853 10.0147 26.9853 5.98521 24.5 3.49994V3.49994C22.0147 1.01469 17.9853 1.01469 15.5 3.49994L14 4.99996L12.5 3.49994Z"
                                        stroke="#1C2158" stroke-width="2" />
                                    <defs>
                                        <clipPath id="bgblur_0_3478_47002_clip_path"
                                            transform="translate(2.1917 2.19242)">
                                            <path
                                                d="M12.5 3.49994C10.0147 1.01469 5.9853 1.01469 3.50003 3.49994V3.49994C1.01474 5.98521 1.01474 10.0147 3.50003 12.4999L12.9286 21.9284C13.5203 22.5201 14.4797 22.5202 15.0714 21.9284L24.5 12.4999C26.9853 10.0147 26.9853 5.98521 24.5 3.49994V3.49994C22.0147 1.01469 17.9853 1.01469 15.5 3.49994L14 4.99996L12.5 3.49994Z" />
                                        </clipPath>
                                    </defs>
                                </svg>
                                <!--喜欢-->
                                <svg width="30" height="30" viewBox="0 0 30 30" fill="none"
                                    xmlns="http://www.w3.org/2000/svg" v-else @click="handleFollowAugur(false)">
                                    <foreignObject x="-0.191699" y="2.80758" width="30.3834" height="26.3931">
                                        <div xmlns="http://www.w3.org/1999/xhtml"
                                            style="backdrop-filter: blur(1.41px); clip-path: url(#bgblur_0_3478_46986_clip_path); height: 100%; width: 100%">
                                        </div>
                                    </foreignObject>
                                    <path data-figma-bg-blur-radius="2.82842"
                                        d="M13.5 7.49994C11.0147 5.01469 6.9853 5.01469 4.50003 7.49994C2.01474 9.98521 2.01474 14.0147 4.50003 16.4999L13.9286 25.9284C14.5203 26.5201 15.4797 26.5202 16.0714 25.9284L25.5 16.4999C27.9853 14.0147 27.9853 9.98521 25.5 7.49994C23.0147 5.01469 18.9853 5.01469 16.5 7.49994L15 8.99996L13.5 7.49994Z"
                                        fill="#FF525C" />
                                    <defs>
                                        <clipPath id="bgblur_0_3478_46986_clip_path"
                                            transform="translate(0.191699 -2.80758)">
                                            <path
                                                d="M13.5 7.49994C11.0147 5.01469 6.9853 5.01469 4.50003 7.49994C2.01474 9.98521 2.01474 14.0147 4.50003 16.4999L13.9286 25.9284C14.5203 26.5201 15.4797 26.5202 16.0714 25.9284L25.5 16.4999C27.9853 14.0147 27.9853 9.98521 25.5 7.49994C23.0147 5.01469 18.9853 5.01469 16.5 7.49994L15 8.99996L13.5 7.49994Z" />
                                        </clipPath>
                                    </defs>
                                </svg>
                                <button
                                    class="send-message h-[46px] w-[168px] white-norwap flex items-center justify-center p-[5px] border-[1px] border-solid border-[#4484FF] rounded-[6px]">
                                    <img src="@/assets/common/send-message.png" class="w-[20px] h-[20px] ">
                                    <span
                                        class="send-message-text text-[18px] font-['Philosopher'] font-[600] ml-[4px] leading-[20px]"
                                        @click="sendMessage">send
                                        Message</span>
                                </button>
                            </div>
                            <!--liveTextChat-->
                            <button class="lg-free-button w-full py-[10px] pr-[20px] pl-[10px] mt-[30px]"
                                v-if="liveTextChat.coins == 0"
                                @click="createOrder({ augurFortuneGroupId: liveTextChat.augurFortuneGroupId, type: 'liveTextChat' })">
                                <img src="@/assets/common/free-chart.png" alt="" class="chart-img w-[60px]" />
                                <span class="font-['Open_Sans_3'] text-[22px] font-semibold whitespace-nowrap">Live Text
                                    Chat</span>
                                <div class="free-button-text ml-[18px] rounded-[8px] h-full flex items-center justify-center font-['Philosopher'] font-bold text-[24px] text-white"
                                    :class="{ 'busy-button': liveTextChat.status === 0 || augurDetail.state != 4 }">
                                    Free</div>
                            </button>
                            <button
                                class="lg-text-button relative mt-[15px] flex justify-between py-[10px] pr-[20px] pl-[10px]"
                                v-else
                                @click="createOrder({ augurFortuneGroupId: liveTextChat.augurFortuneGroupId, type: 'liveTextChat' })">
                                <div class="flex items-center">
                                    <img src="@/assets/common/live-text-chat.png" alt="" class="chart-img w-[60px]" />
                                    <div class="flex flex-col ml-[7px] text-left">
                                        <p class="text-[22px] font-['Open_Sans_3'] font-semibold">Live Text Chat</p>
                                        <p class="text-[14px] font-['Open_Sans_3'] opacity-50 mt-[3px]">Deliver within
                                            1h</p>
                                    </div>
                                </div>
                                <div class="text-button-text w-[90px] h-full rounded-[8px] flex flex-col justify-center items-center"
                                    :class="{ 'busy-button': liveTextChat.status === 0 || augurDetail.state != 4 }">
                                    <div class="flex items-center h-full justify-center">
                                        <span class="text-[24px] font-['Philosopher'] font-bold text-[#FFEB39] mr-1">{{
                                            liveTextChat.discountCoins < liveTextChat.coins ? liveTextChat.discountCoins
                                                : liveTextChat.coins }}</span>
                                                <img src="@/assets/common/money.png" alt="" class="w-[20px] h-[20px]" />
                                    </div>
                                    <div class="line-through text-[#fff] text-xs mt-0.5 opacity-60"
                                        v-if="liveTextChat.discountCoins < liveTextChat.coins">{{ liveTextChat.coins }}
                                    </div>
                                </div>
                                <div class="discount-top w-[75px] h-[26px] top-[-5px] left-[-6px] flex justify-center leading-[20px] text-[#FBFBFC] text-[15px] font-['Lora']"
                                    v-if="liveTextChat.discountCoins < liveTextChat.coins">
                                    {{ ((liveTextChat.coins - liveTextChat.discountCoins) / liveTextChat.coins) * 100
                                    }}% OFF
                                </div>
                            </button>
                            <!----->
                            <button class="lg-free-button w-full py-[10px] pr-[20px] pl-[10px] mt-[30px]"
                                v-if="textReading.coins == 0"
                                @click="createOrder({ augurFortuneGroupId: textReading.augurFortuneGroupId, type: 'textReading' })">
                                <img src="@/assets/common/free-chart.png" alt="" class="chart-img w-[60px]" />
                                <span class="font-['Open_Sans_3'] text-[22px] font-semibold whitespace-nowrap">Text
                                    Reading</span>
                                <div class="free-button-text ml-[18px] rounded-[8px] h-full flex items-center justify-center font-['Philosopher'] font-bold text-[24px] text-white"
                                    :class="{ 'busy-button': textReading.status === 0 }">Free</div>
                            </button>
                            <button
                                class="lg-text-button relative mt-[15px] flex justify-between py-[10px] pr-[20px] pl-[10px]"
                                v-else
                                @click="createOrder({ augurFortuneGroupId: textReading.augurFortuneGroupId, type: 'textReading' })">
                                <div class="flex items-center">
                                    <img src="@/assets/common/text.png" alt="" class="chart-img w-[60px]" />
                                    <div class="flex flex-col ml-[7px] text-left">
                                        <p class="text-[22px] font-['Open_Sans_3'] font-semibold">Text Reading</p>
                                        <p class="text-[14px] font-['Open_Sans_3'] opacity-50 mt-[3px]">Deliver within
                                            1h</p>
                                    </div>
                                </div>
                                <div class="text-button-text w-[90px] h-full rounded-[8px] flex flex-col justify-center items-center"
                                    :class="{ 'busy-button': textReading.status === 0 }">
                                    <div class="flex items-center h-full justify-center">
                                        <span class="text-[24px] font-['Philosopher'] font-bold text-[#FFEB39] mr-1">{{
                                            textReading.discountCoins < textReading.coins ? textReading.discountCoins :
                                                textReading.coins }}</span>
                                                <img src="@/assets/common/money.png" alt="" class="w-[20px] h-[20px]" />
                                    </div>
                                    <div class="line-through text-[#fff] text-xs mt-0.5 opacity-60"
                                        v-if="textReading.discountCoins < textReading.coins">{{ textReading.coins }}
                                    </div>
                                </div>
                                <div class="discount-top  w-[75px] h-[26px] top-[-5px] left-[-6px] flex justify-center leading-[20px] text-[#FBFBFC] text-[15px] font-['Lora']"
                                    v-if="textReading.discountCoins < textReading.coins">
                                    {{ (textReading.coins - textReading.discountCoins) / textReading.coins * 100 }}% OFF
                                </div>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="hidden h-[30px] mt-[60px] mb-[33px] md:block lg:block relative">
                    <img src="@/assets/common/lg-line.png" alt=""
                        class="w-[600px] h-[29px] absolute left-0 right-0 mx-auto" />
                </div>
                <!--about-->
                <div class="about mt-6 text-[#1C2158]">
                    <div class="about-title text-[18px] font-blod font-['Philosopher'] md:text-[28px] lg:text-[28px]">
                        About</div>
                    <div
                        class="about-content mt-[10px] text-[14px] font-['Source_Sans_3'] md:text-[20px] md:mt-[18px] lg:text-[20px] lg:mt-[18px]">
                        {{ augurDetail.about }}
                    </div>
                </div>
                <!--Service Category-->
                <div class="about mt-6 text-[#1C2158] md:mt-[63px] lg:mt-[63px]">
                    <div
                        class="about-title text-[18px] font-blod font-['Philosopher'] mb-[12px] md:text-[28px] lg:text-[28px]">
                        Service Category</div>
                    <div class="ablout-list flex gap-2 flex-wrap">
                        <div class="aboout-item flex items-center py-[6px] px-[8px] rounded-[8px] bg-[#fff] border border-[#ededf2] border-solid lg:px-[10px] lg:py-[8px]"
                            v-for="item in augurDetail.tags">
                            <img :src="item.icon" alt="" class="w-[24px] h-[24px] lg:w-[30px] lg:h-[30px]" />
                            <span class="text-[14px] ml-1.5 lg:text-[18px]">{{ item.name }}</span>
                        </div>
                    </div>
                </div>
                <!--Reviews-->
                <div class="reviews mt-6 text-[#1C2158] md:mt-[63px] lg:mt-[63px]">
                    <div
                        class="reviews-title text-[18px] font-blod font-['Philosopher'] mb-[12px] md:text-[28px] lg:text-[28px]">
                        Reviews <span class="text-[14px]">({{ reviewsCount }})</span></div>
                    <div class="reviews-list">
                        <div class="reviews-item p-[10px] rounded-[10px] bg-[#fff] mt-[10px] md:border md:border-[#ededf2] md:border-solid md:mt-[16px] md:p-[30px] lg:border lg:border-[#ededf2] lg:border-solid lg:mt-[16px] lg:p-[30px]"
                            v-for="item in orderCommitList">
                            <div class="reviews-item-header flex items-center justify-between">
                                <div
                                    class="reviews-item-header-left text-[14px] font-['Open_Sans_3'] opacity-70 md:text-[20px] lg:text-[20px]">
                                    {{ item.details[0].anonymous ? '(Anonymous)' : item.details[0].commentUsername }}
                                </div>
                                <div class="reviews-item-header-right">
                                    <SmRate :model-value="item.grade.toFixed(1)" :disabled="true" :showSmall="true"
                                        :size="size" :textColor="'#1C2158'"></SmRate>
                                </div>
                            </div>
                            <div
                                class="reviews-item-time text-[12px] font-['Open_Sans_3'] opacity-70 mt-[1px] md:text-[16px] md:mt-[12px] lg:text-[16px] lg:mt-[12px]">
                                Aug 31, 2022 19:46:45</div>
                            <div v-for="detail in item.details">
                                <div class="reviews-item-content text-[16px] font-['Open_Sans_3'] mt-[10px] md:text-[24px] lg:text-[24px] md:mt-[20px] lg:mt-[20px]"
                                    v-if="detail.commentUid != augurDetail.uid">
                                    {{ detail.comment }}
                                </div>
                                <div class="has-reback pt-[10px] border-t border-[#ededf2] border-solid mt-[10px] md:mt-[20px] md:pt-[20px] lg:mt-[20px] lg:pt-[20px]"
                                    v-else>
                                    <div
                                        class="has-reback-item p-[10px] rounded-[8px] bg-[#f4f4f7] text-[16px] font-['Open_Sans_3'] md:text-[24px] lg:text-[24px]">
                                        Advisor：{{ detail.comment }}
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
                <!--seeMore-->
                <div class="see-more flex justify-center items-center mt-[10px] md:mt-[20px] lg:mt-[20px]"
                    v-if="!finished">
                    <div class="see-more-item flex items-center px-[17px] py-[7px] rounded-[3px] bg-[#fff] cursor-pointer gap-1 md:rounded-[6px] md:px-[30px] md:py-[15px] md:border md:border-[#4484FF] md:border-solid lg:border lg:border-[#4484FF] lg:border-solid lg:px-[30px] lg:py-[15px] lg:rounded-[6px]"
                        v-loading-mask="loadingMore">
                        <img src="@/assets/common/see-more.png" alt=""
                            class="w-[15px] h-[15px] md:w-[30px] md:h-[30px] lg:w-[30px] lg:h-[30px]" />
                        <span
                            class="text-[16px] font-['Philosopher'] text-[#4484FF] font-bold md:text-[20px] lg:text-[20px]">See
                            More</span>
                    </div>
                </div>
            </div>
        </div>
        <!--在小屏幕下的底部按钮-->
        <bottomButton :textReading="textReading" :liveTextChat="liveTextChat" :augurDetail="augurDetail"
            :showObserve="showObserve" @createOrder="createOrder" @sendMessage="sendMessage"></bottomButton>
        <!-- <Footer></Footer> -->
        <ConnectAdvisorDialog :dialogAdvisorVisible="dialogAdvisorVisible"
            @update:dialogAdvisorVisible="handleDialogAdvisorVisible" :augurDetail="augurDetail"
            :augurFortuneGroupId="augurFortuneGroupIdVal"></ConnectAdvisorDialog>
    </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, onUnmounted, reactive, watch } from 'vue'
import { ArrowRight } from '@element-plus/icons-vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import SmRate from '@/components/smRate/smRate.vue'
import ConnectAdvisorDialog from './components/connectAdvisorDialog.vue'
import StickyHeader from './components/stickyHeader.vue'
import bottomButton from './components/bottomButton.vue'
import message from '@/components/Message'
import { getAugurDetail, followAugur, unFollowAugur } from '@/api/augur'

import { getAguarOrderCommit } from '@/api/order'
import Loading from '@/components/Loading/Loading.vue'
import { OfflineOrderType, OnlineOrderType } from '@/types/orderType'
import { useThrottleFn } from '@vueuse/core';
const appContainer = ref()
const route = useRoute()
const router = useRouter()
const userStore = useUserStore()
const showFree = computed(() => userStore.showFree)
const augurUid = ref(route.query.id as string)
const dialogAdvisorVisible = ref(false)
const reviewsCount = ref(0)
const size = ref('small')
const augurDetail = ref<any>({
    grade: 0,
    avatar: '',
    fortuneDuration: 0,
})
const loading = ref(false)
const pageLoading = ref(false)
const textReading = ref({
    coins: 0,
    id: 0,
    discountCoins: 0,
    status: 0,
    augurFortuneGroupId: 0,
})
const liveTextChat = ref({
    coins: 0,
    id: 0,
    discountCoins: 0,
    status: 0,
    augurFortuneGroupId: 0,
})

const page = ref(1)

const finished = ref(false)
const orderCommitList = ref<any[]>([])
const loadingMore = ref(false)
const loadingImg = ref(true)

const augurFortuneGroupIdVal = ref(0)

const getOrderCommitList = async () => {
    if (finished.value) return

    loadingMore.value = true

    try {
        const response = await getAguarOrderCommit({ augurUid: augurUid.value, current: page.value, size: 10 })
        orderCommitList.value = [...orderCommitList.value, ...response.data]
        reviewsCount.value = response.count
        if (orderCommitList.value.length == response.count) {
            finished.value = true
        }
        page.value++
    } catch (error) {
        console.log(error)
    } finally {
        loadingMore.value = false
    }
}
const handleDialogAdvisorVisible = (value: boolean) => {
    dialogAdvisorVisible.value = value
}

const showObserve = ref(false)

let hasScrolled = false

const getAugurDetailData = async () => {
    pageLoading.value = true
    try {
        const res = await getAugurDetail({ augurUid: augurUid.value })
        console.log(res)
        res.data.augurFortune.forEach((item: any) => {
            if (item.name === 'Text Reading') {
                textReading.value = item
            } else if (item.name === 'Live Text Chat') {
                liveTextChat.value = item
            }
        })
        augurDetail.value = res.data
    } catch (error) {
        console.log(error)
    } finally {
        pageLoading.value = false
    }
}
const handleFollowAugur = (isFollow: boolean) => {
    loading.value = true
    if (isFollow) {
        followAugur({ augurUid: augurUid.value })
            .then((res) => {
                augurDetail.value.isFollowed = true
            })
            .finally(() => {
                loading.value = false
            })
    } else {
        unFollowAugur({ augurUid: augurUid.value })
            .then((res) => {
                augurDetail.value.isFollowed = false
            })
            .finally(() => {
                loading.value = false
            })
    }
}

const sendMessage = () => {
    router.push({
        name:'ChatToAdvisor',
        query:{
            uid:augurDetail.value.uid,
            username:augurDetail.value.username,
        }
    })
}

watch(
    () => augurDetail.value.avatar,
    (val) => {
        if (val) loadingImg.value = true
    }
)
const createOrder = ({ augurFortuneGroupId, type }: { augurFortuneGroupId: number, type: string }) => {
    augurFortuneGroupIdVal.value = augurFortuneGroupId
    // 先判断是否属于OfflineOrderType
    if (Object.values(OfflineOrderType).includes(augurFortuneGroupId)) {
        router.push({
            path: '/offlinePlaceOrder',
            query: {
                uid: augurDetail.value.uid,
                username: augurDetail.value.username,
                augurFortuneGroupId: augurFortuneGroupId,
                augurFortuneId: textReading.value.id
            }
        })
        return
    }

    // 不属于OfflineOrderType再判断状态
    if (augurDetail.value.state == 1 || augurDetail.value.state == 2) {
        message.warning('service is not availabe')
        return
    } else if (augurDetail.value.state == 3) {
        dialogAdvisorVisible.value = true
    } else if (augurDetail.value.state == 4) {
        router.push({
            path: '/createOrder',
            query: {
                uid: augurDetail.value.uid,
                username: augurDetail.value.username,
                augurFortuneGroupId: augurFortuneGroupId,
                augurFortuneId: type == 'liveTextChat' ? liveTextChat.value.id : textReading.value.id
            }
        })
    }
}

const getServeiceHours = (minus: number) => {
    return Math.ceil(minus / 3600)
}

// 监听窗口大小变化
const handleResize = useThrottleFn(() => {
    if (!appContainer.value) return;
    const width = appContainer.value.offsetWidth;
    size.value = width < 640 ? 'small' : 'large'

}, 200);

onMounted(() => {
    getAugurDetailData()
    getOrderCommitList()
    const header = document.querySelector('.header-container') as HTMLElement
    const target = document.getElementById(appContainer.value.offsetWidth < 640 ? 'family-friends' : 'family-friends-lg')
    if (!header || !target) return

    const check = () => {
        const headerRect = header.getBoundingClientRect()
        const targetRect = target.getBoundingClientRect()
        if (document.body.scrollTop === 0) {
            showObserve.value = false
            return
        }
        // hasScrolled = true
        if (targetRect.bottom <= headerRect.bottom) {
            showObserve.value = true
        } else {
            showObserve.value = false
        }
    }

    document.body.addEventListener('scroll', check, { passive: true })
    check()
    // 设置 ResizeObserver
    const resizeObserver = new ResizeObserver(handleResize);
    if (appContainer.value) {
        resizeObserver.observe(appContainer.value);
    }
    onUnmounted(() => {
        resizeObserver.disconnect();
        document.body.removeEventListener('scroll', check)
    })
})
</script>

<style lang="scss" scoped>
.app-container {
    :deep(.el-breadcrumb__inner) {
        color: #1c2158;
        font-family: 'Philosopher';
    }
}

.observe {
    box-shadow: 0px 5px 10px 0px #e4e6f3;
}

.lg-free-button {
    width: 380px;
    min-width: 360px;
    height: 80px;
    background: linear-gradient(90deg, rgba(105, 207, 95, 0.25) 0%, rgba(11, 169, 82, 0.25) 100%);
    border-radius: 10px;
    display: flex;
    align-items: center;

    &:hover {
        box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.25);
    }

    .free-button-text {
        width: 120px;
        background: linear-gradient(90deg, #69cf5f 0%, #0ba952 100%);
        position: relative;

        &::before {
            content: '';
            position: absolute;
            inset: 0;
            width: 100%;
            height: 100%;
            padding: 1px;
            border-radius: 8px;
            background: linear-gradient(180deg, #178a49 0%, #40df3c 34.9%, #178a49 100%);
            -webkit-mask: linear-gradient(#178a49 0 0) content-box, linear-gradient(#178a49 0 0);
            -webkit-mask-composite: xor;
            mask-composite: exclude;
            pointer-events: none;
        }
    }
}

.lg-text-button {
    width: 380px;
    min-width: 360px;
    height: 80px;
    background: linear-gradient(90deg, rgba(122, 175, 255, 0.15) 0%, rgba(122, 135, 255, 0.15) 100%);
    border-radius: 10px;
    display: flex;
    align-items: center;

    &:hover {
        box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.25);
    }

    .text-button-text {
        width: 120px;
        background: linear-gradient(91.29deg, #7aafff 1.1%, #7a87ff 95.36%);
        padding: 5px 0;
        position: relative;
    }

    .busy-button {
        background: linear-gradient(91.29deg, #bdc6d2 1.1%, #b4b7cc 95.36%);
    }

    .offline-button {
        background: linear-gradient(91.29deg, #bdc6d2 1.1%, #b4b7cc 95.36%);
    }
}

.free-button {
    width: 100%;
    height: 70px;
    background: linear-gradient(90deg, rgba(105, 207, 95, 0.25) 0%, rgba(11, 169, 82, 0.25) 100%);
    border-radius: 10px;
    display: flex;
    align-items: center;

    &:hover {
        box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.25);
    }

    .free-button-text {
        background: linear-gradient(90deg, #69cf5f 0%, #0ba952 100%);
        position: relative;

        &::before {
            content: '';
            position: absolute;
            inset: 0;
            width: 100%;
            height: 100%;
            padding: 1px;
            border-radius: 8px;
            background: linear-gradient(180deg, #178a49 0%, #40df3c 34.9%, #178a49 100%);
            -webkit-mask: linear-gradient(#178a49 0 0) content-box, linear-gradient(#178a49 0 0);
            -webkit-mask-composite: xor;
            mask-composite: exclude;
            pointer-events: none;
        }
    }
}

.text-button {
    width: 100%;
    height: 70px;
    background: linear-gradient(90deg, rgba(122, 175, 255, 0.15) 0%, rgba(122, 135, 255, 0.15) 100%);
    border-radius: 10px;
    display: flex;
    align-items: center;

    &:hover {
        box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.25);
    }

    .text-button-text {
        background: linear-gradient(91.29deg, #7aafff 1.1%, #7a87ff 95.36%);
        position: relative;
    }

    .busy-button {
        background: linear-gradient(91.29deg, #bdc6d2 1.1%, #b4b7cc 95.36%);
    }

    .offline-button {
        background: linear-gradient(91.29deg, #bdc6d2 1.1%, #b4b7cc 95.36%);
    }
}

.discount-top {
    position: absolute;
    background: url('@/assets/common/discount.png') no-repeat center center;
    background-size: 100% 100%;
    text-align: center;
}

.discount-top-sm {
    position: absolute;
    background: url('@/assets/common/discount-sm.png') no-repeat center center;
    background-size: 100% 100%;
    text-align: center;
}

.send-message {
    .send-message-text {
        background: linear-gradient(91.29deg, #7AAFFF 1.1%, #7A87FF 95.36%);
        background-clip: text;
        -webkit-background-clip: text;
        color: transparent;

    }
    &:hover {
        box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.25);
    }
}
</style>
