<!--  -->
<template>
    <div class="family-friends-button fixed bottom-[0px] py-[10px]  sm:hidden flex items-center bg-white w-full px-[15px] gap-[15px] transition-transform duration-300"
        :style="{
            transform: showObserve ? 'translateY(0)' : 'translateY(100%)',
        }">
        <button
            class="send-message flex-1 h-[50px] white-norwap flex items-center justify-center p-[5px] border-[1px] border-solid border-[#4484FF] rounded-[6px]">
            <img src="@/assets/common/send-message.png" class="w-[20px] h-[20px] ">
            <span class="send-message-text text-[14px] font-[600] font-['Philosopher'] ml-[6px] leading-[19px]"
                @click="sendMessage">send
                Message</span>
        </button>
        <!--liveTextChat-->
        <button v-if="augurDetail.state == 4 && liveTextChat.status == 1"
            class="flex-1 white-norwap flex justify-between p-[5px]"
            :class="[liveTextChat.coins == 0 ? 'free-button' : 'text-heder-button']"
            @click="createOrder({ augurFortuneGroupId: liveTextChat.augurFortuneGroupId, type: 'liveTextChat' })">
            <div class="flex items-center">
                <img src="@/assets/common/free-chart.png" alt="" class="chart-img w-[40px]"
                    v-if="liveTextChat.coins == 0" />
                <img src="@/assets/common/live-text-chat.png" alt="" class="chart-img w-[40px]" v-else />
                <span class="font-['Open_Sans_3'] whitespace-nowrap text-[14px] font-semibold">Live Text Chat</span>
            </div>
        </button>
        <!--textReading 只有在liveText不生效的时候才会展示-->
        <button v-if="liveTextChat.status != 1 || augurDetail.state != 4" class="flex flex-1 justify-between p-[5px]"
            :class="[textReading.coins == 0 ? 'free-button' : 'text-heder-button']"
            @click="createOrder({ augurFortuneGroupId: textReading.augurFortuneGroupId, type: 'textReading' })">
            <div class="flex items-center">
                <img src="@/assets/common/free-chart.png" alt="" class="chart-img w-[40px]"
                    v-if="textReading.coins == 0" />
                <img src="@/assets/common/text.png" alt="" class="chart-img w-[40px]" v-else />
                <span class="font-['Open_Sans_3'] text-[14px] font-semibold whitespace-nowrap">Text Reading</span>
            </div>
        </button>
    </div>
</template>

<script lang="ts" setup name="bottomButton">

const emit = defineEmits(['createOrder', 'sendMessage'])
const props = defineProps({
    textReading: {
        type: Object,
        default: () => ({}),
    },
    liveTextChat: {
        type: Object,
        default: () => ({}),
    },
    showObserve: {
        type: Boolean,
        default: false,
    },
    augurDetail: {
        type: Object,
        default: () => {
            return {

            }
        }
    }
})
const createOrder = (data: any) => {
    emit('createOrder', data)
}
const sendMessage = () => {
    emit('sendMessage')
}
</script>
<style lang="scss" scoped>
.family-friends-button {
    .text-heder-button {
        border-radius: 6px;
        background: linear-gradient(91.29deg, rgba(122, 175, 255, .15) 1.1%, rgba(122, 135, 255, .15) 95.36%);
    }

    .free-button {
        background: linear-gradient(90deg, rgba(105, 207, 95, .25) 0%, rgba(11, 169, 82, .25) 100%);
    }

    .offline-button {
        background: linear-gradient(91.29deg, rgba(189, 198, 210, .15) 1.1%, rgba(180, 183, 204, .15) 95.36%);
    }

    .send-message {
        .send-message-text {
            background: linear-gradient(91.29deg, #7AAFFF 1.1%, #7A87FF 95.36%);
            background-clip: text;
            -webkit-background-clip: text;
            color: transparent;
        }
        &:hover{
            box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.25);
        }
    }


}
</style>