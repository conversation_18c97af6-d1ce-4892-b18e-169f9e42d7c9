<!--  -->
<template>
    <div :class="{
        'md:top-[146px]': showFree,
        'md:top-[72px]': !showFree,
        'lg:top-[182px]': showFree,
        'lg:top-[92px]': !showFree,
    }" class="observe w-full md px-[15px] md:py-[20px] lg:px-[120px] lg:py-[20px] bg-[#fff] fixed left-0 z-10 transition-transform duration-300 hidden md:block"
        :style="{
            transform: showObserve ? 'translateY(0)' : 'translateY(-100%)',
        }">
        <div class="observe-content flex items-center justify-between">
            <div class="flex items-center">
                <img :src="augurDetail?.avatar" alt=""
                    class="w-[44px] h-[44px] rounded-[4px] object-cover lg:w-[60px] lg:h-[60px]" />
                <div class="observe-content-desc ml-2.5">
                    <p class="text-[22px] font-['Philosopher'] font-semibold flex items-center">
                        <span class="mr-[6px]">{{ augurDetail?.username }}</span>
                        <SmRate class="mt-[0px]" :modelValue="augurDetail?.grade.toFixed(1)" :disabled="true"
                            ></SmRate>
                    </p>
                    <p class="text-[12px] font-['Open_Sans_3'] opacity-50 mt-[6px] line-clamp-1">
                        My expertise in Love
                        <span>{{ returnItemTags(augurDetail?.tags) }}</span>
                    </p>
                </div>
            </div>
            <div class="family-friends-button flex items-center">
                <button class="free-button white-norwap flex justify-between p-[5px] w-[160px]"
                    v-if="liveTextChat?.coins == 0"
                    @click="createOrder({ augurFortuneGroupId: liveTextChat?.augurFortuneGroupId, type: 'liveTextChat' })">
                    <div class="flex items-center">
                        <img src="@/assets/common/free-chart.png" alt="" class="chart-img w-[40px]" />
                        <span class="font-['Open_Sans_3'] whitespace-nowrap text-[14px] font-semibold">Live Text
                            Chat</span>
                    </div>
                </button>
                <button class="text-heder-button flex justify-between p-[5px] w-[160px]" v-else
                    :class="{ 'busy-button': liveTextChat?.status == 0 || augurDetail?.state == 1 || augurDetail?.state == 3 }"
                    @click="createOrder({ augurFortuneGroupId: liveTextChat?.augurFortuneGroupId, type: 'liveTextChat' })">
                    <div class="flex items-center">
                        <img src="@/assets/common/live-text-chat.png" alt="" class="chart-img w-[40px]" />
                        <div class="flex flex-col ml-[7px]">
                            <p class="text-[14px] whitespace-nowrap font-['Open_Sans_3'] font-semibold">Live Text Chat
                            </p>
                        </div>
                    </div>
                </button>
                <button class="free-button flex justify-between p-[5px] ml-[15px] w-[160px]"
                    v-if="textReading?.coins == 0"
                    @click="createOrder({ augurFortuneGroupId: textReading?.augurFortuneGroupId, type: 'textReading' })">
                    <div class="flex items-center">
                        <img src="@/assets/common/free-chart.png" alt="" class="chart-img w-[40px]" />
                        <span class="font-['Open_Sans_3'] text-[14px] font-semibold whitespace-nowrap">Text
                            Reading</span>
                    </div>
                </button>
                <button class="text-heder-button flex justify-between p-[5px] ml-[15px] w-[160px]" v-else
                    @click="createOrder({ augurFortuneGroupId: textReading?.augurFortuneGroupId, type: 'textReading' })">
                    <div class="flex items-center">
                        <img src="@/assets/common/text.png" alt="" class="chart-img w-[40px]" />
                        <div class="flex flex-col ml-[7px]">
                            <p class="text-[14px] font-['Open_Sans_3'] whitespace-nowrap font-semibold">Text Reading</p>
                        </div>
                    </div>
                </button>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup name="StickyHeader">
import { ref } from 'vue'
import SmRate from '@/components/smRate/smRate.vue'

const emit = defineEmits(['createOrder'])
const { textReading, liveTextChat, augurDetail, showObserve, showFree } = defineProps({
    textReading: Object,
    liveTextChat: Object,
    augurDetail: Object,
    showFree: Boolean,
    showObserve: Boolean
})

const createOrder = ({ augurFortuneGroupId, type }: { augurFortuneGroupId: number, type: string }) => {
    emit('createOrder', { augurFortuneGroupId, type })
}
const returnItemTags = (tags: any[]) => {
    if (!tags) return ''
    return (tags.map((item) => item.name).join('/') + '/').slice(0, -1)
}
</script>
<style lang="scss" scoped>
.text-heder-button {
    background: linear-gradient(90deg, rgba(122, 175, 255, 0.15) 0%, rgba(122, 135, 255, 0.15) 100%);
    border-radius: 8px;

    &:hover {
        box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.25);
    }
    &.busy-button{
        background: linear-gradient(91.29deg, rgba(189, 198, 210, .15) 1.1%, rgba(180, 183, 204, .15) 95.36%);
    }
}
</style>
