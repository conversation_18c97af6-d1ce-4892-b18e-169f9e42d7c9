<!-- 连接顾问弹窗 -->
<template>
	<div class="">
		<el-dialog :model-value="dialogAdvisorVisible" @update:model-value="$emit('update:dialogAdvisorVisible', $event)" :before-close="handleAdvisorClose" :show-close="false" class="flex flex-col w-[339px] rounded-[18px] md:w-[400px] lg:w-[500px] p-0">
			<div class="advisor-content px-[20px] py-[30px] lg:p-[40px]">
				<!--关闭按钮-->
				<svg class="absolute top-[10px] left-[10px] w-[20px] h-[20px] lg:w-[24px] lg:h-[24px]" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" @click="handleAdvisorClose">
					<g opacity="0.5">
						<path d="M15.5811 3.92252L9.74772 9.75585L15.5811 15.5892" stroke="#3D3939" stroke-width="1.375" stroke-linecap="round" stroke-linejoin="round" />
						<path d="M3.79395 15.712L9.62728 9.87862L3.79395 4.04529" stroke="#3D3939" stroke-width="1.375" stroke-linecap="round" stroke-linejoin="round" />
					</g>
				</svg>

				<!--顾问头像-->
				<div class="advisor-content-header flex flex-col items-center">
					<div class="relative">
						<img :src="augurDetail.avatar" alt="" class="w-[50px] h-[50px] rounded-[4px] lg:w-[70px] lg:h-[70px]" />
						<div class="online absolute top-[3px] right-[3px] w-[8px] h-[8px] rounded-[50%] bg-[#3AD953] border-1 border-[#fff] border-solid" v-if="augurDetail.state == 4 || augurDetail.state == 2"></div>
						<div class="offline absolute top-[3px] right-[3px] w-[8px] h-[8px] rounded-[50%] bg-[#BFC3C5] border-1 border-[#fff] border-solid" v-else-if="augurDetail.state == 1"></div>
						<div class="busy absolute top-[3px] right-[3px] w-[8px] h-[8px] rounded-[50%] bg-[#EA3232] border-1 border-[#fff] border-solid" v-else-if="augurDetail.state == 3"></div>
					</div>
					<span class="text-[16px] font-['Philosopher'] text-[#1C2158] font-bold mt-[6px] lg:text-[24px] lg:mt-[15px]">Connect Advisor</span>
					<span class="text-[12px] font-['Source_Sans_3'] text-[#1C2158] font-normal mt-[7px] lg:text-[16px] lg:mt-[10px]">is not available for this service now</span>
				</div>
				<!--顾问line-->
				<img src="@/assets/common/lg-line.png" alt="" class="w-full h-[15px] mt-[21px] lg:h-[20px]" />
				<p class="text-[12px] font-['Source_Sans_3'] text-[#1C2158] font-normal mt-[15px] text-center lg:mt-[20px]">Hey，why not try our recommended advisor who is available to help you first?</p>
				<!--顾问详细信息-->
				<div class="advisor-content-info w-full rounded-[10px] p-[10px] mt-[13px] flex items-center justify-between lg:p-[20px]" v-loading-mask="loading">
					<div class="advisor-content-info-left flex items-center flex-1 overflow-hidden">
						<div class="advisor-content-info-left-avatar relative shrink-0">
							<img :src="recommendAugurDetail.avatar"  class="shrink-0 w-[50px] h-[50px] rounded-[4px] lg:w-[70px] lg:h-[70px]" />
							<div class="online absolute top-[3px] right-[3px] w-[8px] h-[8px] rounded-[50%] bg-[#3AD953] border-1 border-[#fff] border-solid"></div>
						</div>
						<div class="advisor-content-info-left-name ml-3 flex-1 flex flex-col justify-between lg:h-[70px]">
							<span class="text-[16px] font-['Philosopher'] text-[#1C2158] font-bold">{{ recommendAugurDetail.username }}</span>
							<span class="text-[12px] font-['Source_Sans_3'] text-[#1C2158] font-normal line-clamp-1 opacity-50 mt-[2px]">{{ recommendAugurDetail.about }}</span>
							<div class="price mt-[2px] flex">
								<span class="font-['Open_Sans_3'] font-bold text-[12px] text-[#F53A3A] lg:text-[16px]">{{ augurFortune.discountCoins < augurFortune.coins ? augurFortune.discountCoins : augurFortune.coins }}</span>
								<img src="@/assets/common/money.png" alt="" class="w-[12px] h-[12px] ml-[2px] lg:w-[18px] lg:h-[18px]" />
							</div>
						</div>
					</div>
					<div class="advisor-content-info-right ml-[12px] shrink-0">
						<div class="advisor-content-info-right-connect cursor-pointer px-[4px] py-[8px] rounded-[4px] text-[#fff] text-[12px] font-['Philosopher'] font-bold lg:px-[13px] lg:py-[11px] lg:text-[16px]" @click="createOrder">Connect Now</div>
					</div>
				</div>
			</div>
		</el-dialog>
	</div>
</template>

<script lang="ts" setup name="ConnectAdvisorDialog">
import { ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import { recommendAugur } from '@/api/augur'

const router = useRouter()
interface AugurFortune {
	augurFortuneGroupId: number
	coins: number
	discountCoins: number
	expeditingCoins: number
	id: number
	name: string
	status: number
	supportExpediting: number
	[key: string]: string | number
}
const emit = defineEmits(['update:dialogAdvisorVisible'])
const recommendAugurDetail = ref<any>({})
const augurFortune = ref(<AugurFortune>{})
const loading = ref(false)
const props = defineProps({
	dialogAdvisorVisible: {
		type: Boolean,
		default: false,
	},
	augurDetail: {
		type: Object,
		default: () => {
			return {}
		},
	},
	augurFortuneGroupId: Number,
})
watch(
	() => props.dialogAdvisorVisible,
	(newVal) => {
		if (newVal) {
			loading.value = true
			recommendAugur({ augurFortuneGroupId: props.augurFortuneGroupId as number })
				.then((res) => {
					// console.log(res)
					recommendAugurDetail.value = res.data
					augurFortune.value = res.data.augurFortune.filter((item: AugurFortune) => item.augurFortuneGroupId == props.augurFortuneGroupId)[0]
				})
				.finally(() => {
					loading.value = false
				})
		}
	}
)

const createOrder = ()=>{
    router.push({
        path:'/createOrder',
        query:{
            uid:recommendAugurDetail.value.uid,
            username:recommendAugurDetail.value.username,
            augurFortuneGroupId:props.augurFortuneGroupId,
            augurFortuneId:augurFortune.value.id
        }
    })
}
const handleAdvisorClose = () => {
	emit('update:dialogAdvisorVisible', false)
}
</script>
<style lang="scss" scoped>
:deep(.el-dialog) {
	padding: 0;
	display: flex;
	flex-direction: column;

	.el-dialog__header {
		display: none;
	}

	.el-dialog__body {
		flex: 1;
		padding: 0 !important;
	}
}
.advisor-content-info {
	background: linear-gradient(269.4deg, #efefff 2.43%, #fff8f4 99.62%);
}
.advisor-content-info-right-connect {
	background: linear-gradient(91.29deg, #7aafff 1.1%, #7a87ff 95.36%);
	white-space: nowrap;
}
</style>
