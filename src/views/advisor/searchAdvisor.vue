<!--  -->
<template>
    <div class="app-container">
        <div class="main-content px-[15px] lg:px-[120px]">
            <el-breadcrumb :separator-icon="ArrowRight"
                class="font-['Open_Sans_3'] font-[600] text-[14px] pt-[8px] mb-[14px] md:pt-6 lg:pt-6 md:mb-[30px] lg:mb-[30px]">
                <el-breadcrumb-item class="text-[#1C2158]">Home</el-breadcrumb-item>
                <el-breadcrumb-item>Family &Friends</el-breadcrumb-item>

            </el-breadcrumb>
            <div class="mt-[26px] font-['Philosopher'] text-[19px] font-blod mb-[12px] lg:text-[28px]">Search Result for
                "{{ searchValue }}"</div>
            <div class="advisor-list min-h-[calc(100vh-500px)]">
                <!-- 骨架屏加载 -->
                <el-skeleton :loading="loading" animated>
                    <template #template>
                        <div class="skeleton-grid">
                            <div v-for="i in 10" :key="i" class="skeleton-card">
                                <el-skeleton-item variant="image" style="width: 100%; height: 146px; border-radius: 8px 8px 0 0;" />
                                <div class="skeleton-card-content">
                                    <el-skeleton-item variant="text" style="width: 80%; height: 16px; margin-bottom: 8px;" />
                                    <el-skeleton-item variant="text" style="width: 90%; height: 14px; margin-bottom: 8px;" />
                                    <el-skeleton-item variant="text" style="width: 60%; height: 14px; margin-bottom: 12px;" />
                                    <el-skeleton-item variant="button" style="width: 100%; height: 36px; border-radius: 6px;" />
                                </div>
                            </div>
                        </div>
                    </template>
                    <!-- 实际内容 -->
                    <template #default>
                        <!-- 暂无数据状态 -->
                        <div v-if="!loading && advisorList.length === 0" class="empty-state">
                            <div class="empty-state-content">
                                <img src="@/assets/card/no-data.png" alt="暂无数据" class="empty-state-image" />
                                <h3 class="empty-state-title">No Results Found</h3>
                                <p class="empty-state-description">
                                    Sorry, we couldn't find any advisors matching "{{ searchValue }}"
                                </p>
                                <div class="empty-state-actions">
                                    <button 
                                        class="empty-state-button"
                                        @click="goBackToAdvisors"
                                    >
                                        Browse All Advisors
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 有数据时的正常显示 -->
                        <div v-else-if="!loading && advisorList.length > 0" class="main-content-item mt-[24px] lg:mt-[30px]">
                            <div
                                class="main-content-list w-full relative mt-5 grid gap-[10px] grid-cols-2 md:grid-cols-5 md:gap-5">
                                <div class="main-content-list-item rounded-[8px] min-w-[146px] lg:min-w-[calc((100% - 4 * 20px) / 5)] relative cursor-pointer md:rounded-[14px]"
                                    v-for="item in advisorList" :key="item.id" @click="handleClick(item)">
                                    <div class="absolute flex p-2.5 w-full items-center justify-between">
                                        <div class="rate flex items-center px-2 py-0.5">
                                            <img src="@/assets/home/<USER>" alt="" class="w-3.5 f-3.5 mr-0.5" />
                                            <span class="text-sm text-white">{{ item.grade.toFixed(1) }}</span>
                                        </div>
                                        <div class="online border border-solid border-white w-3.5 h-3.5"
                                            v-if="item.state == 4 "></div>
                                        <div class="offline border border-solid border-white w-3.5 h-3.5"
                                            v-if="item.state == 1 || item.state == 2"></div>
                                        <div class="busy border border-solid border-white w-3.5 h-3.5" v-if="item.state == 3">
                                        </div>
                                    </div>
                                    <img :src="item.avatar" alt=""
                                        class="w-full h-[146px] rounded-[8px] md:w-full md:h-[224px] object-cover object-top md:rounded-tl-[14px] md:rounded-tr-[14px]" />
                                    <div class="main-content-list-item-bottom px-3 mt-2.5 pb-3">
                                        <div class="font-extrabold text-[#1C2158] text-sm truncate font-['Philosopher'] lg:text-xl">{{ item.username
                                            }}</div>
                                        <p
                                            class="main-content-list-item-bottom-desc mt-[5px] h-[30px] text-[#1C2158] opacity-50 text-xs md:h-[33px] md:text-[14px]">
                                            {{ item.about }}</p>
                                        <div class="main-content-list-item-bottom-bottom flex items-center mt-1.5 font-['Philosopher']">
                                            <span class="text-lg mr-1 text-[#4484FF] lg:text-xl">{{ item.commentTimes }}</span>
                                            <span class="text-sm text-[#1C2158] opacity-70 lg:text-base">Readings</span>
                                        </div>
                                        <div class="connect-button font-['Philosopher']">
                                            <button v-if="userInfo.hasExperienceCard && item.state == 4" @click.stop="createOrder(item)"
                                            class="sm-button free-button w-full py-[9px] leading-[16px] text-base text-center text-white mt-[8px] lg:mt-[13px] lg:py-[11px] lg:text-lg"
                                            >Free Chat</button>
                                            <button v-else-if="!userInfo.hasExperienceCard && userInfo.giftBagRemainingSeconds"
                                                class="sm-button pay-button w-full py-[9px] leading-[16px] text-base text-center text-white mt-[8px] lg:mt-[13px] lg:py-[11px] lg:text-lg"
                                                @click.stop="handleClick(item)">$1.99 for 6min</button>
                                            <button v-else
                                            @click.stop="handleClick(item)"
                                                class="sm-button default-button w-full py-[9px] leading-[16px] text-base text-center text-white mt-[8px] lg:mt-[13px] lg:py-[11px] lg:text-lg">Connect
                                                Now</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </template>
                </el-skeleton>
            </div>
            <div class="pagination-container mt-[20px] flex justify-center md:mt-[50px]" v-if="!finished && advisorList.length > 0">
                <div class="w-[200px] h-[40px] md:w-[300px] md:h-[54px] bg-white rounded-[10px] flex items-center justify-center font-['Philosopher'] text-[#4484FF] text-[16px] md:text-[20px] font-bold border border-solid border-[#4484FF] cursor-pointer"
                    @click="getAdvisorList">All Advisor</div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed, watch } from 'vue'
import { ArrowRight } from '@element-plus/icons-vue'
import { useRoute } from 'vue-router'
import { searchAugur } from '@/api/augur'
import { useThrottleFn } from '@vueuse/core'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()
const userInfo = computed(() => {
    return userStore.userInfo
})
const router = useRouter()
const route = useRoute()

const sorted = ref(route.params.type as string)
const advisorList = ref<Advisor[]>([])
const current = ref(1)

const finished = ref(false)
const loading = ref(true)

const searchValue = ref('')
const menuItems = [
    {
        name: 'All',
        type: '0',
    },
    {
        name: 'New',
        type: '4',
    },
    {
        name: 'Reviews',
        type: '2',
    },
    {
        name: 'Readings',
        type: '1',
    },
    {
        name: 'Rated',
        type: '3',
    },
]
const type = computed(() => {
    return menuItems.find((item) => item.type === route.params.type)?.name
})

const getAdvisorList = useThrottleFn(async () => {
    if (finished.value) return
    console.log('开始加载数据，设置loading为true')
    loading.value = true
    try {
        const res = await searchAugur({
            sorted: sorted.value as string,
            current: current.value,
            keyword: searchValue.value
        })

        advisorList.value = [...advisorList.value, ...res.data]

        if (advisorList.value.length === res.count) {
            finished.value = true
        }
        current.value++
        console.log('数据加载完成，设置loading为false')
    } catch (error) {
        console.error('搜索占卜师失败:', error)
    } finally {
        loading.value = false
    }
}, 1000)

const handleClick = (item: any) => {
    router.push({
        path: '/familyfriends',
        query: {
            id: item.uid,
        },
    })
}

const createOrder = (item: any) => {
    const augurFortuneId = item.augurFortune.filter((item: any) => item.augurFortuneGroupId == 4)[0].id
    router.push({
        path: '/createOrder',
        query: {
            uid: item.uid,
            username: item.username,
            augurFortuneGroupId: 4,
            augurFortuneId: augurFortuneId
        },
    })
}

const goBackToAdvisors = () => {
    router.push({
        name: 'Advisor',
        params: { type: '0' } // 跳转到所有顾问页面
    })
}

const clearSearch = () => {
    // 清空搜索并重新搜索
    // searchValue.value = ''
    // advisorList.value = []
    // current.value = 1
    // finished.value = false
    // getAdvisorList()
}

watch(() => route.query.search, (newVal) => {
    if (newVal === undefined) return;
    searchValue.value = newVal as string;
    advisorList.value = [];
    current.value = 1;
    finished.value = false;
    loading.value = true;
    getAdvisorList();
}, { immediate: true });

onMounted(() => {
    // Component is mounted, do nothing here since the watcher with `immediate: true` handles the initial load.
})
</script>
<style lang="scss" scoped>
.main-content-list-item {
    box-shadow: 0px 1px 5px 0px #dcdde5;
    border-radius: 14px;
    background-color: #fff;
    margin-bottom: 1px;

    &:hover {
        box-shadow: 0px 0px 15px 0px #5f6bd3b2;
        transition: all 0.3s;
    }

    .online {
        border-radius: 50%;
        background-color: #3ad953;
    }

    .offline {
        background-color: #BFC3C5;
        border-radius: 50%;
    }

    .busy {
        background-color: #ea3232;
        border-radius: 50%;
    }

    .rate {
        background: linear-gradient(91.29deg, #7aafff 1.1%, #7a87ff 95.36%);
        border-radius: 22px;
    }

    .main-content-list-item-bottom-desc {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .connect-button {
        .sm-button {
            border-radius: 8px;

            &.default-button {
                background: linear-gradient(91.29deg, #7aafff 1.1%, #7a87ff 95.36%);
            }

            &.free-button {
                background: linear-gradient(90deg, #69cf5f 0%, #0daa53 100%);
            }

            &.pay-button {
                background: linear-gradient(270deg, #fd4444 3.23%, #ff8f5f 100%);
            }
        }
    }
}

// Skeleton Loading Styles
.skeleton-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(146px, 1fr));
    gap: 10px;
    
    @media (min-width: 768px) {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 20px;
    }
    
    @media (min-width: 1024px) {
        grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
        gap: 24px;
    }
}

.skeleton-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0px 1px 5px 0px #dcdde5;
    overflow: hidden;
    position: relative;
    
    @media (min-width: 768px) {
        border-radius: 14px;
    }
}

.skeleton-status {
    position: absolute;
    top: 10px;
    left: 10px;
    right: 10px;
    display: flex;
    justify-content: space-between;
    z-index: 10;
}

.skeleton-card-content {
    padding: 12px;
    
    @media (min-width: 768px) {
        padding: 16px;
    }
}

// Element Plus Skeleton Overrides
:deep(.el-skeleton) {
    width: 100%;
}

:deep(.el-skeleton__item) {
    background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 37%, #f2f2f2 63%);
    background-size: 400% 100%;
    animation: skeleton-loading 1.4s ease infinite;
}

@keyframes skeleton-loading {
    0% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0 50%;
    }
}

// Empty State Styles
.empty-state {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
    padding: 40px 20px;
}

.empty-state-content {
    text-align: center;
    max-width: 400px;
}

.empty-state-image {
    width: 250px;
    height: 250px;
    margin: 0 auto 24px;
    opacity: 0.6;
}

.empty-state-title {
    font-family: 'Philosopher', serif;
    font-size: 24px;
    font-weight: bold;
    color: #1C2158;
    margin: 0 0 12px 0;
    
    @media (min-width: 768px) {
        font-size: 28px;
    }
}

.empty-state-description {
    font-family: 'Open_Sans_3', sans-serif;
    font-size: 16px;
    color: #1C2158;
    opacity: 0.7;
    line-height: 1.5;
    margin: 0 0 32px 0;
    
    @media (min-width: 768px) {
        font-size: 18px;
    }
}

.empty-state-actions {
    display: flex;
    flex-direction: column;
    gap: 12px;
    align-items: center;
    
    @media (min-width: 768px) {
        flex-direction: row;
        justify-content: center;
        gap: 16px;
    }
}

.empty-state-button {
    padding: 12px 24px;
    border-radius: 8px;
    font-family: 'Open_Sans_3', sans-serif;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
    min-width: 160px;
    
    @media (min-width: 768px) {
        padding: 14px 28px;
        font-size: 16px;
        min-width: 180px;
    }
    
    &:not(.secondary) {
        background: linear-gradient(91.29deg, #7aafff 1.1%, #7a87ff 95.36%);
        color: white;
        
        &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(122, 175, 255, 0.3);
        }
    }
    
    &.secondary {
        background: transparent;
        color: #7aafff;
        border: 2px solid #7aafff;
        
        &:hover {
            background: rgba(122, 175, 255, 0.1);
            transform: translateY(-2px);
        }
    }
}
</style>
