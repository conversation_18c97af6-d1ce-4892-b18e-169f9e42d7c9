<!--  -->
<template>
    <div class="app-container">
        <div class="main-content px-[15px] lg:px-[120px]">
            <div class="mt-[26px] font-['Philosopher'] text-[19px] font-blod mb-[12px] lg:text-[28px]">{{ type }}
                Advisors</div>
            <div class="advisor-list min-h-[calc(100vh-500px)]">
                <div class="main-content-item mt-[24px] lg:mt-[30px]">
                    <div
                        class="main-content-list w-full relative mt-5 grid gap-[10px] grid-cols-2 md:grid-cols-5 md:gap-5">
                        <!-- 骨架屏：仅在首次加载且 advisorList.length === 0 时显示 -->
                        <template v-if="loading && advisorList.length === 0">
                            <div v-for="i in 10" :key="i" class="main-content-list-item skeleton-card">
                                <div class="skeleton-status">
                                    <div class="skeleton-circle"></div>
                                    <div class="skeleton-circle"></div>
                                </div>
                                <div class="skeleton-img"></div>
                                <div class="skeleton-card-content">
                                    <div class="skeleton-text w-4/5 mb-2"></div>
                                    <div class="skeleton-text w-11/12 mb-2"></div>
                                    <div class="skeleton-text w-3/5 mb-3"></div>
                                    <div class="skeleton-btn w-full"></div>
                                </div>
                            </div>
                        </template>
                        <!-- 实际内容 -->
                        <template v-else>
                            <div class="main-content-list-item rounded-[8px] min-w-[146px] lg:min-w-[calc((100% - 4 * 20px) / 5)] relative cursor-pointer md:rounded-[14px]"
                                v-for="item in advisorList" :key="item.id" @click="handleClick(item)">
                                <div class="absolute flex p-2.5 w-full items-center justify-between">
                                    <div class="rate flex items-center px-2 py-0.5">
                                        <img src="@/assets/home/<USER>" alt="" class="w-3.5 f-3.5 mr-0.5" />
                                        <span class="text-sm text-white">{{ item.grade.toFixed(1) }}</span>
                                    </div>
                                    <div class="online border border-solid border-white w-3.5 h-3.5"
                                        v-if="item.state == 4 "></div>
                                    <div class="offline border border-solid border-white w-3.5 h-3.5"
                                        v-if="item.state == 1 || item.state == 2"></div>
                                    <div class="busy border border-solid border-white w-3.5 h-3.5"
                                        v-if="item.state == 3">
                                    </div>
                                </div>
                                <img :src="item.avatar" alt=""
                                    class="w-full h-[146px] rounded-[8px] md:w-full md:h-[224px] object-cover object-top md:rounded-tl-[14px] md:rounded-tr-[14px]" />
                                <div class="main-content-list-item-bottom px-3 mt-2.5 pb-3">
                                    <div class="font-extrabold text-[#1C2158] text-sm truncate font-['Philosopher'] lg:text-xl">{{
                                        item.username
                                        }}</div>
                                    <p
                                        class="main-content-list-item-bottom-desc mt-[5px] h-[30px] text-[#1C2158] opacity-50 text-xs md:h-[33px] md:text-[14px]">
                                        {{ item.about }}</p>
                                    <div class="main-content-list-item-bottom-bottom flex items-center mt-1.5 font-['Philosopher']">
                                        <span class="text-lg mr-1 text-[#4484FF] lg:text-xl">{{ item.commentTimes
                                            }}</span>
                                        <span class="text-sm text-[#1C2158] opacity-70 lg:text-base">Readings</span>
                                    </div>
                                    <div class="connect-button font-['Philosopher']">
                                        <button v-if="userInfo.hasExperienceCard && item.state == 4" @click.stop="createOrder(item)"
                                            class="sm-button free-button w-full py-[9px] leading-[16px] text-base text-center text-white mt-[8px] lg:mt-[13px] lg:py-[11px] lg:text-lg"
                                            >Free Chat</button>
                                        <button
                                            v-else-if="!userInfo.hasExperienceCard && userInfo.giftBagRemainingSeconds"
                                            class="sm-button pay-button w-full py-[9px] leading-[16px] text-base text-center text-white mt-[8px] lg:mt-[13px] lg:py-[11px] lg:text-lg"
                                            @click.stop="handleClick(item)">$1.99 for 6min</button>
                                        <button v-else @click.stop="handleClick(item)"
                                            class="sm-button default-button w-full py-[9px] leading-[16px] text-base text-center text-white mt-[8px] lg:mt-[13px] lg:py-[11px] lg:text-lg">Connect
                                            Now</button>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>
                <div class="pagination-container mt-[20px] flex justify-center md:mt-[50px]"
                    v-show="!finished && advisorList.length">
                    <div v-loading-mask="loading"
                        class="w-[200px] h-[40px] md:w-[300px] md:h-[54px] bg-white rounded-[10px] flex items-center justify-center font-['Philosopher'] text-[#4484FF] text-[16px] md:text-[20px] font-bold border border-solid border-[#4484FF] cursor-pointer"
                        @click="getAdvisorList">All Advisor</div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed } from 'vue'
import { useRoute } from 'vue-router'
import { searchAugur } from '@/api/augur'
import { useThrottleFn } from '@vueuse/core'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()
const userInfo = computed(() => {
    return userStore.userInfo
})
const router = useRouter()
const route = useRoute()

const sorted = ref(route.params.type as string)
const advisorList = ref<Advisor[]>([])
const current = ref(1)

const finished = ref(false)
const loading = ref(false)
const menuItems = [
    {
        name: 'All',
        type: '0',
    },
    {
        name: 'New',
        type: '4',
    },
    {
        name: 'Reviews',
        type: '2',
    },
    {
        name: 'Readings',
        type: '1',
    },
    {
        name: 'Rated',
        type: '3',
    },
]
const type = computed(() => {
    return menuItems.find((item) => item.type === route.params.type)?.name
})

const getAdvisorList = useThrottleFn(async () => {
    if (finished.value) return
    loading.value = true
    try {
        const res = await searchAugur({
            sorted: sorted.value as string,
            current: current.value,
        })

        advisorList.value = [...advisorList.value, ...res.data]

        if (advisorList.value.length === res.count) {
            finished.value = true
        }
        current.value++
    } finally {
        loading.value = false
    }
}, 1000)

const handleClick = (item: any) => {
    router.push({
        path: '/familyfriends',
        query: {
            id: item.uid,
        },
    })
}

const createOrder = (item: any) => {
    const augurFortuneId = item.augurFortune.filter((item: any) => item.augurFortuneGroupId == 4)[0].id
    router.push({
        path: '/createOrder',
        query: {
            uid: item.uid,
            username: item.username,
            augurFortuneGroupId: 4,
            augurFortuneId: augurFortuneId
        },
    })
}

onMounted(async () => {
    await getAdvisorList()
})
</script>
<style lang="scss" scoped>
.main-content-list-item {
    box-shadow: 0px 1px 5px 0px #dcdde5;
    border-radius: 14px;
    background-color: #fff;
    margin-bottom: 1px;

    &:hover {
        box-shadow: 0px 0px 15px 0px #5f6bd3b2;
        transition: all 0.3s;
    }

    .online {
        border-radius: 50%;
        background-color: #3ad953;
    }

    .offline {
        background-color: #BFC3C5;
        border-radius: 50%;
    }

    .busy {
        background-color: #ea3232;
        border-radius: 50%;
    }

    .rate {
        background: linear-gradient(91.29deg, #7aafff 1.1%, #7a87ff 95.36%);
        border-radius: 22px;
    }

    .main-content-list-item-bottom-desc {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .connect-button {
        .sm-button {
            border-radius: 8px;

            &.default-button {
                background: linear-gradient(91.29deg, #7aafff 1.1%, #7a87ff 95.36%);
            }

            &.free-button {
                background: linear-gradient(90deg, #69cf5f 0%, #0daa53 100%);
            }

            &.pay-button {
                background: linear-gradient(270deg, #fd4444 3.23%, #ff8f5f 100%);
            }
        }
    }
}

// Skeleton Loading Styles
.skeleton-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0px 1px 5px 0px #dcdde5;
    overflow: hidden;
    position: relative;
    min-width: 146px;
    min-height: 260px;
    margin-bottom: 1px;
}

.skeleton-status {
    position: absolute;
    top: 10px;
    left: 10px;
    right: 10px;
    display: flex;
    justify-content: space-between;
    z-index: 10;
}

.skeleton-circle {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 37%, #f2f2f2 63%);
    background-size: 400% 100%;
    animation: skeleton-loading 1.4s ease infinite;
}

.skeleton-img {
    width: 100%;
    height: 146px;
    border-radius: 8px 8px 0 0;
    background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 37%, #f2f2f2 63%);
    background-size: 400% 100%;
    animation: skeleton-loading 1.4s ease infinite;
}

.skeleton-card-content {
    padding: 12px;
}

.skeleton-text {
    height: 16px;
    border-radius: 4px;
    background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 37%, #f2f2f2 63%);
    background-size: 400% 100%;
    animation: skeleton-loading 1.4s ease infinite;
}

.skeleton-btn {
    height: 36px;
    border-radius: 6px;
    background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 37%, #f2f2f2 63%);
    background-size: 400% 100%;
    animation: skeleton-loading 1.4s ease infinite;
}

@keyframes skeleton-loading {
    0% {
        background-position: 100% 50%;
    }

    100% {
        background-position: 0 50%;
    }
}
</style>
