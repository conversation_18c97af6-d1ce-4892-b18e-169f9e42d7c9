<!-- 连接顾问弹窗 -->
<template>
	<div class="">
		<el-dialog :model-value="dialogInstructionsVisible" @update:model-value="$emit('update:dialogInstructionsVisible', $event)" :before-close="handleInstructionsClose" :show-close="false" class="flex flex-col w-[314px] rounded-[18px] md:w-[400px] lg:w-[480px] p-0">
			<div class="advisor-content px-[28px] py-[24px] lg:p-[40px]">
				<div class="text-center font-['Philosopher'] text-[20px] font-bold leading-[22px] lg:text-[24px] lg:leading-[26px]">Instructions</div>
				<div class="text-center text-[14px] font-['Source_Sans_Pro'] text-[#1C2158] font-normal leading-[18px] mt-[15px] opacity-70 lg:text-[16px] lg:leading-[20px] lg:mt-[10px]">The first available advisor would answer your question once submitted.By clicking the Submit button,you have agreed that both of your question and answer to it to be SHOWN on the PUBLIC READIN page.All the private ifo including the pictures attached will be trimmed before being presented in public.</div>
                <div class="px-[20px] mt-[28px] lg:px-[90px]">
                    <button class="common-button w-full rounded-[10px] text-[16px] font-['Philosopher'] font-bold text-white h-[42px] lg:h-[50px]" @click="handleInstructionsConfirm">Got it</button>
                </div>
			</div>
		</el-dialog>
	</div>
</template>

<script lang="ts" setup name="InstructionsDialog">
const emit = defineEmits(['update:dialogInstructionsVisible','handleInstructionsConfirm'])

const props = defineProps({
	dialogInstructionsVisible: {
		type: Boolean,
		default: false,
	},
})

const handleInstructionsClose = () => {
	emit('update:dialogInstructionsVisible', false)
}

const handleInstructionsConfirm = () => {
	emit('update:dialogInstructionsVisible', false)
	emit('handleInstructionsConfirm')
}
</script>
<style lang="scss" scoped>
:deep(.el-dialog) {
	padding: 0;
	display: flex;
	flex-direction: column;

	.el-dialog__header {
		display: none;
	}

	.el-dialog__body {
		flex: 1;
		padding: 0 !important;
        .common-button{
            background: linear-gradient(91.29deg, #7AAFFF 1.1%, #7A87FF 95.36%);
        }
	}
}
</style>
