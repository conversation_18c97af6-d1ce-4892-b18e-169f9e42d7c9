<!--  -->
<template>
    <div class="app-container">
        <div class="main-content px-[15px] lg:px-[120px]">
            <el-breadcrumb :separator-icon="ArrowRight"
                class="font-['Open_Sans_3'] font-[600] text-[14px] pt-[8px] mb-[14px] md:pt-6 lg:pt-6 md:mb-[30px] lg:mb-[30px]">
                <el-breadcrumb-item class="text-[#1C2158]">Home</el-breadcrumb-item>
                <el-breadcrumb-item>Public Reading</el-breadcrumb-item>
            </el-breadcrumb>
            <div class="mt-[26px] font-['Philosopher'] font-[19px] font-blod mb-[12px]">Public Reading</div>

            <!-- 骨架屏加载 -->
            <el-skeleton :loading="loading" animated>
                <template #template>
                    <div class="skeleton-list">
                        <div v-for="i in 5" :key="i" class="skeleton-item">
                            <!-- 标题骨架 -->
                            <el-skeleton-item variant="text" style="width: 80%; height: 20px; margin-bottom: 12px;" />

                            <!-- 用户信息骨架 -->
                            <div class="skeleton-user-info">
                                <el-skeleton-item variant="circle"
                                    style="width: 36px; height: 36px; margin-right: 8px;" />
                                <div class="skeleton-user-details">
                                    <el-skeleton-item variant="text"
                                        style="width: 100px; height: 14px; margin-bottom: 4px;" />
                                    <el-skeleton-item variant="text" style="width: 80px; height: 12px;" />
                                </div>
                            </div>

                            <!-- 描述骨架 -->
                            <el-skeleton-item variant="text" style="width: 90%; height: 16px; margin: 12px 0;" />

                            <!-- 顾问信息骨架 -->
                            <div class="skeleton-consultant">
                                <div class="skeleton-consultant-top">
                                    <div class="skeleton-consultant-left">
                                        <el-skeleton-item variant="circle"
                                            style="width: 30px; height: 30px; margin-right: 8px;" />
                                        <div class="skeleton-consultant-details">
                                            <el-skeleton-item variant="text"
                                                style="width: 80px; height: 14px; margin-bottom: 6px;" />
                                            <el-skeleton-item variant="text" style="width: 60px; height: 12px;" />
                                        </div>
                                    </div>
                                    <el-skeleton-item variant="button"
                                        style="width: 80px; height: 32px; border-radius: 8px;" />
                                </div>
                                <div class="skeleton-consultant-bottom">
                                    <el-skeleton-item variant="text"
                                        style="width: 100%; height: 16px; margin-bottom: 8px;" />
                                    <el-skeleton-item variant="text" style="width: 85%; height: 16px;" />
                                </div>
                            </div>
                        </div>
                    </div>
                </template>

                <!-- 实际内容 -->
                <template #default>
                    <div class="read-list" v-if="readingList.length > 0">
                        <div class="read-item p-[12px] rounded-[10px] bg-white mt-[10px] cursor-pointer"
                            v-for="(item, index) in readingList" :key="index">
                            <div
                                class="read-item-title font-['Open_Sans'] font-bold text-[16px] text-[#1C2158] leading-[22px]">
                                {{ item.specificQuestion }}</div>
                            <div class="read-item-time flex mt-[10px]">
                                <div
                                    class="constellation w-[36px] h-[36px] rounded-[50%] flex items-center justify-center mr-[8px]">
                                    <img :src="item.userAvatar" alt="scorpio" class="w-[24px] h-[24px]" />
                                </div>
                                <!--用户信息-->
                                <div class="user-info">
                                    <div
                                        class="user-info-name font-['Open_Sans'] font-bol text-[13px] text-[#1C2158] leading-[18px]">
                                        {{ item.username }}</div>
                                    <div
                                        class="user-info-time font-['Open_Sans'] font-normal text-[11px] text-[#1C2158] leading-[14px] opacity-[0.4] mt-[2px]">
                                        {{ item.questionCreateTimestamp }}</div>
                                </div>
                            </div>
                            <!--描述信息-->
                            <div
                                class="read-item-desc mt-2 text-[15px] leading-[19px] text-normal-opacity-70 line-clamp-1 text-ellipsis">
                                {{ item.generalSituation }}</div>
                            <!--卜卦师信息-->
                            <div class="read-item-consultant mt-[11px] p-[10px]">
                                <div class="read-item-consultant-top flex items-center justify-between">
                                    <div class="top-left flex">
                                        <img :src="item.augurAvatar" alt=""
                                            class="w-[30px] h-[30px] mr-[5px] rounded-[50%] object-cover" />
                                        <div>
                                            <div class="text-[13px] text-[#1C2158] leading-[16px]">{{ item.augurUsername
                                                }}</div>
                                            <div
                                                class="px-[5px] text-[11px] font-['Source_Sans_3'] text-[#4484FF] leading-[14px] w-max rounded-[10px] border border-[#4484FF] border-solid">
                                                {{ item.constellation }}</div>
                                        </div>
                                    </div>
                                    <!--右边按钮-->
                                    <div class="top-right">
                                        <button
                                            class="connect-button py-[6px] px-[8px] leading-[18px] font-['Philosopher'] font-[16px] font-blod rounded-[8px] text-white">Connect</button>
                                    </div>
                                </div>
                                <!--卜卦师信息详情-->
                                <div class="read-item-consultant-bottom mt-[10px]">
                                    <EllipsisTextMultiline :lineClamp="2" :text="item.answerContent"
                                        class="block md:hidden"> </EllipsisTextMultiline>
                                    <div class="hidden text-[16px] text-normal-opacity-70 leading-[20px] md:block">
                                        {{ item.answerContent }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!---没有数据的时候的展示-->
                    <div v-else-if="finished && !readingList.length">
                        <!-- <img src="@/assets/common/no-data.png" alt="" class="w-[100px] h-[100px]" /> -->
                        <div class="text-[16px] text-normal-opacity-70 leading-[20px]">No data</div>
                    </div>
                </template>
            </el-skeleton>
            <!--seeMore-->
            <div class="see-more flex justify-center items-center mt-[10px] md:mt-[20px] lg:mt-[20px]"
                v-show="readingList.length > 0&&!finished" @click="getReadingListData(1)">
                <div class="see-more-item relative flex items-center px-[17px] py-[7px] rounded-[3px] bg-[#fff] cursor-pointer gap-1 md:rounded-[6px] md:px-[30px] md:py-[15px] md:border md:border-[#4484FF] md:border-solid lg:border lg:border-[#4484FF] lg:border-solid lg:px-[30px] lg:py-[15px] lg:rounded-[6px]"
                    v-loading-mask="loadingMore">
                    <img src="@/assets/common/see-more.png" alt=""
                        class="w-[15px] h-[15px] md:w-[30px] md:h-[30px] lg:w-[30px] lg:h-[30px]" />
                    <span
                        class="text-[16px] font-['Philosopher'] text-[#4484FF] font-bold md:text-[20px] lg:text-[20px]">See
                        More</span>
                </div>
            </div>
        </div>
        <!--edit 固定在右边-->
        <div class="fixed top-0 bottom-0 right-[27px] m-auto h-max flex flex-col items-center z-[100] md:right-[120px] cursor-pointer"
            @click="goToSubmitPublicReading">
            <img src="@/assets/public/edit.png" alt="" class="w-[60px] h-[60px] md:w-[88px] md:h-[88px]" />
            <div
                class="eidt_money flex items-center w-max px-[9px] py-[5px] relative mt-[-10px] bg-[#5D46EA] rounded-[100px] border border-solid border-[#fff] md:px-[12px]">
                <div class="text-[#fff] text-[14px] mr-[2px] font-medium md:text-[21px]">10</div>
                <img src="@/assets/common/money.png" alt="" class="w-[12px] h-[12px] md:w-[18px] md:h-[18px]" />
            </div>
        </div>
        <!-- <div class="fixed-loading w-[100vw] h-[100vh] fixed top-0 left-0 bg- z-[1000] flex items-center justify-center" v-loading="loading" v-show="loading">

        </div> -->
    </div>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, nextTick, computed, onMounted } from 'vue'
import { ArrowRight } from '@element-plus/icons-vue'
import EllipsisTextMultiline from '@/components/EllipsisTextMultiline.vue'
import { useUserStore } from '@/stores/user'
import { getReadingList } from '@/api/reading'
import ConnectAdvisorDialog from '../familyFriends/compoents/connectAdvisorDialog.vue'
import { useRouter } from 'vue-router'
const router = useRouter()
const userStore = useUserStore()
const showFree = computed(() => userStore.showFree)
const text = ref("Hello,it's nice to meet you.Hello,it's nice to meet you.My name is Xiao-Ming Lee. You could just Hello,it's nice to meet you.Hello,it's nice to meet you.My name is Xiao-Ming Lee. You could justHello,it's nice to meet you.Hello,it's nice to meet you.My name is Xiao-Ming Lee. You could just Hello,it's nice to meet you.Hello,it's nice to meet you.My name is Xiao-Ming Lee")
const loading = ref(false)
const loadingMore = ref(false)
const readingList = ref<any[]>([])

const current = ref(1)

const finished = ref(false)

const getReadingListData = async (type: number) => {
    console.log(type)
    if (finished.value) return

    if (type == 1) {
        loadingMore.value = true
    } else {
        // 初始加载时设置loading状态
        loading.value = true
    }

    try {
        const res = await (getReadingList({ current: current.value, size: 10 })) as {
            count:number,
            data:any
        }
        console.log(res)
        readingList.value = [...readingList.value, ...res.data]
        if (readingList.value.length >= res.count) {
            finished.value = true
        }
        current.value++
    } catch (error) {
        console.log(error)
    } finally {
        if (type == 1) {
            loadingMore.value = false
        } else {
            loading.value = false
        }
    }
}

const goToSubmitPublicReading = () => {
    router.push('/submitPublicReading')
}
onMounted(() => {
    getReadingListData(0)
})
</script>
<style lang="scss" scoped>
.read-item {
    &:hover {
        background-color: rgba($color: #7aafff, $alpha: 0.1);
        box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.25);
    }

    .constellation {
        background: linear-gradient(91.29deg, #7aafff 1.1%, #7a87ff 95.36%);
    }

    .read-item-consultant {
        background: rgba(68, 132, 255, 0.07);
    }

    .connect-button {
        background: linear-gradient(91.29deg, #7aafff 1.1%, #7a87ff 95.36%);
    }
}

// 骨架屏样式
.skeleton-list {
    .skeleton-item {
        background: white;
        border-radius: 10px;
        padding: 12px;
        margin-top: 10px;
        box-shadow: 0px 1px 5px 0px #dcdde5;
    }

    .skeleton-user-info {
        display: flex;
        align-items: center;
        margin: 12px 0;

        .skeleton-user-details {
            flex: 1;
        }
    }

    .skeleton-consultant {
        margin-top: 12px;
        padding: 10px;
        background: rgba(68, 132, 255, 0.07);
        border-radius: 8px;

        .skeleton-consultant-top {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 10px;

            .skeleton-consultant-left {
                display: flex;
                align-items: center;

                .skeleton-consultant-details {
                    flex: 1;
                }
            }
        }

        .skeleton-consultant-bottom {
            margin-top: 10px;
        }
    }
}

// Element Plus Skeleton 样式覆盖
:deep(.el-skeleton) {
    width: 100%;
}

:deep(.el-skeleton__item) {
    background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 37%, #f2f2f2 63%);
    background-size: 400% 100%;
    animation: skeleton-loading 1.4s ease infinite;
}

@keyframes skeleton-loading {
    0% {
        background-position: 100% 50%;
    }

    100% {
        background-position: 0 50%;
    }
}

// 无障碍支持
@media (prefers-reduced-motion: reduce) {
    :deep(.el-skeleton__item) {
        animation: none;
    }
}
</style>
