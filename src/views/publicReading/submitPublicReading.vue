<!-- live text chat -->
<template>
    <div class="app-container">
        <div class="main-content px-[15px] lg:px-[120px]">
            <el-breadcrumb :separator-icon="ArrowRight"
                class="font-['Open_Sans_3'] font-[600] text-[14px] pt-[8px] mb-[14px] md:pt-6 lg:pt-6 md:mb-[30px] lg:mb-[30px]">
                <el-breadcrumb-item class="text-[#1C2158]">Home</el-breadcrumb-item>
                <el-breadcrumb-item>Public Reading</el-breadcrumb-item>
            </el-breadcrumb>

            <div
                class="main-content-detail flex flex-col md:bg-white lg:bg-white md:px-[40px] md:pb-[70px] md:pt-[40px] lg:px-[40px] lg:pb-[70px] lg:pt-[40px] rounded-[30px]">
                <!---表单--->
                <div class="mt-[20px] lg:mt-0 lg:flex-1 lg:mr-[70px]">
                    <div
                        class="form-title font-['Philosopher'] font-bold text-[#1C2158] mb-[12px] text-[18px] lg:text-[22px]">
                        Public Reading</div>
                    <div class="form-content">
                        <el-form :model="publicReadingForm" :rules="liveTextChatRules" ref="liveTextChatRef"
                            label-width="100px" label-position="top" :hide-required-asterisk="true">
                            <div class="lg:grid grid-cols-2 gap-10">
                                <el-form-item prop="specificQuestion">
                                    <template #label>
                                        <span data-content="(*Required )">Specific Question</span>
                                    </template>
                                    <div class="flex flex-wrap gap-2 pb-[8px] lg:bg-white">
                                        <div :class="{ 'question-active': publicReadingForm.specificQuestion == item }"
                                            class="question-item translation duration-500 py-[5px] px-[8px] text-[12px] text-normal-opacity-70 font-['Source_Sans_3'] leading-[1] border border-[#60648A] border-solid rounded-[4px] cursor-pointer"
                                            v-for="item in specificQuestionList" :key="item"
                                            @click="changeSpecificQuestion(item)">{{ item }}</div>
                                    </div>
                                    <el-input v-model="publicReadingForm.specificQuestion" maxlength="200"
                                        placeholder="Describing your situation in certain sentences will help the advisor know your status better to futher imporve the accuracy and the service quality."
                                        show-word-limit type="textarea" :rows="5" class="rounded-[6px] text-[12px]" />
                                </el-form-item>
                                <div class="formatter-form">
                                    <el-form-item prop="generalSituation">
                                        <template #label>
                                            <span data-content="(*Required )">General Situation</span>
                                        </template>
                                        <el-input v-model="publicReadingForm.generalSituation" maxlength="3000"
                                            :autosize="false"
                                            placeholder="Describing your situation in certain sentences will help the advisor know your status better to futher imporve the accuracy and the service quality."
                                            show-word-limit type="textarea" :rows="6"
                                            class="rounded-[6px] text-[12px]" />
                                    </el-form-item>
                                </div>
                            </div>

                            <div
                                class="form-title font-['Philosopher'] font-bold text-[#1C2158] mt-[32px] mb-[12px] text-[18px] lg:text-[22px]">
                                Basic Information</div>
                            <div class="lg:grid grid-cols-2 gap-10">
                                <el-form-item>
                                    <template #label>
                                        <span data-content="(*Required )">Name</span>
                                    </template>
                                    <el-input :placeholder="'Provide your last name'" v-model="publicReadingForm.name"
                                        :clearable="true"></el-input>
                                </el-form-item>
                                <el-form-item :label="'Birth Date'" required>
                                    <template #label>
                                        <span data-content="(*Required )">Birth Date</span>
                                    </template>
                                    <div class="relative w-full" ref="birthDateRef">
                                        <el-input :placeholder="'Select your birth date'"
                                            v-model="publicReadingForm.birthDate" readonly @click="toggleCalendar">
                                            <template #suffix>
                                                <img src="@/assets/common/calendar.png" alt=""
                                                    class="w-[24px] h-[24px]" />
                                            </template>
                                        </el-input>
                                        <Teleport to="body">
                                            <Transition enter-active-class="transition duration-200 ease-out"
                                                enter-from-class="opacity-0 transform scale-95 -translate-y-2"
                                                enter-to-class="opacity-100 transform scale-100 translate-y-0"
                                                leave-active-class="transition duration-150 ease-in"
                                                leave-from-class="opacity-100 transform scale-100 translate-y-0"
                                                leave-to-class="opacity-0 transform scale-95 -translate-y-2">
                                                <div v-if="showCalendar" :style="calendarStyle"
                                                    class="fixed z-[4000] shadow-lg origin-top calendar-dropdown rounded-[20px]">
                                                    <CustomCalendar v-model="selectedDate" @confirm="handleDateConfirm"
                                                        @cancel="showCalendar = false" />
                                                </div>
                                            </Transition>
                                        </Teleport>
                                    </div>
                                </el-form-item>
                            </div>
                            <div class="lg:grid grid-cols-2 gap-10">
                                <el-form-item :label="'Birth Time'" required>
                                    <template #label>
                                        <span data-content="(*Required )">Birth Time</span>
                                    </template>
                                    <div class="relative w-full" ref="birthTimeRef">
                                        <el-input :placeholder="'Fill your birth time'"
                                            v-model="publicReadingForm.birthTime" readonly @click="toggleTimePicker">
                                        </el-input>
                                        <!-- 时间选择器弹出层 -->
                                        <Teleport to="body">
                                            <Transition enter-active-class="transition duration-200 ease-out"
                                                enter-from-class="opacity-0 transform scale-95 -translate-y-2"
                                                enter-to-class="opacity-100 transform scale-100 translate-y-0"
                                                leave-active-class="transition duration-150 ease-in"
                                                leave-from-class="opacity-100 transform scale-100 translate-y-0"
                                                leave-to-class="opacity-0 transform scale-95 -translate-y-2">
                                                <div v-if="showTimePicker" :style="timePickerStyle"
                                                    class="fixed z-[4000] shadow-lg origin-top calendar-dropdown">
                                                    <CustomTimePicker v-model="selectedTime"
                                                        @confirm="handleTimeConfirm" @cancel="showTimePicker = false" />
                                                </div>
                                            </Transition>
                                        </Teleport>
                                    </div>
                                </el-form-item>

                                <el-form-item :label="'Birthplace'" required class="white-bg" prop="birthPlace">
                                    <template #label>
                                        <span data-content="(*Required )">Birthplace</span>
                                    </template>
                                    <SmSearchRemote v-model="publicReadingForm.birthPlace"
                                        :remote-api="searchBirthPlace" @changePlace="handleChangePlace"
                                        :placeholder="'Select your birthplace'" />
                                </el-form-item>
                            </div>
                            <div class="lg:grid grid-cols-2 gap-10">
                                <el-form-item :label="'Gender'" required>
                                    <template #label>
                                        <span data-content="(*Required )">Gender</span>
                                    </template>
                                    <div class="bg-white rounded-[6px] w-full flex items-center lg:bg-[#F8F8FA]">
                                        <div class="gender-item relative flex-1 flex flex-col justify-center items-center pt-[10px] pb-[12px] cursor-pointer lg:pt-[13px] lg:pb-[5px]"
                                            v-for="(item, index) in genderOptions"
                                            :class="{ 'active-gender lg:bg-white': publicReadingForm.gender == item.value }"
                                            @click="changeGender(item.value)">
                                            <img :src="publicReadingForm.gender == item.value ? item.selectIcon : item.noSelectIcon"
                                                alt="" class="w-[24px] h-[24px]" />
                                            <span
                                                class="text-[12px] font-['Source_Sans_Pro'] mt-[6px] opacity-50 leading-[12px] lg:text-[14px] lg:leading-[14px] lg:mt-[10px]"
                                                :class="{ 'text-[#4484FF]': publicReadingForm.gender == item.value }">{{
                                                    item.label }}</span>
                                        </div>
                                    </div>
                                    <!-- <CustomeSelect v-model="publicReadingForm.gender" :options="genderOptions" placeholder="Select your gender" /> -->
                                </el-form-item>
                            </div>

                            <div class="form-button px-[5px] mt-[36px]  lg:mt-[43px] lg:px-[335px]">
                                <button
                                    class="confirm-button w-full rounded-[12px] font-['Philosopher'] text-white h-[44px] text-[16px]"
                                    type="button" @click="submitForm" v-loading-mask="submitLoading"
                                    :disabled="submitLoading">
                                    Submit
                                </button>
                            </div>
                        </el-form>
                    </div>
                </div>
            </div>
        </div>
        <InstructionsDialog :dialog-instructions-visible="dialogInstructionsVisible"
            @update:dialogInstructionsVisible="handleInstructionsVisible"
            @handleInstructionsConfirm="handleInstructionsConfirm"></InstructionsDialog>
    </div>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, nextTick, computed, onMounted } from 'vue'
import { ArrowRight } from '@element-plus/icons-vue'
import CustomCalendar from '@/components/CustomCalendar.vue'
import CustomTimePicker from '@/components/CustomTimePicker.vue'
import InstructionsDialog from './components/instructionsDialog.vue'
import type { FormInstance } from 'element-plus'
import { getAugurOrderCommonQuestion } from '@/api/order'
import { addPublicReading } from '@/api/publicReading'
import SmSearchRemote from '@/components/smSearchRemote/smSearchRemote.vue'
import { searchBirthPlace } from '@/api/common'

const dialogInstructionsVisible = ref(false)
const liveTextChatRef = ref<FormInstance>()
const submitLoading = ref(false)
const liveTextChatRules = reactive({
    specificQuestion: [{ required: true, message: 'Please select a specific question', trigger: 'blur' }],
    // name: [{ required: true, message: 'Please enter your name', trigger: 'blur' }],
    birthDate: [{ required: true, message: 'Please select your birth date', trigger: 'change' }],
    birthTime: [{ required: true, message: 'Please select your birth time', trigger: 'change' }],
    gender: [{ required: true, message: 'Please select your gender', trigger: 'change' }],
    birthPlace: [{ required: true, message: 'Please select your birthplace', trigger: 'change' }],
    generalSituation: [{ required: true, message: 'Please enter your general situation', trigger: 'change' }],
})
const genderOptions = [
    { label: 'Male', value: '1', selectIcon: new URL('@/assets/common/boy-select.png', import.meta.url).href, noSelectIcon: new URL('@/assets/common/boy-unselect.png', import.meta.url).href },
    { label: 'Female', value: '2', selectIcon: new URL('@/assets/common/gril-select.png', import.meta.url).href, noSelectIcon: new URL('@/assets/common/gril-unselect.png', import.meta.url).href },
    { label: 'Non-binary', value: '3', selectIcon: new URL('@/assets/common/no-select.png', import.meta.url).href, noSelectIcon: new URL('@/assets/common/no-unselect.png', import.meta.url).href },
]

const publicReadingForm = reactive({
    specificQuestion: '',
    name: '',
    lastName: '',
    gender: '1',
    birth: '',
    birthDate: '',
    birthTime: '',
    birthPlace: '',
    generalSituation: '',
    latitude: '',
    longitude: '',
})
const specificQuestionList = ref<string[]>([])
const showCalendar = ref(false)
const selectedDate = ref<Date>()
const birthDateRef = ref<HTMLElement | null>(null)
const calendarPosition = ref({ top: 0, left: 0 })

const showTimePicker = ref(false)
const selectedTime = ref<{ hour: number; minute: number } | undefined>(undefined)
const birthTimeRef = ref<HTMLElement | null>(null)
const timePickerPosition = ref({ top: 0, left: 0 })

const publicReadingLoading = ref(false)

//
const changeGender = (gender: string) => {
    publicReadingForm.gender = gender
}
const handleChangePlace = (data: any) => {
    publicReadingForm.latitude = data.latitude
    publicReadingForm.longitude = data.longitude
}
// 计算日历样式
const calendarStyle = computed(() => {
    return {
        top: `${calendarPosition.value.top}px`,
        left: `${calendarPosition.value.left}px`,
    }
})

// 计算日历位置
const updateCalendarPosition = () => {
    if (!birthDateRef.value) return

    const rect = birthDateRef.value.getBoundingClientRect()
    const windowHeight = window.innerHeight
    const calendarHeight = 350 // 日历的预估高度

    // 检查是否有足够空间在下方显示
    if (rect.bottom + calendarHeight < windowHeight) {
        // 在输入框下方显示
        calendarPosition.value = {
            top: rect.bottom + 8 + window.scrollY,
            left: rect.left + window.scrollX,
        }
    } else {
        // 在输入框上方显示
        calendarPosition.value = {
            top: rect.top - calendarHeight - 8 + window.scrollY,
            left: rect.left + window.scrollX,
        }
    }
}

// 切换日历显示并计算位置
const toggleCalendar = () => {
    if (!showCalendar.value) {
        // 延迟一下计算位置，确保DOM更新
        nextTick(() => {
            updateCalendarPosition()
        })
    }
    showCalendar.value = !showCalendar.value
}

// 处理日期确认
const handleDateConfirm = (date: Date) => {
    if (date) {
        publicReadingForm.birthDate = date.toLocaleDateString()
    }
    showCalendar.value = false
}

// 处理滚动事件
const handleScroll = () => {
    if (showCalendar.value) {
        updateCalendarPosition()
    }
}

// 监听点击事件，当点击组件外部时关闭日历
const closeCalendarOnClickOutside = (e: MouseEvent) => {
    const target = e.target as HTMLElement
    if (showCalendar.value && birthDateRef.value && !birthDateRef.value.contains(target) && !target.closest('.calendar-dropdown')) {
        showCalendar.value = false
    }
}

// 计算时间选择器样式
const timePickerStyle = computed(() => {
    return {
        top: `${timePickerPosition.value.top}px`,
        left: `${timePickerPosition.value.left}px`,
    }
})

// 计算时间选择器位置
const updateTimePickerPosition = () => {
    if (!birthTimeRef.value) return

    const rect = birthTimeRef.value.getBoundingClientRect()
    const windowHeight = window.innerHeight
    const timePickerHeight = 280 // 时间选择器的预估高度

    // 检查是否有足够空间在下方显示
    if (rect.bottom + timePickerHeight < windowHeight) {
        // 在输入框下方显示
        timePickerPosition.value = {
            top: rect.bottom + 8 + window.scrollY,
            left: rect.left + window.scrollX,
        }
    } else {
        // 在输入框上方显示
        timePickerPosition.value = {
            top: rect.top - timePickerHeight - 8 + window.scrollY,
            left: rect.left + window.scrollX,
        }
    }
}

// 切换时间选择器显示并计算位置
const toggleTimePicker = () => {
    if (!showTimePicker.value) {
        // 延迟一下计算位置，确保DOM更新
        nextTick(() => {
            updateTimePickerPosition()
        })
    }
    showTimePicker.value = !showTimePicker.value
}

// 处理时间确认
const handleTimeConfirm = (time: { hour: number; minute: number }) => {
    if (time) {
        publicReadingForm.birthTime = `${time.hour.toString().padStart(2, '0')}:${time.minute.toString().padStart(2, '0')}`
    }
    showTimePicker.value = false
}

// 处理滚动事件
const handleTimeScroll = () => {
    if (showTimePicker.value) {
        updateTimePickerPosition()
    }
}

// 监听点击事件，当点击组件外部时关闭时间选择器
const closeTimePickerOnClickOutside = (e: MouseEvent) => {
    const target = e.target as HTMLElement
    if (showTimePicker.value && birthTimeRef.value && !birthTimeRef.value.contains(target) && !target.closest('.calendar-dropdown')) {
        showTimePicker.value = false
    }
}

const handleInstructionsVisible = (value: boolean) => {
    dialogInstructionsVisible.value = value
}
const getAugurOrderCommonQuestionFunc = () => {
    getAugurOrderCommonQuestion()
        .then((response) => {
            specificQuestionList.value = response.data
        })
        .catch((error) => { })
}
// 更改具体问题
const changeSpecificQuestion = (item: string) => {
    publicReadingForm.specificQuestion = item
}
// 表单提交函数
const submitForm = async () => {
    if (!liveTextChatRef.value) return

    submitLoading.value = true
    try {
        await liveTextChatRef.value.validate((valid: boolean) => {
            if (valid) {
                console.log('表单验证通过，提交数据:', publicReadingForm)
                dialogInstructionsVisible.value = true
            } else {
                console.log('表单验证失败')
            }
        })
    } catch (error) {
        console.error('提交失败:', error)
    } finally {
        submitLoading.value = false
    }
}
const handleInstructionsConfirm = () => {
    publicReadingForm.birth = String(new Date(publicReadingForm.birthDate + ' ' + publicReadingForm.birthTime).getTime() / 1000)
    addPublicReading({
        name: publicReadingForm.name,
        gender: publicReadingForm.gender,
        birth: publicReadingForm.birth,
        generalSituation: publicReadingForm.generalSituation,
        specificQuestion: publicReadingForm.specificQuestion,
        birthPlace: publicReadingForm.birthPlace,
    })
        .then((res) => {
            console.log(res)
        })
        .catch((err) => {
            console.log(err)
        }).finally(() => {
            publicReadingLoading.value = false
        })
}
onMounted(() => {
    getAugurOrderCommonQuestionFunc()
})
// 添加/移除全局点击事件和滚动事件监听器
watch(showCalendar, (val) => {
    if (val) {
        setTimeout(() => {
            document.addEventListener('click', closeCalendarOnClickOutside)
            window.addEventListener('scroll', handleScroll, true)
            window.addEventListener('resize', updateCalendarPosition)
        }, 0)
    } else {
        document.removeEventListener('click', closeCalendarOnClickOutside)
        window.removeEventListener('scroll', handleScroll, true)
        window.removeEventListener('resize', updateCalendarPosition)
    }
})

watch(showTimePicker, (val) => {
    if (val) {
        setTimeout(() => {
            document.addEventListener('click', closeTimePickerOnClickOutside)
            window.addEventListener('scroll', handleTimeScroll, true)
            window.addEventListener('resize', updateTimePickerPosition)
        }, 0)
    } else {
        document.removeEventListener('click', closeTimePickerOnClickOutside)
        window.removeEventListener('scroll', handleTimeScroll, true)
        window.removeEventListener('resize', updateTimePickerPosition)
    }
})
</script>
<style lang="scss" scoped>
.equity {
    background: linear-gradient(91.29deg, rgba(122, 175, 255, 0.1) 1.1%, rgba(122, 135, 255, 0.1) 95.36%);
}

:deep(.el-form) {
    .el-form-item {
        .el-form-item__label {
            font-size: 14px;
            font-weight: 700;
            font-family: 'Philosopher';

            span {
                &::after {
                    content: attr(data-content);
                    font-size: 12px;
                    color: #ff5353;
                    margin-left: 4px;
                    font-weight: 400;
                }
            }
        }

        &.white-bg {
            .el-form-item__content {
                background-color: #fff;
            }
        }

        .el-form-item__content {

            .el-input__wrapper,
            .el-select__wrapper {
                height: 44px;
                font-size: 15px;
                border-radius: 6px;
                font-weight: 400;
                color: #1c2158;
                font-family: 'Open Sans';
            }

            .el-input__inner {
                color: #1c2158;
            }
        }
    }

    @media screen and (min-width: 1330px) {
        .el-form-item {
            margin-bottom: 32px;

            .el-form-item__label {
                font-size: 22px;
                font-weight: 700;
                font-family: 'Philosopher';
                // margin-bottom: 24px;
            }

            &.white-bg {
                .el-form-item__content {
                    background-color: #f8f8fa;
                }
            }

            .el-form-item__content {
                background-color: #f8f8fa;

                .el-textarea__inner {
                    font-size: 16px;
                    border-radius: 12px;
                    background-color: #f8f8fa;
                    color: #1c2158;
                    font-family: 'Open Sans';
                }

                .el-input__wrapper,
                .el-select__wrapper {
                    height: 50px;
                    font-size: 20px;
                    border-radius: 12px;
                    background-color: transparent;
                    color: #1c2158;
                    font-family: 'Open Sans';
                }
            }
        }

        .formatter-form {
            margin-bottom: 27px;

            .el-form-item {
                width: 100%;
                height: 100%;
                display: flex;
                flex-direction: column;
                margin-bottom: 0;

                .el-form-item__content {
                    flex: 1;

                    .el-textarea {
                        width: 100%;
                        height: 100%;

                        .el-textarea__inner {
                            height: 100%;
                        }
                    }
                }
            }
        }
    }
}

.active-gender {
    &::before {
        content: '';
        position: absolute;
        inset: 0;
        width: 100%;
        height: 100%;
        padding: 1px;
        border-radius: 6px;
        background: linear-gradient(104.04deg, #96baff 0%, #7064ff 86.39%);
        -webkit-mask: linear-gradient(#96baff 0 0) content-box, linear-gradient(#96baff 0 0);
        -webkit-mask-composite: xor;
        mask-composite: exclude;
        pointer-events: none;
    }
}

.confirm-button {
    background: linear-gradient(91.29deg, #7aafff 1.1%, #7a87ff 95.36%);

    &:hover {
        box-shadow: 0px 0px 12px 0px rgba(0, 0, 0, 0.25);
    }
}

/* 问题选择激活状态样式 */
.question-active {
    color: rgba(68, 132, 255, 0.7);
    border-color: #4484ff;
}
</style>
