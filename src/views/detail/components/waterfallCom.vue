<template>
    <div class="waterfall-container" ref="container">
        <div class="waterfall-column" 
             v-for="(column, columnIndex) in columns" 
             :key="columnIndex">
            <div v-for="item in column" 
                 :key="item.id" 
                 class="waterfall-item"
                 :style="{ height: item.height + 'px' }">
                <div class="relative overflow-hidden rounded-lg group h-full">
                    <!-- 骨架屏 -->
                    <div v-show="!loadedImages.has(item.id)" 
                         class="skeleton-loader absolute inset-0">
                    </div>
                    <!-- 图片容器 -->
                    <div class="absolute inset-0" 
                         :style="{ opacity: loadedImages.has(item.id) ? 1 : 0 }"
                         :class="{ 'transition-opacity duration-300': true }">
                        <img :src="item.url" 
                             :alt="item.title"
                             class="w-full h-full object-cover"
                             loading="lazy"
                             @load="onImageLoad(item)"
                        >
                        <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-4 transform transition-transform duration-300 translate-y-full group-hover:translate-y-0">
                            <h3 class="text-white text-sm font-medium">{{ item.title }}</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, watch } from 'vue';
import { useThrottleFn } from '@vueuse/core';

interface WaterfallItem {
    id: number;
    url: string;
    title: string;
    height: number;
    aspectRatio?: number;
}

const props = defineProps<{
    items: WaterfallItem[]
}>();

const container = ref<HTMLElement | null>(null);
const columnCount = ref(3);
const columns = ref<WaterfallItem[][]>([]);
const loadedImages = ref(new Set<number>());
const columnHeights = ref<number[]>([]);

// 计算列宽
const columnWidth = computed(() => {
    if (!container.value) return 0;
    const gap = 16;
    return (container.value.offsetWidth - (gap * (columnCount.value - 1))) / columnCount.value;
});

// 初始化布局
const initializeLayout = () => {
    // 设置初始列数
    const width = container.value?.offsetWidth || 0;
    columnCount.value = width < 640 ? 2 : width < 1024 ? 3 : 4;
    
    // 初始化列
    columns.value = Array.from({ length: columnCount.value }, () => []);
    columnHeights.value = Array(columnCount.value).fill(0);
    
    // 分配项目到列
    distributeItems(props.items);
};

// 分配项目到列
const distributeItems = (items: WaterfallItem[]) => {
    // 重置列
    columns.value = Array.from({ length: columnCount.value }, () => []);
    columnHeights.value = Array(columnCount.value).fill(0);
    
    // 分配项目
    items.forEach(item => {
        // 如果没有高度，设置一个默认高度
        if (!item.height) {
            item.height = 300; // 默认高度
        }
        
        // 找到最短的列
        const shortestColumnIndex = columnHeights.value.indexOf(Math.min(...columnHeights.value));
        
        // 添加项目到最短列
        columns.value[shortestColumnIndex].push(item);
        columnHeights.value[shortestColumnIndex] += item.height + 16; // 加上间距
    });
};

// 处理图片加载
const onImageLoad = (item: WaterfallItem) => {
    if (loadedImages.value.has(item.id)) return;
    
    const img = new Image();
    img.onload = () => {
        // 计算实际高度
        const newHeight = (img.height / img.width) * columnWidth.value;
        
        // 更新高度
        item.height = newHeight;
        loadedImages.value.add(item.id);
        
        // 重新分配项目
        distributeItems(props.items);
    };
    img.src = item.url;
};

// 监听窗口大小变化
const handleResize = useThrottleFn(() => {
    if (!container.value) return;
    
    const width = container.value.offsetWidth;
    const newColumnCount = width < 640 ? 2 : width < 1024 ? 3 : 4;
    
    if (newColumnCount !== columnCount.value) {
        columnCount.value = newColumnCount;
        distributeItems(props.items);
    }
}, 200);

// 监听items变化
watch(() => props.items, (newItems) => {
    distributeItems(newItems);
}, { deep: true });

onMounted(() => {
    initializeLayout();
    
    // 设置 ResizeObserver
    const resizeObserver = new ResizeObserver(handleResize);
    if (container.value) {
        resizeObserver.observe(container.value);
    }
    
    // 开始加载图片
    props.items.forEach(item => {
        onImageLoad(item);
    });
    
    onUnmounted(() => {
        resizeObserver.disconnect();
    });
});
</script>

<style lang="scss" scoped>
.waterfall-container {
    width: 100%;
    display: flex;
    gap: 16px;
    padding: 16px;
}

.waterfall-column {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.waterfall-item {
    width: 100%;
    transition: height 0.3s ease;
}

.skeleton-loader {
    background: linear-gradient(
        90deg,
        rgba(190, 190, 190, 0.2) 25%,
        rgba(129, 129, 129, 0.24) 37%,
        rgba(190, 190, 190, 0.2) 63%
    );
    background-size: 400% 100%;
    animation: skeleton-loading 1.4s ease infinite;
}

@keyframes skeleton-loading {
    0% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0 50%;
    }
}
</style>
