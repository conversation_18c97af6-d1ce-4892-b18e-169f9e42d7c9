
<template>
    <div class="app-container dark:bg-black">
        <div class="app-container-left">
            <div class="p-4">
                <h1 class="text-2xl font-bold mb-4">图片库</h1>
                <div class="space-y-4">
                    <div class="bg-white dark:bg-gray-800 rounded-lg p-4 shadow">
                        <h2 class="text-lg font-semibold mb-2">分类</h2>
                        <div class="space-y-2">
                            <button 
                                v-for="category in categories" 
                                :key="category.id"
                                class="block w-full text-left px-3 py-2 rounded hover:bg-gray-100 dark:hover:bg-gray-700"
                                :class="{'bg-primary-100 dark:bg-gray-700': selectedCategory === category.id}"
                                @click="selectedCategory = category.id"
                            >
                                {{ category.name }}
                            </button>
                            <button 
                                class="block w-full text-left px-3 py-2 rounded hover:bg-gray-100 dark:hover:bg-gray-700"
                                :class="{'bg-primary-100 dark:bg-gray-700': selectedCategory === null}"
                                @click="selectedCategory = null"
                            >
                                全部
                            </button>
                        </div>
                    </div>
                    <div class="bg-white dark:bg-gray-800 rounded-lg p-4 shadow">
                        <p class="text-sm text-gray-600 dark:text-gray-400">
                            当前显示: {{ filteredImages.length }} 张图片
                        </p>
                    </div>
                </div>
            </div>
        </div>
        <div class="app-container-right">
            <waterfallCom :items="filteredImages"></waterfallCom>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import waterfallCom from './components/waterfallCom.vue';

interface Category {
    id: number;
    name: string;
}

interface ImageItem {
    id: number;
    url: string;
    title: string;
    height: number;
    categoryId: number;
}

const selectedCategory = ref<number | null>(null);

const categories = ref<Category[]>([
    { id: 1, name: '风景' },
    { id: 2, name: '人物' },
    { id: 3, name: '建筑' },
    { id: 4, name: '美食' },
]);

// 生成随机图片数据的工具函数
const generateImages = (count: number): ImageItem[] => {
    const titlePrefixes = {
        1: ['山川', '自然', '海景', '森林', '瀑布', '草原', '沙漠', '雪景', '日落', '湖泊'],
        2: ['人像', '写真', '街拍', '生活', '表情', '群像', '特写', '剪影', '儿童', '老人'],
        3: ['现代', '古典', '地标', '桥梁', '城市', '住宅', '庭院', '公园', '商业', '工业'],
        4: ['美食', '甜点', '饮品', '中餐', '西餐', '日料', '烘焙', '街食', '农家', '海鲜'],
    };

    const titleSuffixes = ['之美', '掠影', '印象', '纪实', '特辑', '全景', '精选', '专辑', '记录', '写生'];

    return Array.from({ length: count }, (_, index) => {
        const categoryId = Math.floor(Math.random() * 4) + 1;
        const prefixes = titlePrefixes[categoryId as keyof typeof titlePrefixes];
        const prefix = prefixes[Math.floor(Math.random() * prefixes.length)];
        const suffix = titleSuffixes[Math.floor(Math.random() * titleSuffixes.length)];
        
        // 使用不同的尺寸来创建视觉变化
        const width = 400;
        const height = Math.floor(Math.random() * 400) + 300; // 300-700之间的随机高度

        return {
            id: index + 1,
            url: `https://picsum.photos/${width}/${height}?random=${index}`,
            title: `${prefix}${suffix}`,
            height: 0,
            categoryId
        };
    });
};

// 使用 ref 存储生成的图片，确保只生成一次
const images = ref<ImageItem[]>([]);

// 在组件挂载时生成图片，而不是每次渲染都生成
onMounted(() => {
    if (images.value.length === 0) {
        images.value = generateImages(50);
    }
});

// 根据选择的分类过滤图片
const filteredImages = computed(() => {
    if (!selectedCategory.value) return images.value;
    return images.value.filter(img => img.categoryId === selectedCategory.value);
});
</script>
<style lang='scss' scoped>
.app-container {
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column; // 默认上下布局

    @media (min-width: 768px) { // md breakpoint
        flex-direction: row; // 宽度大于768px时改为左右布局
    }

    &-left {
        width: 100%; // 移动端时占满宽度
        background-color: var(--el-bg-color);
        border-bottom: 1px solid var(--el-border-color-light);

        @media (min-width: 768px) {
            width: 300px; // 桌面端固定宽度
            height: 100vh;
            border-right: 1px solid var(--el-border-color-light);
            border-bottom: none;
            flex-shrink: 0; // 防止左侧区域被压缩
        }
    }

    &-right {
        flex: 1;
        min-height: 0; // 修复 Firefox 中的 flex 布局问题
        overflow: hidden; // 防止溢出

        @media (min-width: 768px) {
            height: 100vh;
            overflow-y: auto; // 桌面端时允许右侧内容滚动
        }
    }
}

// 暗色模式适配
:root.dark {
    .app-container {
        &-left {
            background-color: var(--el-bg-color-overlay);
            border-color: var(--el-border-color-darker);
        }
    }
}
</style>
