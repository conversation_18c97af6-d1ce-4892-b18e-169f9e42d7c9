<!-- app-container -->
<template>
    <div class="app-container">
        <div class="main-content px-[15px] md:px-[15px] lg:px-[120px]">
            <el-breadcrumb :separator-icon="ArrowRight"
                class="font-['Open_Sans_3'] font-[600] text-[14px] pt-[8px] mb-[14px] md:pt-6 lg:pt-6 md:mb-[30px] lg:mb-[30px]">
                <el-breadcrumb-item class="text-[#1C2158]">Home</el-breadcrumb-item>
                <el-breadcrumb-item>My Orders</el-breadcrumb-item>
                <el-breadcrumb-item>{{ orderDetail.augur?.username }}</el-breadcrumb-item>
            </el-breadcrumb>
            <el-skeleton :loading="loading" animated>
                <template #template>
                    <div class="order-list-container flex">
                        <div class="shrink-0 hidden md:block" style="width:220px;">
                            <el-skeleton-item variant="rect" style="width:100%;height:400px;border-radius:16px;" />
                        </div>
                        <div class="flex-1 md:bg-white md:ml-[20px] md:rounded-[30px] md:p-[40px] overflow-auto">
                            <div class="order-header flex items-center justify-between mb-6">
                                <el-skeleton-item variant="h1" style="width:120px;height:28px;" />
                                <div class="hidden md:flex gap-3">
                                    <el-skeleton-item variant="rect"
                                        style="width:90px;height:36px;border-radius:6px;" />
                                    <el-skeleton-item variant="rect"
                                        style="width:90px;height:36px;border-radius:6px;" />
                                </div>
                            </div>
                            <div
                                class="order-content mt-[12px]  rounded-[10px] md:mt-[20px] md:p-[30px] md:border-[1px] md:border-[#1C215814] md:border-solid md:rounded-[20px]">
                                <div class="flex items-center justify-between mb-4">
                                    <div class="flex items-center">
                                        <el-skeleton-item variant="circle"
                                            style="width:50px;height:50px;margin-right:10px;" />
                                        <div>
                                            <el-skeleton-item variant="text"
                                                style="width:100px;height:18px;margin-bottom:8px;" />
                                            <el-skeleton-item variant="text" style="width:60px;height:14px;" />
                                        </div>
                                    </div>
                                    <el-skeleton-item variant="rect"
                                        style="width:80px;height:32px;border-radius:6px;" />
                                </div>
                                <div class="flex flex-col md:flex-row gap-3 mb-4">
                                    <el-skeleton-item variant="text" style="width:120px;height:16px;" />
                                    <el-skeleton-item variant="text" style="width:120px;height:16px;" />
                                    <el-skeleton-item variant="text" style="width:120px;height:16px;" />
                                </div>
                                <div class="flex flex-col md:flex-row gap-3 mb-4">
                                    <el-skeleton-item variant="text" style="width:100px;height:16px;" />
                                    <el-skeleton-item variant="text" style="width:100px;height:16px;" />
                                    <el-skeleton-item variant="text" style="width:100px;height:16px;" />
                                </div>
                            </div>
                            <div
                                class="reply-container mt-6 md:px-[30px] md:pt-[33px] md:pb-[50px] md:border-[1px] md:border-[#1C215814] md:border-solid md:rounded-[20px]">
                                <el-skeleton-item variant="h1" style="width:80px;height:22px;margin-bottom:12px;" />
                                <div class="flex items-center mb-2">
                                    <el-skeleton-item variant="circle"
                                        style="width:30px;height:30px;margin-right:10px;" />
                                    <el-skeleton-item variant="text" style="width:80px;height:16px;" />
                                </div>
                                <el-skeleton-item variant="text" style="width:100%;height:16px;margin-bottom:6px;" />
                                <el-skeleton-item variant="text" style="width:90%;height:16px;margin-bottom:6px;" />
                                <el-skeleton-item variant="text" style="width:80%;height:16px;" />
                            </div>
                            <div class="order-attach-img flex gap-[10px] mt-6">
                                <el-skeleton-item variant="rect" style="width:86px;height:98px;border-radius:6px;" />
                                <el-skeleton-item variant="rect" style="width:86px;height:98px;border-radius:6px;" />
                                <el-skeleton-item variant="rect" style="width:86px;height:98px;border-radius:6px;" />
                            </div>
                        </div>
                    </div>
                </template>
                <template #default>
                    <div class="order-list-container flex">
                        <OrderLeft class="shrink-0 hidden md:block" :active-item="'My Orders'" />
                        <div class="flex-1 md:bg-white md:rounded-[30px] md:p-[40px] overflow-auto">
                            <div class="order-header flex items-center justify-between">
                                <div
                                    class="order-header-title text-[18px] leading-[20px] text-[#1C2158] font-['Philosopher'] font-bold md:text-[28px] md:leading-[32px]">
                                    Order Details</div>
                                <div class="hidden md:block">
                                    <div class="flex items-center">
                                        <div
                                            class="order-header-btn flex items-center px-[12px] py-[9px] rounded-[4px] border-[1px] border-[#1C215814] border-solid mr-[15px]">
                                            <img src="@/assets/order/review.png"
                                                class="w-[18px] h-[18px] md:w-[24px] md:h-[24px] mr-[6px]" />
                                            <div
                                                class="text-[14px] leading-[20px] text-[#1C2158] font-['Source_Sans_Pro']">
                                                My
                                                Review</div>
                                        </div>
                                        <div
                                            class="order-header-btn flex items-center px-[12px] py-[9px] rounded-[4px] border-[1px] border-[#1C215814] border-solid">
                                            <img src="@/assets/order/advisor.png"
                                                class="w-[18px] h-[18px] md:w-[24px] md:h-[24px] mr-[6px]" />
                                            <div
                                                class="text-[14px] leading-[20px] text-[#1C2158] font-['Source_Sans_Pro']">
                                                Tip
                                                Advisor</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!--订单内容-->
                            <div
                                class="order-content mt-[12px] rounded-[10px] md:mt-[20px] md:p-[30px] md:rounded-[20px]">
                                <!-- 1. 订单头部信息（公共） -->
                                <div
                                    class="order-content-header p-[10px] bg-white rounded-tl-[10px] rounded-tr-[10px] md:rounded-[10px] md:border-[1px] md:border-[#1C215814] md:border-solid">
                                    <!--订单头部信息 人员信息-->
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center">
                                            <img :src="orderDetail.augur?.avatar"
                                                class="w-[50px] h-[50px] rounded-[4px] mr-[10px] md:w-[100px] md:h-[100px] md:rounded-[6px] md:mr-[15px]" />
                                            <div>
                                                <div
                                                    class="text-[16px] leading-[18px] text-[#1C2158] font-['Philosopher'] font-bold md:text-[26px] md:leading-[38px]">
                                                    {{ orderDetail.augur?.username }}</div>
                                                <div
                                                    class="text-[12px] font-['Source_Sans_3'] font-[400] text-[#1C2158] opacity-[0.5] mt-[7px] md:text-[20px] md:leading-[25px] md:mt-[13px]">
                                                    {{ orderType }}</div>
                                            </div>
                                        </div>
                                        <!--联系按钮-->
                                        <button
                                            class="connect-btn px-[7px] py-[5px] text-white text-[16px] leading-[18px] font-['Philosopher'] font-bold rounded-[6px] md:px-[22px] md:py-[11px] md:text-[20px] md:leading-[22px]"
                                            @click="handleConnect">Connect</button>
                                    </div>
                                    <!--订单头部信息 订单信息-->
                                    <div class="order-info-container pt-[6px]">
                                        <div class="flex flex-col md:flex-row">
                                            <div
                                                class="order-info-item flex-1 flex items-center justify-between mt-[15px] md:mt-[12px] md:justify-start">
                                                <div
                                                    class="order-info-item-title text-[14px] leading-[18px] text-[#1C2158] font-['Source_Sans_3'] font-[400] opacity-[0.5] md:text-[16px] md:leading-[24px]">
                                                    Order Status:</div>
                                                <div class="order-info-item-value font-['Open_Sans_3'] font-[600] text-[14px] leading-[18px] md:ml-[5px] md:text-[16px] md:leading-[20px]"
                                                    :class="statusMap[orderDetail.status].class"
                                                    v-if="orderDetail.status">
                                                    {{ statusMap[orderDetail.status].text }}</div>
                                            </div>
                                            <div
                                                class="order-info-item flex-1 flex items-center justify-between mt-[15px] md:mt-[12px] md:justify-start">
                                                <div
                                                    class="order-info-item-title text-[14px] leading-[18px] text-[#1C2158] font-['Source_Sans_3'] font-[400] opacity-[0.5] md:text-[16px] md:leading-[24px]">
                                                    Actula payment:</div>
                                                <div class="order-info-item-value flex items-center md:ml-[5px]">
                                                    <span
                                                        class="text-[#F53A3A] font-['Open_Sans_3'] font-[600] text-[18px] leading-[25px] md:text-[24px] md:leading-[33px]">{{
                                                            orderDetail.totalCoins }}</span>
                                                    <img src="@/assets/common/money.png"
                                                        class="w-[14px] h-[14px] ml-[4px] md:w-[24px] md:h-[24px]" />
                                                </div>
                                            </div>
                                            <div
                                                class="order-info-item flex-1 flex items-center justify-between mt-[15px] md:mt-[12px] md:justify-start">
                                                <div
                                                    class="flex-1 order-info-item-title text-[14px] leading-[18px] text-[#1C2158] font-['Source_Sans_3'] font-[400] opacity-[0.5] md:text-[16px] md:leading-[24px]">
                                                    Offers:</div>
                                                <div class="order-info-item-value flex items-center justify-end flex-wrap w-fit md:ml-[5px] md:justify-start">
                                                    <span
                                                        class="text-[#F53A3A] font-['Open_Sans_3'] font-[600] text-[18px] leading-[25px] md:text-[24px] md:leading-[33px]">{{
                                                            orderDetail.discountCoins }}</span>
                                                    <img src="@/assets/common/money.png"
                                                        class="w-[14px] h-[14px] ml-[4px] md:w-[24px] md:h-[24px]" />
                                                    <span class="card flex items-center shrink-0"
                                                        v-if="orderDetail.m10CalllCardNumber">
                                                        <span class="line w-[1px] h-[14px] bg-[#E8E8E8] mx-[10px]"></span>
                                                        <span class="flex items-center">
                                                            <span class="text-[#FF7C44] text-[14px] leading-[18px]">{{ orderDetail.m10CalllCardNumber }}x</span>
                                                            <img src="@/assets/order/10MCalllCard.png" class="w-[38px] h-[20px] ml-[4px]">
                                                        </span>
                                                    </span>
                                                    <span class="card flex items-center shrink-0"
                                                        v-if="orderDetail.callCardNumber">
                                                        <span class="line w-[1px] h-[14px] bg-[#E8E8E8] mx-[10px]"></span>
                                                        <span class="flex items-center">
                                                            <span class="text-[#FF7C44] text-[14px] leading-[18px]">{{ orderDetail.callCardNumber }}x</span>
                                                            <img src="@/assets/order/callCard.png" class="w-[38px] h-[20px] ml-[4px]">
                                                        </span>
                                                    </span>
                                                    <span class="card flex items-center shrink-0"
                                                        v-if="orderDetail.experienceCardNumber">
                                                        <span class="line w-[1px] h-[14px] bg-[#E8E8E8] mx-[10px]"></span>
                                                        <span class="flex items-center">
                                                            <span class="text-[#FF7C44] text-[14px] leading-[18px]">{{ orderDetail.experienceCardNumber }}x</span>
                                                            <img src="@/assets/order/experienceCard.png" class="w-[38px] h-[20px] ml-[4px]">
                                                        </span>
                                                    </span>
                                                    <span class="card flex items-center shrink-0"
                                                        v-if="orderDetail.firstOrderCardNumber">
                                                        <span class="line w-[1px] h-[14px] bg-[#E8E8E8] mx-[10px]"></span>
                                                        <span class="flex items-center">
                                                            <!-- <img src="@/assets/order/firstOrderCard.png" class="w-[38px] h-[20px] ml-[4px]"> -->
                                                            <span class="text-[#FF4444] text-[14px] leading-[18px] ml-[14px]">(30% Off)</span>
                                                        </span>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                        <!--订单时间-->
                                        <div class="flex flex-col md:flex-row md:mt-[20px]">
                                            <div
                                                class="order-info-item flex-1 flex items-center justify-between mt-[15px] md:mt-[12px] md:justify-start">
                                                <div
                                                    class="order-info-item-title text-[14px] leading-[18px] text-[#1C2158] font-['Source_Sans_3'] font-[400] opacity-[0.5] md:text-[16px] md:leading-[24px]">
                                                    Order Time:</div>
                                                <div
                                                    class="order-info-item-value font-['Source_Sans_3'] text-[14px] leading-[18px] md:ml-[5px] md:text-[16px] md:leading-[24px]">
                                                    {{ formatTimestampToString(orderDetail.createTimestamp) }}</div>
                                            </div>
                                            <div
                                                class="order-info-item flex-1 flex items-center justify-between mt-[15px] md:mt-[12px] md:justify-start">
                                                <div
                                                    class="order-info-item-title text-[14px] leading-[18px] text-[#1C2158] font-['Source_Sans_3'] font-[400] opacity-[0.5] md:text-[16px] md:leading-[24px]">
                                                    Due Time:</div>
                                                <div
                                                    class="order-info-item-value font-['Source_Sans_3'] text-[14px] leading-[18px] md:ml-[5px] md:text-[16px] md:leading-[24px]">
                                                    {{ getdueTime() }}</div>
                                            </div>
                                            <div
                                                class="order-info-item flex-1 flex items-center justify-between mt-[15px] md:mt-[12px] md:justify-start">
                                                <div
                                                    class="order-info-item-title text-[14px] leading-[18px] text-[#1C2158] font-['Source_Sans_3'] font-[400] opacity-[0.5] md:text-[16px] md:leading-[24px]">
                                                    Order Id:</div>
                                                <div
                                                    class="order-info-item-value flex items-center font-['Source_Sans_3'] text-[14px] leading-[18px] md:ml-[5px] md:text-[16px] md:leading-[24px]">
                                                    <div>{{ orderDetail.orderNo }}</div>
                                                    <div
                                                        class="line w-[1px] h-[14px] bg-[#1C2158] opacity-[0.08] mx-[8px]">
                                                    </div>
                                                    <div class="text-[#4484FF] font-['Source_Sans_3'] font-[400] text-[14px] leading-[18px] md:text-[16px] md:leading-[24px] cursor-pointer"
                                                        @click="copyId(orderDetail.id)">Copy Id</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- 2. 超时订单（公共） -->
                                <div class="reply-container mt-[20px] md:mt-[30px] md:px-[30px] md:pt-[33px] md:pb-[50px] md:border-[1px] md:border-[#1C215814] md:border-solid md:rounded-[20px]"
                                    v-if="orderDetail.status == 4">
                                    <div
                                        class="reply-title text-[18px] leading-[20px] text-[#1C2158] font-['Philosopher'] font-bold md:text-[28px] md:leading-[31px]">
                                        Expired</div>
                                    <div
                                        class="order-expired-message p-[10px] rounded-[6px] mt-[10px] md:mt-[20px] md:p-[20px] flex items-center justify-between">
                                        <div
                                            class="text-[14px] leading-[16px] text-[#1C2158] font-['Source_Sans_Pro'] font-[400] md:text-[16px] md:leading-[20px]">
                                            The advisor did not answer your question within the specified time, the
                                            order has
                                            expired</div>
                                    </div>
                                </div>
                                <!-- 3. 订单被拒绝（公共） -->
                                <div class="reply-container mt-[20px] md:mt-[30px] md:px-[30px] md:pt-[33px] md:pb-[50px] md:border-[1px] md:border-[#1C215814] md:border-solid md:rounded-[20px]"
                                    v-else-if="orderDetail.status == 3">
                                    <div
                                        class="reply-title text-[18px] leading-[20px] text-[#1C2158] font-['Philosopher'] font-bold md:text-[28px] md:leading-[31px]">
                                        Reasons for decline</div>
                                    <div
                                        class="order-expired-message p-[10px] rounded-[6px] mt-[10px] md:mt-[20px] md:p-[20px] flex items-center justify-between">
                                        <div
                                            class="text-[14px] leading-[16px] text-[#1C2158] font-['Source_Sans_Pro'] font-[400] md:text-[16px] md:leading-[20px]">
                                            <p>1.{{ orderDetail.refuseReason }}</p>

                                        </div>
                                    </div>
                                </div>
                                <!-- 4. text reading 专属内容 -->
                                <div v-else-if="orderDetail.augurFortuneGroupId == 1">
                                    <!--Reply 订单的回复信息 只有完成订单有 textReading有-->
                                    <div
                                        class="reply-container mt-[20px] md:mt-[30px] md:px-[30px] md:pt-[33px] md:pb-[50px] md:border-[1px] md:border-[#1C215814] md:border-solid md:rounded-[20px]">
                                        <div v-if="orderDetail.augurAsk">
                                            <div
                                                class="reply-title text-[18px] leading-[20px] text-[#1C2158] font-['Philosopher'] font-bold md:text-[28px] md:leading-[31px]">
                                                Reply</div>
                                            <div
                                                class="reply-content p-[12px] rounded-[6px] mt-[12px] md:mt-[23px] md:p-[20px]">
                                                <div class="reply-content-header flex items-center">
                                                    <img :src="orderDetail.augur.avatar"
                                                        class="w-[30px] h-[30px] rounded-[50%] mr-[10px] md:w-[36px] md:h-[36px] md:mr-[15px]" />
                                                    <div
                                                        class="text-[14px] leading-[18px] text-[#1C2158] font-['Open_Sans'] font-[600] md:text-[18px] md:leading-[20px] md:font-['Philosopher'] md:font-bold">
                                                        {{ orderDetail.augur.username }}</div>
                                                </div>
                                                <div
                                                    class="reply-content-advisor text-[#1C2158] mt-2 pb-2 border-b-[1px] border-[#1C215814] border-solid text-[14px] leading-[18px] font-['Source_Sans_3'] font-[400] md:mt-[10px] md:pb-[16px] md:text-[16px] md:leading-[20px]">
                                                    {{ orderDetail.augurAsk }}</div>
                                                <div
                                                    class="reply-content-user mt-2 text-[#1C2158] text-[14px] leading-[18px] font-['Source_Sans_3'] font-[400] md:mt-[10px] md:pb-[16px] md:text-[16px] md:leading-[20px]">
                                                    <span class="text-[#4484FF] hidden md:inline-block">Reply:</span> My
                                                    name is
                                                    Ben and
                                                    I'm from New York, USA. It's been a long time since I can remember,
                                                    but
                                                    it's
                                                    been
                                                    getting worse lately.
                                                </div>
                                            </div>
                                        </div>
                                        <!---正在等待接单的情况-->
                                        <div class="wait-order-container flex flex-col items-center justify-center mt-[20px] md:mt-[28px]"
                                            v-if="orderDetail.status == 1">
                                            <div
                                                class="wait-order-content relative flex items-center justify-center w-[200px] h-[160px] md:w-[300px] md:h-[238px]">
                                                <img src="@/assets/order/wait-order.png"
                                                    class="absolute top-0 left-0 w-full h-full" />
                                                <div
                                                    class="wait-order-text text-[#1C2158] relative text-[18px] leading-[25px] font-['Open_Sans_3'] font-[600] mt-[12px] md:text-[28px] md:leading-[38px] md:mt-[20px]">
                                                    {{ hours + ':' + minus + ':' + seconds }}</div>
                                            </div>
                                            <div
                                                class="mt-3 text-[12px] leading-[18px] text-[#1C2158] font-['Source_Sans_3'] font-[400] opacity-[0.5] md:mt-[28px] md:text-[14px] md:leading-[18px]">
                                                The consultant will get back to you within {{ hours + ':' + minus }} pm
                                            </div>
                                        </div>
                                        <!--订单完成-->
                                        <div v-if="orderDetail.replyContent">
                                            <div
                                                class="finish-order-container relative mt-[10px] p-[12px] text-[14px] text-[#1C2158] font-['Source_Sans_3'] font-[400] leading-[16px] md:text-[16px] md:leading-[20px]">
                                                {{ orderDetail.replyContent }}
                                                <img src="@/assets/order/order-replay-img.png"
                                                    class="w-[140px] h-[105px] absolute bottom-0 right-0 md:w-[200px] md:h-[150px] md:right-3" />
                                            </div>
                                            <!--add-order-reply-->
                                            <!-- <div class="add-order-replay p-[10px] rounded-[6px] mt-[10px] md:mt-[20px] md:p-[20px] flex items-center justify-between"
                                                v-if="orderDetail.replyContent">
                                                <div
                                                    class="text-[14px] leading-[16px] text-[#1C2158] font-['Source_Sans_Pro'] font-[400] md:text-[16px] md:leading-[20px]">
                                                    Append a message if you're still puzzled about this reading. Please
                                                    kindly place
                                                    another order if you have other questions.</div>
                                                <div
                                                    class="add-order-replay-btn shrink-0 flex items-center ml-3 md:ml-[44px]">
                                                    <span
                                                        class="text-[15px] leading-[20px] text-[#4484FF] font-['Open_Sans'] font-bold md:text-[24px] md:leading-[27px] md:font-['Philosopher'] md:font-bold">Add</span>
                                                    <img src="@/assets/order/add-replay.png"
                                                        class="w-[18px] h-[18px] ml-[2px] md:w-[30px] md:h-[30px] md:ml-[10px]" />
                                                </div>
                                            </div> -->
                                            <!-- <div class="mt-[20px] md:mt-[24px]" v-else>
                                                <div
                                                    class="reply-title text-[18px] leading-[20px] text-[#1C2158] font-['Philosopher'] font-bold md:text-[28px] md:leading-[31px]">
                                                    Append Message</div>

                                                <div
                                                    class="p-3 bg-white rounded-[6px] mt-[10px] text-[14px] leading-[18px] text-[#1C2158] font-['Source_Sans_3'] font-[400] md:mt-[19px] md:p-[20px] md:text-[16px] md:leading-[20px] md:bg-[#F8F8FA]">
                                                    <div
                                                        class="reply-content-user text-[#1C2158] text-[14px] leading-[18px] font-['Source_Sans_3'] font-[400] mb-2 md:mb-3 md:text-[16px] md:leading-[20px]">
                                                        My name is Ben and I'm from New York, USA. It's been a long time
                                                        since I can
                                                        remember, but it's been getting worse lately.</div>
                                                    <div
                                                        class="reply-content-header flex items-center border-t-[1px] border-[#1C215814] border-solid pt-[12px] md:pt-[20px]">
                                                        <img src="@/assets/family.png"
                                                            class="w-[30px] h-[30px] rounded-[50%] mr-[10px] md:w-[36px] md:h-[36px] md:mr-[15px]" />
                                                        <div
                                                            class="text-[14px] leading-[18px] text-[#1C2158] font-['Open_Sans'] font-[600] md:text-[18px] md:leading-[20px] md:font-['Philosopher'] md:font-bold">
                                                            Psychic Mel</div>
                                                    </div>
                                                    <div
                                                        class="reply-content-advisor text-[#1C2158] mt-2 pb-2 text-[14px] leading-[18px] font-['Source_Sans_3'] font-[400] md:mt-[10px] md:pb-[16px] md:text-[16px] md:leading-[20px]">
                                                        Can you tell me how old you are, where you come from, and when
                                                        did
                                                        this
                                                        phenomenon of sleepwalking start?</div>
                                                </div>
                                            </div> -->
                                        </div>
                                    </div>
                                    <!--订单的详细信息只有textReading才展示-->
                                    <div
                                        class="order-detail-container mt-[20px] md:mt-[30px] md:px-[30px] md:pt-[33px] pointer-events-none md:pb-[36px] md:border-[1px] md:border-[#1C215814] md:border-solid md:rounded-[20px]">
                                        <!--基本信息-->
                                        <div class="order-detail-container-item mt-[20px]">
                                            <div
                                                class="order-detail-container-item-title text-[18px] leading-[20px] text-[#1C2158] font-['Philosopher'] font-bold md:text-[28px] md:leading-[31px]">
                                                Basic Information</div>
                                            <div
                                                class="order-detail-container-item-content flex flex-col md:flex-row p-[12px] bg-white rounded-[6px] mt-[10px] md:mt-[15px] md:p-0">
                                                <div
                                                    class="order-detail-container-item-content-item flex-1 flex items-center justify-between md:justify-start">
                                                    <div
                                                        class="order-detail-container-item-content-item-title text-[14px] leading-[18px] text-[#1C2158] font-['Source_Sans_3'] font-[400] md:text-[16px] md:leading-[20px] md:opacity-[.5]">
                                                        Name:</div>
                                                    <div
                                                        class="order-detail-container-item-content-item-value ml-[8px] text-[14px] leading-[18px] text-[#1C2158] font-['Source_Sans_3'] font-[400] opacity-[.5] md:text-[16px] md:leading-[20px] md:opacity-[1]">
                                                        {{ orderDetail.name }}</div>
                                                </div>
                                                <div
                                                    class="order-detail-container-item-content-item flex-1 flex items-center justify-between mt-[19px] md:justify-start">
                                                    <div
                                                        class="order-detail-container-item-content-item-title text-[14px] leading-[18px] text-[#1C2158] font-['Source_Sans_3'] font-[400] md:text-[16px] md:leading-[20px] md:opacity-[.5]">
                                                        Date of Birth:</div>
                                                    <div
                                                        class="order-detail-container-item-content-item-value ml-[8px] text-[14px] leading-[18px] text-[#1C2158] font-['Source_Sans_3'] font-[400] opacity-[.5] md:text-[16px] md:leading-[20px] md:opacity-[1]">
                                                        {{ formatTimestampToString2(orderDetail.birth).time }}</div>
                                                </div>
                                                <div
                                                    class="order-detail-container-item-content-item flex-1 flex items-center justify-between mt-[19px] md:justify-start">
                                                    <div
                                                        class="order-detail-container-item-content-item-title text-[14px] leading-[18px] text-[#1C2158] font-['Source_Sans_3'] font-[400] md:text-[16px] md:leading-[20px] md:opacity-[.5]">
                                                        Gender:</div>
                                                    <div
                                                        class="order-detail-container-item-content-item-value ml-[8px] flex items-center">
                                                        <img src="@/assets/common/gril-select.png"
                                                            class="w-[14px] h-[14px] mr-[2px]"
                                                            v-if="orderDetail.gender == 2" />
                                                        <img src="@/assets/common/boy-select.png"
                                                            class="w-[14px] h-[14px] mr-[2px]"
                                                            v-else-if="orderDetail.gender == 1" />
                                                        <img src="@/assets/common/no-select.png"
                                                            class="w-[14px] h-[14px] mr-[2px]"
                                                            v-else-if="orderDetail.gender == 3" />
                                                        <div
                                                            class="text-[14px] leading-[18px] text-[#1C2158] font-['Source_Sans_3'] font-[400] opacity-[.5] md:text-[16px] md:leading-[20px] md:opacity-[1]">
                                                            {{ returnGender(orderDetail.gender) }}</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <!--一般情况-->
                                        <div class="order-detail-container-item mt-[20px] md:mt-[35px]">
                                            <div
                                                class="order-detail-container-item-title text-[18px] leading-[20px] text-[#1C2158] font-['Philosopher'] font-bold md:text-[28px] md:leading-[31px]">
                                                General Situation</div>
                                            <div
                                                class="order-detail-container-item-content px-[12px] py-[10px] bg-white rounded-[6px] mt-[10px] text-[14px] leading-[18px] text-[#1C2158] font-['Source_Sans_3'] font-[400] md:mt-[23px] md:px-[20px] md:py-[0px] md:text-[16px] md:leading-[20px]">
                                                <div
                                                    class="order-detail-container-item-content-item opacity-[0.5] md:opacity-[1]">
                                                    {{ orderDetail.generalSituation }}
                                                </div>
                                            </div>
                                        </div>
                                        <!--具体问题-->
                                        <div class="order-detail-container-item mt-[20px] md:mt-[43px]">
                                            <div
                                                class="order-detail-container-item-title text-[18px] leading-[20px] text-[#1C2158] font-['Philosopher'] font-bold md:text-[28px] md:leading-[31px]">
                                                Specific Question</div>
                                            <div
                                                class="order-detail-container-item-content px-[12px] py-[10px] bg-white rounded-[6px] mt-[10px] text-[14px] leading-[18px] text-[#1C2158] font-['Source_Sans_3'] font-[400] md:mt-[23px] md:px-[20px] md:py-[0px] md:text-[16px] md:leading-[20px]">
                                                <div
                                                    class="order-detail-container-item-content-item opacity-[0.5] md:opacity-[1]">
                                                    {{ orderDetail.specificQuestion }}
                                                </div>
                                            </div>
                                        </div>
                                        <!--附件图片-->
                                        <div class="order-detail-container-item mt-[20px] md:mt-[43px]"
                                            v-if="orderDetail.images && orderDetail.images.length">
                                            <div
                                                class="order-detail-container-item-title text-[18px] leading-[20px] text-[#1C2158] font-['Philosopher'] font-bold md:text-[28px] md:leading-[31px]">
                                                Attach Picture</div>
                                            <div class="order-attach-img flex gap-[10px] mt-[10px]">
                                                <img v-for="item in orderDetail.images" :src="item" :key="item"
                                                    class="w-[86px] h-[98px] rounded-[6px] object-cover md:w-[200px] md:h-[200px] md:rounded-[12px]" />
                                            </div>
                                        </div>
                                        <!--留言-->
                                        <!---有留言的情况-->
                                        <!-- <div class="order-detail-container-item mt-[20px] md:mt-[43px]" v-if="hasReply">
                                            <div class="add-order-replay-btn shrink-0 flex items-center">
                                                <span
                                                    class="text-[18px] leading-[20px] text-[#4484FF] font-['Philosopher'] font-bold md:text-[28px] md:leading-[30px]">Add</span>
                                                <img src="@/assets/order/add-replay.png"
                                                    class="w-[18px] h-[18px] ml-[2px] md:w-[30px] md:h-[30px] md:ml-[10px]" />
                                            </div>
                                            <div
                                                class="text-[14px] leading-[18px] text-[#1C2158] font-['Source_Sans_3'] font-[400] mt-2 md:mt-[10px] md:text-[16px] md:leading-[20px]">
                                                I've been having nightmares lately, and todayl dreamt that there was a
                                                family who
                                                cut off thein own heads before they died, and this has been the case
                                                since
                                                ancient
                                                times, and the scene in the dream seemed to be in a cellar</div>
                                        </div>
                                        <div class="flex items-center justify-between p-[13px] rounded-[6px] mt-[20px] text-[16px] leading-[18px] text-[#1C2158] font-['Philosopher'] font-bold border-[1px] border-[#4484FF] border-dashed md:max-w-[340px]"
                                            v-else>
                                            <div class="">Lack of important information?</div>
                                            <div
                                                class="add-order-replay-btn shrink-0 flex items-center ml-3 md:ml-[44px]">
                                                <span
                                                    class="text-[15px] leading-[20px] text-[#4484FF] font-bold md:text-[24px] md:leading-[27px] md:font-bold">Add</span>
                                                <img src="@/assets/order/add-replay.png"
                                                    class="w-[18px] h-[18px] ml-[2px] md:w-[30px] md:h-[30px] md:ml-[10px]" />
                                            </div>
                                        </div> -->
                                    </div>
                                </div>
                                <!-- 5. live chat text 专属内容 -->
                                <div v-else-if="orderDetail.augurFortuneGroupId == 4">
                                    <div class="live-text-chat-complete relative pt-[20px] px-[10px] pb-[10px] bg-white rounded-bl-[10px] rounded-br-[10px] md:pt-[40px]"
                                        v-if="orderDetail.status == 6">
                                        <div class="live-chat-message relative w-full h-[344px] overflow-hidden rounded-[12px] border-[1px] border-solid border-transparent transition-all duration-300"
                                            :class="{ 'h-[500px]  md:h-[800px] overflow-y-auto': showLess }"
                                            style="padding-bottom:72px;">
                                            <div
                                                class="live-chat-message-box w-full h-full bg-transparent  pt-[18px] px-[10px] pb-[14px]">
                                                <div
                                                    class="live-chat-message-box-time text-[14px] leading-[18px] text-[#1C2158] font-['Source_Sans_3'] font-[400] opacity-[.5] md:text-[16px] text-center md:leading-[20px] md:opacity-[1]">
                                                    {{ formatTimestampToString(orderDetail.createTimestamp) }}
                                                </div>
                                                <div v-for="msg in orderMessages" :key="msg.ID" class="message-wrapper"
                                                    :class="[msg.uid === userInfo.uid ? 'is-me' : 'is-other']">
                                                    <img class="avatar" :src="msg.avatar" alt="avatar" />
                                                    <div class="content">
                                                        <div class="text-bubble" v-if="msg.msgType == 'TIMTextElem'">
                                                            {{ msg.msgContent }}
                                                        </div>
                                                        <img :src="msg.msgContent" alt=""
                                                            class="w-[100px] h-[100px] rounded-[12px] object-cover"
                                                            v-else-if="msg.msgType == 'TIMImageElem'">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div
                                            class="live-chat-message-footer absolute w-full h-[72px] left-0 bottom-[11px] flex justify-center items-center px-[15px]">
                                            <div
                                                class="live-chat-message-footer-box rounded-[12px] w-full h-full flex items-center justify-center">
                                                <div class="show-more w-max flex items-center justify-center gap-[8px] px-[51px] py-[10px] border-[1px] border-dashed border-[#4484FF] rounded-[6px]"
                                                    @click="showLess = !showLess">
                                                    <span
                                                        class="text-[16px] leading-[18px] text-[#4484FF] font-['Philosopher'] font-bold">
                                                        {{ !showLess ? 'show More' : 'show Less' }} </span>
                                                    <img src="@/assets/order/show-more.png"
                                                        class="w-[24px] h-[24px] transition-all duration-300"
                                                        :class="{ 'rotate-180': showLess }">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <!--完成订单留言-->
                                    <!-- <div class="add-order-replay p-[10px] rounded-[6px] mt-[10px] md:mt-[20px] md:p-[20px] flex items-center justify-between"
                                        v-if="orderDetail.status == 6">
                                        <div
                                            class="text-[14px] leading-[16px] text-[#1C2158] font-['Source_Sans_Pro'] font-[400] md:text-[16px] md:leading-[20px]">
                                            Append a message if you're still puzzled about this reading. Please
                                            kindly place
                                            another order if you have other questions.</div>
                                        <div class="add-order-replay-btn shrink-0 flex items-center ml-3 md:ml-[44px]">
                                            <span
                                                class="text-[16px] leading-[20px] text-[#4484FF] font-['Open_Sans'] font-bold md:text-[24px] md:leading-[27px] md:font-['Philosopher'] md:font-bold">Add</span>
                                            <img src="@/assets/order/add-replay.png"
                                                class="w-[18px] h-[18px] ml-[2px] md:w-[30px] md:h-[30px] md:ml-[10px]" />
                                        </div>
                                    </div> -->
                                </div>
                            </div>
                        </div>
                    </div>
                </template>
            </el-skeleton>
        </div>
        <AppendMessageDialog v-model:dialogAppendMessageVisible="dialogAppendMessageVisible" />
    </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'
import OrderLeft from '@/components/orderLeft.vue'
import { ArrowRight } from '@element-plus/icons-vue'
import AppendMessageDialog from './components/appendMessageDialog.vue'
import { getOrderDetail, orderMessageList } from '@/api/order'
import { useUserStore } from '@/stores/user'
import { copyText, formatTimestampToString, formatTimestampToString2 } from '@/utils'
import { useRoute, useRouter } from 'vue-router'
const route = useRoute()
const router = useRouter()
const orderNo = ref(route.params.orderNo)

const useStore = useUserStore()
const userInfo = computed(() => useStore.userInfo)
const orderMessages = ref<any[]>([])

const orderType = ref('')
const dialogAppendMessageVisible = ref(false) // 追加留言弹窗
const orderDetail = ref<any>({})
const hasAppendMessage = ref(true)
const current = ref(1)

const hasReply = ref(false)
const loading = ref(true)
const finished = ref(false)

const showLess = ref(false)

const hours = ref('00')
const minus = ref('00')
const seconds = ref('00')
const currentTimer = ref<number | undefined>()
const statusMap: { [key: number]: { text: string; class: string } } = {
    6: { text: 'Completed', class: 'text-[#4BDD74]' },
    1: { text: 'Pending', class: 'text-[#FC791A]' },
    2: { text: 'Cancel', class: 'text-[#8D8C92]' },
    4: { text: 'Expired', class: 'text-[#F4B000]' },
    5: { text: 'Awaiting Receipt', class: 'text-[#4073F8]' },
    3: { text: 'Declined', class: 'text-[#EF42BF]' },
}
const genderOptions = [
    {
        label: 'Male',
        value: '1',
        selectIcon: new URL('@/assets/common/boy-select.png', import.meta.url).href,
        noSelectIcon: new URL('@/assets/common/boy-unselect.png', import.meta.url).href,
    },
    {
        label: 'Female',
        value: '2',
        selectIcon: new URL('@/assets/common/gril-select.png', import.meta.url).href,
        noSelectIcon: new URL('@/assets/common/gril-unselect.png', import.meta.url).href,
    },
    {
        label: 'Non-binary',
        value: '3',
        selectIcon: new URL('@/assets/common/no-select.png', import.meta.url).href,
        noSelectIcon: new URL('@/assets/common/no-unselect.png', import.meta.url).href,
    },
]
const copyId = async (text: string) => {
    copyText(text)
}

const getOrderDetailFunc = async () => {
    loading.value = true
    try {
        const res = await getOrderDetail(orderNo.value as string)
        orderDetail.value = res.data
        orderType.value = res.data.augur.augurFortune.filter((item: any) => item.augurFortuneGroupId == res.data.augurFortuneGroupId)[0].name
        if (orderDetail.value.augurFortuneGroupId == 4 && orderDetail.value.status == 6) {
            getOrderMessageList()
        } else if (orderDetail.value.augurFortuneGroupId == 1 && orderDetail.value.status == 1) {
            //倒计时
            console.log(11111)
            getTextReadingCutDown()
        }
    } catch (error) {
        console.error(error)
    } finally {
        loading.value = false
    }
}
const returnOrderCreateTime = (timeStamp: number) => {
    const time = new Date(timeStamp * 1000).toUTCString()
    return time.split('GMT')[0]
}
const getdueTime = () => {
    const dueSeconds = orderDetail.value.expeditingType == 2 ? 60 * 60 * 1 : 60 * 60 * 24;
    return formatTimestampToString(orderDetail.value.createTimestamp + dueSeconds);
}
const getOrderMessageList = async () => {
    if (finished.value) return
    try {
        const res = await orderMessageList({ current: current.value, orderNo: orderNo.value as string })
        orderMessages.value = [...orderMessages.value, ...res.data]
        current.value++
    } catch (error) {

    }

}
const getTextReadingCutDown = () => {
    const dueSeconds = orderDetail.value.expeditingType == 2 ? 60 * 60 * 1 : 60 * 60 * 24;
    const dueTime = orderDetail.value.createTimestamp + dueSeconds
    const currentTime = Math.floor(new Date().getTime() / 1000)
    let duration = dueTime - currentTime
    currentTimer.value = window.setInterval(() => {
        hours.value = String(Math.floor(duration / 3600) < 10 ? '0' + Math.floor(duration / 3600) : Math.floor(duration / 3600))
        minus.value = String(Math.floor(duration % 3600 / 60) < 10 ? '0' + Math.floor(duration % 3600 / 60) : Math.floor(duration % 3600 / 60))
        seconds.value = String(duration % 60 < 10 ? '0' + (duration % 60) : duration % 60)
        duration--
        if (duration < 0) {
            if (currentTimer.value !== undefined) {
                clearInterval(currentTimer.value)
                currentTimer.value = undefined
            }
        }
    }, 1000)
}
const returnGender = (gender: number) => {
    let str = ''
    genderOptions.map(item => {
        if (item.value == String(gender)) {
            str = item.label
        }
    })
    return str
}

const handleConnect = () => {
    router.push({
        path: '/chatToAdvisor',
        query: {
            uid: orderDetail.value.augurUid,
            username: orderDetail.value.augur.username
        }
    })
}
onMounted(() => {
    getOrderDetailFunc()
})

</script>
<style lang="scss" scoped>
.app-container {
    .connect-btn {
        background: linear-gradient(91.29deg, #7aafff 1.1%, #7a87ff 95.36%);
    }

    .reply-content {
        background: rgba(219, 231, 253, 0.5);
    }

    .finish-order-container {
        background: linear-gradient(180deg, #efefff 0%, #fff8f4 100%);
        position: relative;

        &::before {
            content: '';
            position: absolute;
            inset: 0;
            width: 100%;
            height: 100%;
            padding: 1px;
            border-radius: 6px;
            border: 1px solid;
            background: linear-gradient(91.29deg, rgba(122, 175, 255, 1) 1.1%, rgba(122, 135, 255, 1) 95.36%);
            -webkit-mask: linear-gradient(#7aafff 0 0) content-box, linear-gradient(#7aafff 0 0);
            -webkit-mask-composite: xor;
            mask-composite: exclude;
            pointer-events: none;
        }
    }

    .add-order-replay,
    .order-expired-message {
        background: linear-gradient(91.29deg, rgba(122, 175, 255, 0.1) 1.1%, rgba(122, 135, 255, 0.1) 95.36%);
    }

    .order-expired-message {
        position: relative;

        &::before {
            content: '';
            inset: 0;
            position: absolute;
            width: 100%;
            height: 100%;
            padding: 1px;
            border-radius: 6px;
            border: 1px solid;
            background: linear-gradient(91.29deg, rgba(122, 175, 255, .1) 1.1%, rgba(122, 135, 255, .1) 95.36%);
            // -webkit-mask: linear-gradient(#7aafff 0 0) content-box, linear-gradient(#7aafff 0 0);
            -webkit-mask-composite: xor;
            mask-composite: exclude;
            pointer-events: none;
        }
    }

    .live-chat-message {
        position: relative;
        width: 100%;
        // height: 344px;
        border-radius: 12px;
        // padding: 18px 10px 14px 10px;
        background:
            linear-gradient(180deg, #EFEFFF 0%, #FFF8F4 100%) padding-box,
            linear-gradient(91.29deg, #7AAFFF 1.1%, #7A87FF 95.36%) border-box;
        border: 1px solid transparent;
        box-sizing: border-box;


    }

    .live-chat-message-footer-box {
        background: linear-gradient(180deg, rgba(250, 245, 249, 0) 0%, #FFF8F4 19.91%);
    }

    // 聊天消息样式
    .message-wrapper {
        display: flex;
        margin-bottom: 20px;

        .avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
        }

        .content {
            display: flex;
            flex-direction: column;
            max-width: 70%;
        }

        .text-bubble {
            padding: 10px 15px;
            border-radius: 18px;
            font-size: 16px;
            line-height: 1.5;
            word-break: break-word;
        }

        &.is-other {
            flex-direction: row-reverse;

            .avatar {
                margin-left: 10px;
            }

            .content {
                align-items: flex-end;
            }

            .text-bubble {
                background-color: #BED5FF;
                color: #1C2158;
                border-top-right-radius: 4px;
            }
        }

        &.is-me {
            flex-direction: row;

            .avatar {
                margin-right: 10px;
            }

            .content {
                align-items: flex-start;
            }

            .text-bubble {
                background-color: #fff;
                color: #1C2158;
                border-top-left-radius: 4px;
            }
        }
    }

    .live-text-chat-complete {
        position: relative;

        .live-chat-message {
            padding-bottom: 72px;
        }

        // .live-chat-message-footer {
        //     position: absolute;
        //     left: 0;
        //     bottom: 0;
        //     width: 100%;
        //     height: 72px;
        // }
    }
}
</style>
