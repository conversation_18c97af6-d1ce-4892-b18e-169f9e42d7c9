<!-- Offline Place Order -->
<template>
    <div class="app-container">
        <div class="main-content px-[15px] lg:px-[120px]">
            <el-breadcrumb
                :separator-icon="ArrowRight"
                class="breadcrumb-style"
            >
                <el-breadcrumb-item class="text-[#1C2158]">Home</el-breadcrumb-item>
                <el-breadcrumb-item class="text-[#1C2158]">Family & Friends</el-breadcrumb-item>
                <el-breadcrumb-item class="text-[#1C2158]">{{ augurDetail.username }}</el-breadcrumb-item>
                <el-breadcrumb-item>Offline Order</el-breadcrumb-item>
            </el-breadcrumb>

            <div
                class="main-content-detail flex flex-col lg:flex-row-reverse md:bg-white lg:bg-white md:px-[40px] md:pb-[70px] md:pt-[40px] lg:px-[40px] lg:pb-[70px] lg:pt-[40px] rounded-[30px]">
                <!--卜卦师信息-->
                <div class="p-[10px] bg-white rounded-xl lg:flex-1 lg:pt-[56px]">
                    <!--名称和图片-->
                    <div class="flex">
                        <div class="shrink-0 relative">
                            <img :src="augurDetail.avatar" alt=""
                                class="w-[80px] h-[80px] rounded-[13px] object-cover lg:w-[160px] lg:h-[160px]" />
                            <div class="online absolute top-[10px] right-[10px] w-[14px] h-[14px] rounded-[14px] bg-[#3AD953] border-2 border-white border-solid"
                                v-if="augurDetail.state == 4"></div>
                            <div class="offline absolute top-[10px] right-[10px] w-[14px] h-[14px] rounded-[14px] bg-[#BFC3C5] border-2 border-white border-solid"
                                v-else-if="augurDetail.state == 1 || augurDetail.state == 2"></div>
                            <div class="busy absolute top-[10px] right-[10px] w-[14px] h-[14px] rounded-[14px] bg-[#EA3232] border-2 border-white border-solid"
                                v-else-if="augurDetail.state == 3"></div>
                        </div>
                        <div class="ml-[10px]">
                            <p class="text-[18px] font-['Philosopher'] font-blod text-[#1C2158] lg:text-[34px]">{{
                                augurDetail.username }}</p>
                            <p
                                class="font-['Open_Sans_3'] text-[12px] mt-[6px] text-[#1C2158] lg:text-[18px] lg:mt-[12px]">
                                My expertise in {{ returnItemTags(augurDetail.tags) }}</p>
                            <SmRate v-model:rateValue="augurDetail.grade" :show-small="true" :show-color="true"
                                :isDisabled="true" class="mt-[5px] lg:mt-[12px]"></SmRate>
                        </div>
                    </div>
                    <!--具体的介绍-->
                    <div class="mt-[25px] relative lg:mt-[54px]">
                        <img src="@/assets/common/2.png" alt=""
                            class="absolute top-[-14px] left-0 w-[27px] h-[23px] lg:w-[47px] lg:h-[41px] lg:top-[-24px]" />
                        <div
                            class="chat-title font-['Philosopher'] font-bold text-[#1C2158] text-[16px] lg:text-[24px]">
                            Advisor's Instruction:</div>
                        <div
                            class="chat-content font-['Open_Sans_3'] text-[14px] text-[#1C2158] mt-[7px] opacity-60 lg:text-[20px] lg:mt-[10px]">
                            Hi.While placing call please mention your Date of birth and Mother's name.</div>
                        <img src="@/assets/common/1.png" alt=""
                            class="absolute right-0 bottom-[-14px] w-[27px] h-[23px] lg:w-[47px] lg:h-[41px] lg:bottom-[-24px]" />
                    </div>
                    <!--权益-->
                    <div
                        class="equity mt-[25px] py-[10px] flex justify-between items-center rounded-[2px] lg:flex-col lg:py-[30px] lg:rounded-[6px]">
                        <div class="flex flex-1 items-center px-[10px] lg:w-full lg:px-[30px] lg:items-start">
                            <img src="@/assets/common/private.png" alt=""
                                class="w-[24px] h-[24px] mr-[10px] lg:w-[50px] lg:h-[50px] lg:mr-[15px]" />
                            <div class="lg:border-b lg:border-[#D1DDFF] border-solid lg:pb-[30px]">
                                <span
                                    class="font-['Philosopher'] font-bold text-[#1C2158] text-[16px] lg:text-[22px]">Private</span>
                                <span class="hidden text-[16px] text-normal-opacity-70 mt-2 lg:block">Your date is never
                                    disclosed.Only you are in control.</span>
                            </div>
                        </div>
                        <div class="line w-[1px] h-[16px] bg-[#D3D8E9] lg:hidden"></div>
                        <div class="flex flex-1 items-center px-[10px] lg:w-full lg:mt-[30px] lg:px-[30px]">
                            <img src="@/assets/common/secure.png" alt=""
                                class="w-[24px] h-[24px] mr-[10px] lg:w-[50px] lg:h-[50px] lg:mr-[15px]" />
                            <div>
                                <span
                                    class="font-['Philosopher'] font-bold text-[#1C2158] text-[16px] lg:text-[22px]">Secure</span>
                                <span class="hidden text-[16px] text-normal-opacity-70 mt-2 lg:block">Your date is never
                                    disclosed.Only you are in control.</span>
                            </div>
                        </div>
                    </div>
                </div>
                <!---表单--->
                <div class="mt-[20px] lg:mt-0 lg:flex-1 lg:mr-[70px]">
                    <div
                        class="form-title font-['Philosopher'] font-bold text-[#1C2158] mb-[12px] text-[18px] lg:text-[22px]">
                        Order Request</div>
                    <div class="form-content">
                        <el-form :model="createOrderForm" :rules="createOrderRules" ref="createOrderRef"
                            label-width="100px" label-position="top" :hide-required-asterisk="true">
                            <el-form-item prop="specificQuestion">
                                <template #label>
                                    <span data-content="(*Required )">Specific Question</span>
                                </template>
                                <div class="flex flex-wrap gap-2 md:bg-white pb-[8px]">
                                    <div :class="{ 'question-active': createOrderForm.specificQuestion == item }"
                                        class="question-item translation duration-500 py-[5px] px-[8px] text-[12px] text-normal-opacity-70 font-['Source_Sans_3'] leading-[1] border border-[#60648A] border-solid rounded-[4px] cursor-pointer"
                                        v-for="item in specificQuestionList" :key="item"
                                        @click="changeSpecificQuestion(item)">{{ item }}</div>
                                </div>
                                <el-input v-model="createOrderForm.specificQuestion" maxlength="200"
                                    placeholder="Describing your situation in certain sentences will help the advisor know your status better to futher imporve the accuracy and the service quality."
                                    show-word-limit type="textarea" :rows="5" class="rounded-[6px] text-[12px]" />
                            </el-form-item>
                            <div class="formatter-form">
                                <el-form-item prop="imageUrlList">
                                    <template #label>
                                        <span>Attach Picture <span class="text-[12px] ml-1">(0/2)</span></span>
                                    </template>
                                    <div class="upload-attach-image flex items-center w-full lg:bg-white  gap-[10px]">
                                        <div class="upload-attach-image-box flex items-center gap-[10px]">
                                            <div class="w-[86px] h-[98px] relative"
                                                v-for="(item, index) in createOrderForm.imageUrlList"
                                                :key="'image_' + index">
                                                <img alt="" class="w-full h-full object-cover rounded-[6px]"
                                                    :src="item" />
                                                <img src="@/assets/common/img-delete.png" alt=""
                                                    class="absolute w-[16px] h-[16px] top-[4px] right-[4px] cursor-pointer"
                                                    @click="deleteImg(index)">
                                            </div>
                                        </div>
                                        <div class="relative flex justify-center items-center w-[86px] h-[98px] rounded-[6px] bg-white object-cover lg:bg-[#F8F8FA] md:bg-[#F8F8FA]"
                                            v-loading-mask="uploadLoading"
                                            v-show="createOrderForm.imageUrlList.length < 2">
                                            <img src="@/assets/order/upload-img.png" alt="" class="w-[30px] h-[30px]">
                                            <input type="file"
                                                class="absolute top-0 left-0 w-full h-full opacity-0 cursor-pointer"
                                                :disabled="uploadLoading" @change="handleFileChange($event)">
                                        </div>
                                    </div>
                                </el-form-item>
                            </div>
                            <div class="formatter-form">
                                <el-form-item prop="generalSituation">
                                    <template #label>
                                        <span data-content="(*Required )">General Situation</span>
                                    </template>
                                    <el-input v-model="createOrderForm.generalSituation" maxlength="3000"
                                        :autosize="false"
                                        placeholder="Describing your situation in certain sentences will help the advisor know your status better to futher imporve the accuracy and the service quality."
                                        show-word-limit type="textarea" :rows="6" class="rounded-[6px] text-[12px]" />
                                </el-form-item>
                            </div>
                            <div
                                class="form-title font-['Philosopher'] font-bold text-[#1C2158] mt-[32px] mb-[12px] text-[18px] lg:text-[22px]">
                                Basic Information</div>
                            <el-form-item prop="name">
                                <template #label>
                                    <span data-content="(*Required )">Name</span>
                                </template>
                                <el-input :placeholder="'Provide your  name'" v-model="createOrderForm.name"
                                    :clearable="true"></el-input>
                            </el-form-item>
                            <el-form-item :label="'Birth Date'" prop="birthDate">
                                <template #label>
                                    <span data-content="(*Required )">Birth Date</span>
                                </template>
                                <div class="relative w-full" ref="birthDateRef">
                                    <el-input :placeholder="'Select your birth date'"
                                        v-model="createOrderForm.birthDate" readonly @click="toggleCalendar">
                                        <template #suffix>
                                            <img src="@/assets/common/calendar.png" alt="" class="w-[24px] h-[24px]" />
                                        </template>
                                    </el-input>
                                    <Teleport to="body">
                                        <Transition enter-active-class="transition duration-200 ease-out"
                                            enter-from-class="opacity-0 transform scale-95 -translate-y-2"
                                            enter-to-class="opacity-100 transform scale-100 translate-y-0"
                                            leave-active-class="transition duration-150 ease-in"
                                            leave-from-class="opacity-100 transform scale-100 translate-y-0"
                                            leave-to-class="opacity-0 transform scale-95 -translate-y-2">
                                            <div v-if="showCalendar" :style="calendarStyle"
                                                class="fixed z-[4000] shadow-lg origin-top calendar-dropdown">
                                                <CustomCalendar v-model="selectedDate" @confirm="handleDateConfirm"
                                                    @cancel="showCalendar = false" />
                                            </div>
                                        </Transition>
                                    </Teleport>
                                </div>
                            </el-form-item>
                            <el-form-item :label="'Birth Time'" prop="birthTime">
                                <template #label>
                                    <span data-content="(*Required )">Birth Time</span>
                                </template>
                                <div class="relative w-full" ref="birthTimeRef">
                                    <el-input :placeholder="'Fill your birth time'" v-model="createOrderForm.birthTime"
                                        readonly @click="toggleTimePicker"> </el-input>
                                    <Teleport to="body">
                                        <Transition enter-active-class="transition duration-200 ease-out"
                                            enter-from-class="opacity-0 transform scale-95 -translate-y-2"
                                            enter-to-class="opacity-100 transform scale-100 translate-y-0"
                                            leave-active-class="transition duration-150 ease-in"
                                            leave-from-class="opacity-100 transform scale-100 translate-y-0"
                                            leave-to-class="opacity-0 transform scale-95 -translate-y-2">
                                            <div v-if="showTimePicker" :style="timePickerStyle"
                                                class="fixed z-[4000] shadow-lg origin-top calendar-dropdown">
                                                <CustomTimePicker v-model="selectedTime" @confirm="handleTimeConfirm"
                                                    @cancel="showTimePicker = false" />
                                            </div>
                                        </Transition>
                                    </Teleport>
                                </div>
                            </el-form-item>
                            <el-form-item :label="'Birthplace'" prop="birthPlace" class="white-bg">
                                <template #label>
                                    <span data-content="(*Required )">Birthplace</span>
                                </template>
                                <SmSearchRemote v-model="createOrderForm.birthPlace" :remote-api="searchBirthPlace"
                                    @changePlace="handleChangePlace" />
                            </el-form-item>
                            <el-form-item :label="'Gender'" prop="gender">
                                <template #label>
                                    <span data-content="(*Required )">Gender</span>
                                </template>
                                <div class="bg-white rounded-[6px] w-full flex items-center lg:bg-[#F8F8FA]">
                                    <div class="gender-item relative flex-1 flex flex-col justify-center items-center pt-[10px] pb-[12px] cursor-pointer lg:pt-[13px] lg:pb-[5px]"
                                        v-for="item in genderOptions" :key="item.value"
                                        :class="{ 'active-gender lg:bg-white': createOrderForm.gender == item.value }"
                                        @click="changeGender(item.value)">
                                        <img :src="createOrderForm.gender == item.value ? item.selectIcon : item.noSelectIcon"
                                            alt="" class="w-[24px] h-[24px]" />
                                        <span
                                            class="text-[12px] font-['Source_Sans_Pro'] mt-[6px] opacity-50 leading-[12px] lg:text-[14px] lg:leading-[14px] lg:mt-[10px]"
                                            :class="{ 'text-[#4484FF]': createOrderForm.gender == item.label }">{{
                                                item.label }}</span>
                                    </div>
                                </div>
                            </el-form-item>
                            <div class="form-button  flex gap-[11px] mt-[36px]  lg:mt-[43px] lg:gap-[30px]">
                                <div class="flex-1">
                                    <button
                                        class="confirm-button relative w-full py-[5px] h-[50px] rounded-[6px]  lg:pt-[16px] lg:h-[80px] lg:pb-[9px]"
                                        type="button" @click="createOrderFunc(createOrderRef,1)">
                                        <div class="confirm-button-top flex justify-center items-center">
                                            <span
                                                class="text-[#FFEB39] text-[20px] font-['Philosopher'] lg:text-[28px]">{{
                                                    augurFortune.discountCoins
                                                <augurFortune.coins?augurFortune.discountCoins:augurFortune.coins
                                                    }}</span>
                                                    <img src="@/assets/common/money.png" alt=""
                                                        class="ml-1 w-[18px] h-[18px] lg:w-[24px] lg:h-[24px]" />
                                        </div>
                                        <span
                                            class="text-white text-[12px] font-['Source_Sans_Pro'] line-through mt-1 opacity-50 lg:text-[20px]" v-if="augurFortune.discountCoins<augurFortune.coins">{{augurFortune.coins}}</span>
                                        <img src="@/assets/common/discount.png" alt=""
                                            class="absolute left-[-5.5px] top-[-6px] w-[75px] h-[26px]">
                                    </button>
                                    <p
                                        class="text-center text-[12px] font-['Source_Sans_Pro'] text-normal-opacity-50 mt-[6px] leading-[15px]">
                                        Delivered within 24h
                                    </p>
                                </div>
                                <div class="flex-1">
                                    <button
                                        class="hours-button relative w-full py-[5px] rounded-[6px] h-[50px] lg:pt-[16px] lg:pb-[9px] lg:h-[80px]"
                                        type="button" @click="createOrderFunc(createOrderRef,2)">
                                        <div class="confirm-button-top flex justify-center items-center">
                                            <span
                                                class="text-[#8E5936] font-[20px] font-['Philosopher'] lg:text-[28px]">{{ augurFortune.expeditingCoins}}</span>
                                            <img src="@/assets/common/money.png" alt=""
                                                class="ml-1 w-[18px] h-[18px] lg:w-[24px] lg:h-[24px]" />
                                        </div>
                                        <span
                                            class="text-[#8E5936] text-[12px] font-['Source_Sans_Pro'] line-through mt-1 opacity-50 lg:text-[20px]" v-if="augurFortune.expeditingCoins<augurFortune.coins">{{ augurFortune.coins}}</span>
                                        <img src="@/assets/common/discount.png" alt=""
                                            class="absolute left-[-5.5px] top-[-6px] w-[75px] h-[26px]">
                                    </button>
                                    <p
                                        class="text-center text-[12px] font-['Source_Sans_Pro'] text-[#8E5936] opacity-50 mt-[6px] leading-[15px]">
                                        1-Hour Delivery
                                    </p>
                                </div>
                            </div>
                        </el-form>
                    </div>
                </div>
            </div>
        </div>
        <OfflineOrderSuccessDialog :dialog-offline-order-success-visible="dialogOfflineOrderSuccessVisible"
            @update:dialog-offline-order-success-visible="handleOfflineOrderSuccessVisible" @reorder="reOrderFunc" :augurDetail="augurDetail" :augurFortuneGroupId="createOrderForm.augurFortuneGroupId" :orderNo="orderNo"></OfflineOrderSuccessDialog>
    </div>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, nextTick, computed, onMounted } from 'vue'
import { ArrowRight } from '@element-plus/icons-vue'

import { useRoute } from 'vue-router'
import type { FormInstance } from 'element-plus'
import { createAugurOrder, getAugurOrderCommonQuestion } from '@/api/order'
import { uploadFile } from '@/api/common'
import { getAugurDetail } from '@/api/augur'
import SmRate from '@/components/rate/index.vue'
import CustomCalendar from '@/components/CustomCalendar.vue'
import CustomTimePicker from '@/components/CustomTimePicker.vue'
import OfflineOrderSuccessDialog from './components/offlineOrderSuccessDialog.vue'
import SmSearchRemote from '@/components/smSearchRemote/smSearchRemote.vue'
import { searchBirthPlace } from '@/api/common'

// 定义接口类型
interface CreateOrderForm {
  augurFortuneGroupId: string
  augurFortuneId: string
  augurUid: string
  name: string
  lastName: string
  imageUrlList: string[]
  imageUrl: string
  gender: string
  birth: string
  birthDate: string
  birthTime: string
  birthPlace: string
  generalSituation: string
  specificQuestion: string
}

interface ChangePlaceData {
    latitude: string
    longitude: string
}

// 响应式数据
const route = useRoute()
const specificQuestionList = ref<string[]>([])
const augurFortune = ref<any>({})
const createOrderRef = ref<FormInstance>()
const createOrderLoading = ref(false)
const orderNo = ref('')
const uploadLoading = ref(false)
// 离线订单成功弹窗相关逻辑
const dialogOfflineOrderSuccessVisible = ref(false)
// 表单验证规则
const createOrderRules = reactive({
    specificQuestion: [{ required: true, message: 'Please select a specific question', trigger: 'blur' }],
    name: [{ required: true, message: 'Please enter your name', trigger: 'blur' }],
    birthDate: [{ required: true, message: 'Please select your birth date', trigger: 'change' }],
    birthTime: [{ required: true, message: 'Please select your birth time', trigger: 'change' }],
    gender: [{ required: true, message: 'Please select your gender', trigger: 'change' }],
    birthPlace: [{ required: true, message: 'Please select your birthplace', trigger: 'change' }],
    generalSituation: [{ required: true, message: 'Please enter your general situation', trigger: 'change' }],
})
const genderOptions = [
    {
        label: 'Male',
        value: '1',
        selectIcon: new URL('@/assets/common/boy-select.png', import.meta.url).href,
        noSelectIcon: new URL('@/assets/common/boy-unselect.png', import.meta.url).href,
    },
    {
        label: 'Female',
        value: '2',
        selectIcon: new URL('@/assets/common/gril-select.png', import.meta.url).href,
        noSelectIcon: new URL('@/assets/common/gril-unselect.png', import.meta.url).href,
    },
    {
        label: 'Non-binary',
        value: '3',
        selectIcon: new URL('@/assets/common/no-select.png', import.meta.url).href,
        noSelectIcon: new URL('@/assets/common/no-unselect.png', import.meta.url).href,
    },
]
// 表单数据
const createOrderForm = reactive<CreateOrderForm & { latitude?: string; longitude?: string }>({
    augurFortuneGroupId: '',
    augurFortuneId: '',
    augurUid: '',
    name: '',
    lastName: '',
    imageUrlList: [] as string[],
    imageUrl: '',
    gender: '1',
    birth: '0',
    birthDate: '',
    birthTime: '',
    birthPlace: '',
    generalSituation: '',
    specificQuestion: '',
    latitude: '',
    longitude: '',
})
const showCalendar = ref(false)
const selectedDate = ref<Date>()
const birthDateRef = ref<HTMLElement | null>(null)
const calendarPosition = ref({ top: 0, left: 0 })


const augurDetail = ref<any>({
    grade: 4.8,
    avatar: '',
    username: '',
    about: '',
})
const showTimePicker = ref(false)
const selectedTime = ref<{ hour: number; minute: number } | undefined>(undefined)
const timePickerStyle = ref({})
const birthTimeRef = ref()
const changeGender = (gender: string) => {
    createOrderForm.gender = gender
}
const calendarStyle = computed(() => {
    return {
        top: `${calendarPosition.value.top}px`,
        left: `${calendarPosition.value.left}px`,
    }
})
// 更新日历位置
const updateCalendarPosition = () => {
    if (!birthDateRef.value) return
    const rect = birthDateRef.value.getBoundingClientRect()
    const isLargeScreen = document.documentElement.clientWidth >= 1330
    const topOffset = isLargeScreen ? 448 : 412

    calendarPosition.value = {
        top: rect.top - topOffset + window.scrollY,
        left: rect.left + window.scrollX,
    }
}
const toggleCalendar = () => {
    if (!showCalendar.value) {
        nextTick(() => {
            updateCalendarPosition()
        })
    }
    showCalendar.value = !showCalendar.value
}
const handleDateConfirm = (date: Date) => {
    if (date) {
        createOrderForm.birthDate = date.toLocaleDateString()
    }
    showCalendar.value = false
}
const handleScroll = () => {
    if (showCalendar.value) {
        updateCalendarPosition()
    }
}
const closeCalendarOnClickOutside = (e: MouseEvent) => {
    const target = e.target as HTMLElement
    if (showCalendar.value && birthDateRef.value && !birthDateRef.value.contains(target) && !target.closest('.calendar-dropdown')) {
        showCalendar.value = false
    }
}
watch(showCalendar, (val) => {
    if (val) {
        setTimeout(() => {
            document.addEventListener('click', closeCalendarOnClickOutside)
            window.addEventListener('scroll', handleScroll, true)
            window.addEventListener('resize', updateCalendarPosition)
        }, 0)
    } else {
        document.removeEventListener('click', closeCalendarOnClickOutside)
        window.removeEventListener('scroll', handleScroll, true)
        window.removeEventListener('resize', updateCalendarPosition)
    }
})
function updateTimePickerPosition() {
    if (!birthTimeRef.value) return
    const rect = birthTimeRef.value.getBoundingClientRect()
    timePickerStyle.value = {
        left: rect.left + 'px',
        top: rect.bottom + 8 + 'px',
        position: 'fixed',
    }
}
watch(showTimePicker, (val) => {
    if (val) {
        setTimeout(() => {
            window.addEventListener('scroll', updateTimePickerPosition, true)
            window.addEventListener('resize', updateTimePickerPosition)
        }, 0)
        nextTick(() => {
            updateTimePickerPosition()
        })
    } else {
        window.removeEventListener('scroll', updateTimePickerPosition, true)
        window.removeEventListener('resize', updateTimePickerPosition)
    }
})
function toggleTimePicker() {
    showTimePicker.value = !showTimePicker.value
    nextTick(() => {
        if (showTimePicker.value) {
            updateTimePickerPosition()
        }
    })
}
function handleTimeConfirm(val: { hour: number; minute: number }) {
    createOrderForm.birthTime = `${val.hour.toString().padStart(2, '0')}:${val.minute.toString().padStart(2, '0')}`
    showTimePicker.value = false
}
// 获取常见问题列表
const getAugurOrderCommonQuestionFunc = async () => {
    try {
        const response = await getAugurOrderCommonQuestion()
        specificQuestionList.value = response.data
    } catch (error) {
        console.error('Failed to fetch common questions:', error)
    }
}

// 获取占卜师详情
const getAugurDetailData = async () => {
    try {
        const res = await getAugurDetail({ augurUid: createOrderForm.augurUid })
        augurDetail.value = res.data
        augurFortune.value = res.data.augurFortune.find((item: any) => item.id == route.query.augurFortuneId)
    } catch (error) {
        console.error('Failed to fetch augur details:', error)
    }
}
// 工具函数
const returnItemTags = (tags: any[]) => {
    if (!tags || !Array.isArray(tags)) return ''
    return tags.map((item) => item.name).join('/')
}

const changeSpecificQuestion = (item: string) => {
    createOrderForm.specificQuestion = item
}

const deleteImg = (index: number) => {
    if (index >= 0 && index < createOrderForm.imageUrlList.length) {
        createOrderForm.imageUrlList.splice(index, 1)
    }
}

const handleChangePlace = (data: ChangePlaceData) => {
    createOrderForm.latitude = data.latitude
    createOrderForm.longitude = data.longitude
}

// 创建订单函数
const createOrderFunc = async (formRef: FormInstance | undefined, type: number) => {
    if (!formRef) return

    try {
        const isValid = await formRef.validate()
        if (isValid) {
            createOrderLoading.value = true
            createOrderForm.birth = String(new Date(createOrderForm.birthDate + ' ' + createOrderForm.birthTime).getTime() / 1000)
            createOrderForm.imageUrl = createOrderForm.imageUrlList.toString()

            const res = await createAugurOrder({
                augurFortuneGroupId: createOrderForm.augurFortuneGroupId,
                augurFortuneId: createOrderForm.augurFortuneId,
                augurUid: createOrderForm.augurUid,
                name: createOrderForm.name,
                gender: createOrderForm.gender,
                birth: createOrderForm.birth,
                generalSituation: createOrderForm.generalSituation,
                specificQuestion: createOrderForm.specificQuestion,
                birthPlace: createOrderForm.birthPlace,
                latitude: createOrderForm.latitude,
                longitude: createOrderForm.longitude,
                imageUrl:createOrderForm.imageUrl,
                expeditingType: type,
            })

            console.log('Order created successfully:', res)
            orderNo.value = res.data.orderNo
            // 这里可以添加成功后的处理逻辑
            dialogOfflineOrderSuccessVisible.value = true
        }
    } catch (error) {
        console.error('Failed to create order:', error)
    } finally {
        createOrderLoading.value = false
    }
}

// 文件上传处理
const handleFileChange = async (event: Event) => {
    event.stopPropagation()
    const input = event.target as HTMLInputElement

    if (!input.files || !input.files[0]) return

    uploadLoading.value = true
    try {
        const response: any = await uploadFile(input.files[0])
        if (response?.data?.[0]) {
            createOrderForm.imageUrlList.push(response.data[0])
        }
    } catch (error) {
        console.error('Failed to upload file:', error)
    } finally {
        uploadLoading.value = false
        // 清空input值，允许重复选择同一文件
        input.value = ''
    }
}
// 初始化数据
const initializeData = async () => {
    createOrderForm.augurFortuneGroupId = route.query.augurFortuneGroupId as string
    createOrderForm.augurFortuneId = route.query.augurFortuneId as string
    createOrderForm.augurUid = route.query.uid as string

    // 并行获取数据
    await Promise.all([
        getAugurDetailData(),
        getAugurOrderCommonQuestionFunc()
    ])
}

// 组件生命周期
onMounted(() => {
    initializeData()
})


const handleOfflineOrderSuccessVisible = (val: boolean) => {
    dialogOfflineOrderSuccessVisible.value = val
}

const reOrderFunc = ()=>{
    handleOfflineOrderSuccessVisible(false)
    if (!createOrderRef.value) return
    createOrderRef.value.resetFields()

}
</script>
<style lang="scss" scoped>
// 通用样式类
.breadcrumb-style {
    font-family: 'Open Sans', sans-serif;
    font-weight: 600;
    font-size: 14px;
    padding-top: 8px;
    margin-bottom: 14px;

    @media (min-width: 768px) {
        padding-top: 24px;
        margin-bottom: 30px;
    }
}

.equity {
    background: linear-gradient(91.29deg, rgba(122, 175, 255, 0.1) 1.1%, rgba(122, 135, 255, 0.1) 95.36%);
}

:deep(.el-form) {
    .el-form-item {
        .el-form-item__label {
            font-size: 14px;
            font-weight: 700;
            font-family: 'Philosopher';

            span {
                &::after {
                    content: attr(data-content);
                    font-size: 12px;
                    color: #ff5353;
                    margin-left: 4px;
                    font-weight: 400;
                }
            }
        }

        &.white-bg {
            .el-form-item__content {
                background-color: #fff;
            }
        }

        .el-form-item__content {

            .el-input__wrapper,
            .el-select__wrapper {
                height: 44px;
                font-size: 15px;
                border-radius: 6px;
                font-weight: 400;
                color: #1c2158;
                font-family: 'Open Sans';
            }

            .el-input__inner {
                color: #1c2158;
            }
        }
    }

    @media screen and (min-width: 1330px) {
        .el-form-item {
            margin-bottom: 24px;

            .el-form-item__label {
                font-size: 22px;
                font-weight: 700;
                font-family: 'Philosopher';
                // margin-bottom: 24px;
            }

            &.white-bg {
                .el-form-item__content {
                    background-color: #f8f8fa;
                }
            }

            .el-form-item__content {
                background-color: #f8f8fa;

                .el-textarea__inner {
                    font-size: 16px;
                    border-radius: 12px;
                    background-color: #f8f8fa;
                    color: #1c2158;
                    font-family: 'Open Sans';
                }

                .el-input__wrapper,
                .el-select__wrapper {
                    height: 50px;
                    font-size: 20px;
                    border-radius: 12px;
                    background-color: transparent;
                    color: #1c2158;
                    font-family: 'Open Sans';
                }
            }
        }
    }
}

.active-gender {
    &::before {
        content: '';
        position: absolute;
        inset: 0;
        width: 100%;
        height: 100%;
        padding: 1px;
        border-radius: 6px;
        background: linear-gradient(104.04deg, #96baff 0%, #7064ff 86.39%);
        -webkit-mask: linear-gradient(#96baff 0 0) content-box, linear-gradient(#96baff 0 0);
        -webkit-mask-composite: xor;
        mask: linear-gradient(#96baff 0 0) content-box, linear-gradient(#96baff 0 0);
        mask-composite: exclude;
        pointer-events: none;
    }
}

.confirm-button {
    background: linear-gradient(91.29deg, #7aafff 1.1%, #7a87ff 95.36%);

    &:hover {
        box-shadow: 0px 0px 12px 0px rgba(0, 0, 0, 0.25);
    }
}

.hours-button {
    background: linear-gradient(272.2deg, #F7CFA3 3.76%, #F9D9B6 43.9%, #FCEBDA 59.26%, #FEE1C2 80.2%);

    &:hover {
        box-shadow: 0px 0px 12px 0px rgba(0, 0, 0, 0.25);
    }
}
</style>
