<!-- app -->
<template>
	<div class="app-container">
		<div class="main-content px-[0] md:px-[120px]">
			<el-breadcrumb :separator-icon="ArrowRight" class="font-['Open_Sans_3'] font-[600] h-[54px] text-[14px] pt-[8px] mb-[14px] md:h-[66px] md:mb-[10px] px-[12px] md:px-[0]">
				<el-breadcrumb-item class="text-[#1C2158]">Home</el-breadcrumb-item>
				<el-breadcrumb-item>Credits Store</el-breadcrumb-item>
			</el-breadcrumb>
			<div class="purchase-container flex px-[15px] gap-[21px] md:px-[120px] ">
				<div class="purchase-container-left flex-1">
					<div class="purchase-container-left-top block font-['Philosopher'] text-[18px] font-[700] text-[#1C2158] leading-[20px] md:hidden">Credits Store</div>
					<div class="purchase-container-left-list mt-[12px] md:mt-[0]">
						<!--Advisor Package-->
						<div class="purchase-container-left-list-item advisor-package flex items-center justify-between border-[2px] border-[#fff] border-solid rounded-[10px] pt-[12px] pr-[15px] pb-[15px] pl-[20px]">
							<div class="purchase-container-left-list-item-left">
								<div class="purchase-container-left-list-item-left-top advisor-package-left-title font-['Philosopher'] text-[20px] font-[700] leading-[24px]">Advisor Package</div>
								<div class="flex items-center mt-2">
									<img src="@/assets/cardPurchase/money.png" alt="" class="w-[16px] h-[16px]" />
									<span class="ml-2 font-['Open_Sans_3'] font-[400] text-[13px] leading-[16px]">Instant 120 Credits</span>
								</div>
								<div class="flex items-center mt-[5px]">
									<img src="@/assets/cardPurchase/free.png" alt="" class="w-[16px] h-[16px]" />
									<span class="ml-2 font-['Open_Sans_3'] font-[400] text-[13px] leading-[16px]">21-Min Free Advisor Session</span>
								</div>
							</div>
							<div class="purchase-container-left-list-item-right buy-button font-['TTChocolates'] px-[18px] py-[9px] text-[18px] font-bold text-[#fff] leading-[20px] rounded-[10px]">$14.99</div>
						</div>
						<!--limted-minutes-->
						<div class="purchase-container-left-list-item relative limted-minutes flex items-center justify-between rounded-[10px] py-[10px] pr-[33px] pl-[12px] bg-white mt-2">
							<div class="flex items-center">
								<img src="@/assets/cardPurchase/6-minutes.png" alt="" class="w-[50px] h-[50px] mr-2" />
								<div class="flex flex-col">
									<div class="font-['Open_Sans_3'] font-[700] text-[19px] leading-[26px] text-[#1C2158]">6-Min Live chat</div>
									<div class="font-['Open_Sans_3'] font-[400] text-[15px] leading-[19px] text-[#1C2158B2] mt-[2px]">Limited-Time Offer</div>
								</div>
							</div>
							<div class="font-['Philosopher'] font-[700] text-[18px] leading-[20px] text-[#4484FF]">$1.99</div>
							<div class="current-down-time absolute top-[0] left-[0] w-[64px] h-[18px] rounded-tl-[10px] rounded-br-[10px] font-['TTChocolates'] text-center text-[13px] leading-[18px] text-[#fff]">23:32:40</div>
						</div>
						<!--discount-package-->
						<div class="purchase-container-left-list-item relative limted-minutes flex items-center justify-between rounded-[10px] py-[10px] pr-[33px] pl-[12px] bg-white mt-2" @click="handleDialogVisible(true)">
							<div class="flex items-center">
								<img src="@/assets/cardPurchase/discount.png" alt="" class="w-[50px] h-[50px] mr-2" />
								<div class="flex flex-col">
									<div class="font-['Open_Sans_3'] font-[700] text-[19px] leading-[26px] text-[#1C2158] flex items-center">
										<span>110</span>
										<span class="text-[13px] font-[600] leading-[26px] ml-[2px]">Credits</span>
									</div>
									<div class="font-['Open_Sans_3'] font-[400] text-[15px] leading-[19px] text-[#1C2158B2] mt-[2px]">Daily Check-In Reward</div>
								</div>
							</div>
							<div class="font-['Philosopher'] font-[700] text-[18px] leading-[20px] text-[#4484FF] flex flex-col">
								<span class="opacity-70 line-through">$9.99</span>
								<span>$8.99</span>
							</div>
							<div class="current-down-time absolute top-[0] left-[0] w-[64px] h-[18px] rounded-tl-[10px] rounded-br-[10px] font-['TTChocolates'] text-center text-[13px] leading-[18px] text-[#fff]">23:32:40</div>
						</div>
						<!--normal-package-->
						<div class="purchase-container-left-list-item normal-package active flex items-center justify-between rounded-[10px] py-[10px] pr-[33px] pl-[12px] bg-white mt-2">
							<div class="flex items-center">
								<img src="@/assets/cardPurchase/level-1.png" alt="" class="w-[42px] h-[42px] mr-[14px]" />
								<div class="flex flex-col">
									<div class="font-['Open_Sans_3'] font-[700] text-[19px] leading-[26px] text-[#1C2158] flex items-center">
										<span>110</span>
										<span class="text-[13px] font-[600] ml-[2px]">Credits</span>
									</div>
								</div>
							</div>
							<div class="font-['Philosopher'] font-[700] text-[18px] leading-[20px] text-[#4484FF] flex flex-col">$4.99</div>
							<img src="@/assets/cardPurchase/check.png" alt="" class="w-[20px] h-[20px] absolute bottom-[0] right-[0]" />
						</div>
					</div>
				</div>
				<div class="purchase-container-right flex-1  hidden md:block">
                    <div class="purchase-container-right-top p-[20px] bg-white rounded-[10px]">
                        <div class="font-['Open_Sans_3'] font-[700] text-[19px] leading-[26px] text-[#1C2158] pb-[16px] border-b-[1px] border-[#EDEDF2] border-solid">
                            Summary
                        </div>
                        <div class="py-[16px] flex flex-col gap-[16px] border-b-[1px] border-[#EDEDF2] border-solid">
                            <div class="flex items-center justify-between opacity-50">
                                <span class="font-['Open_Sans_3'] font-[400] text-[15px] leading-[20px] text-[#1C2158B2]">Balance Starmet</span>
                                <span class="font-['Open_Sans_3'] font-[400] text-[15px] leading-[20px] text-[#1C2158B2]">$4.99</span>
                            </div>
                            <div class="flex items-center justify-between opacity-50">
                                <span class="font-['Open_Sans_3'] font-[400] text-[15px] leading-[20px] text-[#1C2158B2]">Free Bonus Balance</span>
                                <span class="font-['Open_Sans_3'] font-[400] text-[15px] leading-[20px] text-[#1C2158B2]">$4.99</span>
                            </div>
                        </div>
                        <!--Toatl-->
                        <div class="flex justify-between pt-[16px]">
                            <div class="font-['Open_Sans_3'] font-semibold text-[15px] leading-[20px] text-[#1C2158]">
                                Total Balance
                            </div>
                            <div class="font-['Open_Sans_3'] font-semibold text-[15px] leading-[20px] text-[#1C2158]">
                                <div>$4.99</div>
                                <!--折扣-->
                                <div class="text-[#FF861C] font-['Open_Sans_3'] font-[400] text-[15px] leading-[20px] mt-2">
                                    12%Off
                                </div>
                            </div>
                        </div>
                        <!--YOU Pay-->
                        <div class="flex justify-between pt-[16px]">
                            <div class="font-['Open_Sans_3'] font-semibold text-[15px] leading-[20px] text-[#1C2158]">
                                You Pay
                            </div>
                            <div class="font-['Open_Sans_3'] font-semibold text-[15px] leading-[20px] text-[#1C2158]">
                                US$3.99
                            </div>
                        </div>
                        <div class="mt-[15px] pl-[15px] text-[#3DC45B] text-[12px] leading-[16px]">
                            SECURITY GUARANTEED
                        </div>
                    </div>
                    <!----支付方式-->
                    <div class="purcahse-channel-list mt-5">
                        <div class="purcahse-channel-list-item cursor-pointer flex items-center justify-between mt-3 px-[20px] py-[10px] rounded-[10px] bg-white">
                            <div class="purcahse-channel-list-item-left flex items-center">
                                <img src="@/assets/cardPurchase/pay.png" alt="" class="w-[64px] h-[40px] bg-white object-cover" />
                                <div class="purcahse-channel-list-item-left-title ml-[14px] font-['Open_Sans_3'] font-bold text-[15px] leading-[20px] text-[#1C2158]">
                                    <span>LinkAja</span>
                                   <div class="flex items-center mt-1 text-[#FF7C44]">
                                        <span class="text-[14px] font-bold leading-[20px]">+300</span>
                                        <span class="text-[13px]">Free Balance</span>
                                   </div>
                                </div>
                            </div>
                            <div class="purcahse-channel-list-item-right">
                                <img src="@/assets/cardPurchase/arrow-right.png" alt="" class="w-[20px] h-[20px]" />
                            </div>
                        </div>
                    </div>
                </div>
			</div>
		</div>
        <div class="block md:hidden">
            <cardPurchaseDialog :dialog-visible="dialogVisible" @update:dialog-visible="handleDialogVisible" />
        </div>
	</div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { ArrowRight } from '@element-plus/icons-vue'
import cardPurchaseDialog from './components/cardPurchaseDialog.vue'

const dialogVisible = ref(false)
const handleDialogVisible = (val: boolean) => {
	dialogVisible.value = val
}
</script>
<style lang="scss" scoped>
.el-collapse {
	border: none;
}
:deep(.el-collapse-item__header) {
	// align-items: flex-start;
	position: relative;
}
@media (min-width: 1120px) {
	:deep(.el-collapse-item__content) {
		padding-left: 40px;
	}
	:deep(.el-collapse-item__header) {
		//    height: 64px;
		//    font-size: 16px;
		padding: 26px 0;
		height: max-content;
	}
}
@media (max-width: 1120px) {
	:deep(.el-collapse-item__header) {
		border-bottom: none;
		padding: 21px 0;
		height: max-content;
	}
}

.buy-button {
	background: linear-gradient(91.29deg, #7aafff 1.1%, #7a87ff 95.36%);
}
.current-down-time {
	background: linear-gradient(90deg, #ff9d42 0%, #ff5353 100%);
	box-shadow: 0px -1px 3px 0px #ffffff40 inset;
}
.purchase-container-left-list-item {
	box-shadow: 0px 0px 4px 0px #bac5ff4f;
	position: relative;
	cursor: pointer;
	box-sizing: border-box;
	border: 1px solid transparent;
	&:hover {
		border-color: #4484ff;
	}
	&.active {
		border: 1px solid #4484ff;
	}
}
.advisor-package {
	background: linear-gradient(180deg, rgba(242, 147, 255, 0.2) 0%, rgba(185, 160, 255, 0) 100%);
	box-shadow: 0px 0px 4px 0px #bac5ff4f;
    border-width:2px;
	.advisor-package-left-title {
		background: linear-gradient(90.41deg, #ff64c2 1.39%, #7e5dff 99.75%);
		background-clip: text;
		-webkit-background-clip: text;
		-webkit-text-fill-color: transparent;
	}
}
.purcahse-channel-list-item{
    box-shadow: 0px 0px 4px 0px #BAC5FF4F;
    &:hover{
        box-shadow: 0px 0px 4px 0px #4484FF4F;
    }
}
</style>
