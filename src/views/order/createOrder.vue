<!-- 创建订单页面 - 用于离线占卜师下单 -->
<template>
	<div class="app-container">
		<div class="main-content px-[15px] lg:px-[120px]">
			<!-- 面包屑导航 -->
			<el-breadcrumb :separator-icon="ArrowRight" class="font-['Open_Sans_3'] font-[600] text-[14px] pt-[8px] mb-[14px] md:pt-6 lg:pt-6 md:mb-[30px] lg:mb-[30px]">
				<el-breadcrumb-item class="text-[#1C2158]">Home</el-breadcrumb-item>
				<el-breadcrumb-item class="text-[#1C2158]">Family &Friends</el-breadcrumb-item>
				<el-breadcrumb-item class="text-[#1C2158]">{{ augurDetail.username }}</el-breadcrumb-item>
				<el-breadcrumb-item>Live Text Chat</el-breadcrumb-item>
			</el-breadcrumb>

			<!-- 主要内容区域 - 使用flex布局在大屏幕上实现左右结构 -->
			<div class="main-content-detail flex flex-col lg:flex-row-reverse pb-[20px] md:bg-white lg:bg-white md:px-[40px] md:pb-[70px] md:pt-[40px] lg:px-[40px] lg:pb-[70px] lg:pt-[40px] rounded-[30px]">
				<!-- 右侧 - 占卜师信息展示区域 -->
				<div class="p-[10px] bg-white rounded-xl lg:flex-1 lg:pt-[56px]">
					<!-- 占卜师基本信息 - 头像和名称 -->
					<div class="flex">
						<div class="shrink-0 relative">
							<img :src="augurDetail.avatar" alt="" class="w-[80px] h-[80px] rounded-[13px] object-cover lg:w-[160px] lg:h-[160px]" />
							<!-- 在线状态标识 -->
                            <div class="online absolute top-[10px] right-[10px] w-[14px] h-[14px] rounded-[14px] bg-[#3AD953] border-2 border-white border-solid"
                                v-if="augurDetail.state == 4 "></div>
                            <div class="offline absolute top-[10px] right-[10px] w-[14px] h-[14px] rounded-[14px] bg-[#BFC3C5] border-2 border-white border-solid"
                                v-else-if="augurDetail.state == 1 || augurDetail.state == 2"></div>
                            <div class="busy absolute top-[10px] right-[10px] w-[14px] h-[14px] rounded-[14px] bg-[#EA3232] border-2 border-white border-solid"
                                v-else-if="augurDetail.state == 3"></div>
						</div>
						<div class="ml-[10px]">
							<p class="text-[18px] font-['Philosopher'] font-blod text-[#1C2158] lg:text-[34px]">{{ augurDetail.username }}</p>
							<p class="font-['Open_Sans_3'] text-[12px] mt-[6px] text-[#1C2158] lg:text-[18px] lg:mt-[12px]">My expertise in {{ returnItemTags(augurDetail.tags) }}</p>
							<!-- 评分组件 -->
							<SmRate :model-value="augurDetail?.grade.toFixed(1)" :disabled="true" class="mt-[5px] lg:mt-[12px]"></SmRate>
						</div>
					</div>

					<!-- 占卜师介绍信息 -->
					<div class="mt-[25px] relative lg:mt-[54px]">
						<!-- 装饰性引号图标 -->
						<img src="@/assets/common/2.png" alt="" class="absolute top-[-14px] left-0 w-[27px] h-[23px] lg:w-[47px] lg:h-[41px] lg:top-[-24px]" />
						<div class="chat-title font-['Philosopher'] font-bold text-[#1C2158] text-[16px] lg:text-[24px]">Advisor's Instuction:</div>
						<div class="chat-content font-['Open_Sans_3'] text-[14px] text-[#1C2158] mt-[7px] opacity-60 lg:text-[20px] lg:mt-[10px]">Hi.While placing call please mention your Date of birth and Mother's name.</div>
						<img src="@/assets/common/1.png" alt="" class="absolute right-0 bottom-[-14px] w-[27px] h-[23px] lg:w-[47px] lg:h-[41px] lg:bottom-[-24px]" />
					</div>

					<!-- 服务特点展示区域 -->
					<div class="equity mt-[25px] py-[10px] flex justify-between items-center rounded-[2px] lg:flex-col lg:py-[30px] lg:rounded-[6px]">
						<!-- 隐私保护特点 -->
						<div class="flex flex-1 items-center px-[10px] lg:w-full lg:px-[30px] lg:items-start">
							<img src="@/assets/common/private.png" alt="" class="w-[24px] h-[24px] mr-[10px] lg:w-[50px] lg:h-[50px] lg:mr-[15px]" />
							<div class="lg:border-b lg:border-[#D1DDFF] border-solid lg:pb-[30px]">
								<span class="font-['Philosopher'] font-bold text-[#1C2158] text-[16px] lg:text-[22px]">Private</span>
								<span class="hidden text-[16px] text-normal-opacity-70 mt-2 lg:block">Your date is never disclosed.Only you are in control.</span>
							</div>
						</div>
						<!-- 分隔线 - 仅在移动端显示 -->
						<div class="line w-[1px] h-[16px] bg-[#D3D8E9] lg:hidden"></div>
						<!-- 安全保障特点 -->
						<div class="flex flex-1 items-center px-[10px] lg:w-full lg:mt-[30px] lg:px-[30px]">
							<img src="@/assets/common/secure.png" alt="" class="w-[24px] h-[24px] mr-[10px] lg:w-[50px] lg:h-[50px] lg:mr-[15px]" />
							<div>
								<span class="font-['Philosopher'] font-bold text-[#1C2158] text-[16px] lg:text-[22px]">Secure</span>
								<span class="hidden text-[16px] text-normal-opacity-70 mt-2 lg:block">Your date is never disclosed.Only you are in control.</span>
							</div>
						</div>
					</div>
				</div>

				<!-- 左侧 - 订单表单区域 -->
				<div class="mt-[20px] lg:mt-0 lg:flex-1 lg:mr-[70px]">
					<div class="form-title font-['Philosopher'] font-bold text-[#1C2158] mb-[12px] text-[18px] lg:text-[22px]">Order Request</div>
					<div class="form-content">
						<!-- 表单主体 -->
						<el-form :model="createOrderForm" :rules="createOrderRules" ref="createOrderRef" label-width="100px" label-position="top" :hide-required-asterisk="true">
							<!-- 具体问题选择区域 -->
							<el-form-item prop="specificQuestion">
								<template #label>
									<span data-content="(*Required )">Specific Question</span>
								</template>
								<!-- 问题标签选择区 -->
								<div class="flex flex-wrap gap-2 md:bg-white pb-[8px]">
									<div :class="{ 'question-active': createOrderForm.specificQuestion == item }" class="question-item translation duration-500 py-[5px] px-[8px] text-[12px] text-normal-opacity-70 font-['Source_Sans_3'] leading-[1] border border-[#60648A] border-solid rounded-[4px] cursor-pointer" v-for="item in specificQuestionList" :key="item" @click="changeSpecificQuestion(item)">{{ item }}</div>
								</div>
								<!-- 问题详细描述输入框 -->
								<el-input v-model="createOrderForm.specificQuestion" maxlength="200" placeholder="Describing your situation in certain sentences will help the advisor know your status better to futher imporve the accuracy and the service quality." show-word-limit type="textarea" :rows="5" class="rounded-[6px] text-[12px]" />
							</el-form-item>
                            <div class="formatter-form">
                                    <el-form-item prop="generalSituation">
                                        <template #label>
                                            <span data-content="(*Required )">General Situation</span>
                                        </template>
                                        <el-input v-model="createOrderForm.generalSituation" maxlength="3000" :autosize="false"
                                            placeholder="Describing your situation in certain sentences will help the advisor know your status better to futher imporve the accuracy and the service quality."
                                        show-word-limit type="textarea" :rows="6" class="rounded-[6px] text-[12px]" />
                                </el-form-item>
                            </div>
							<!-- 基本信息表单区域 -->
							<div class="form-title font-['Philosopher'] font-bold text-[#1C2158] mt-[32px] mb-[12px] text-[18px] lg:text-[22px]">Basic Information</div>

							<!-- 姓名输入框 -->
							<el-form-item prop="name">
								<template #label>
									<span data-content="(*Required )">Name</span>
								</template>
								<el-input :placeholder="'Provide your  name'" v-model="createOrderForm.name" :clearable="true"></el-input>
							</el-form-item>

							<!-- 出生日期选择器 -->
							<el-form-item :label="'Birth Date'" prop="birthDate">
								<template #label>
									<span data-content="(*Required )">Birth Date</span>
								</template>
								<div class="relative w-full" ref="birthDateRef">
									<el-input :placeholder="'Select your birth date'" v-model="createOrderForm.birthDate" readonly @click="toggleCalendar">
										<template #suffix>
											<img src="@/assets/common/calendar.png" alt="" class="w-[24px] h-[24px]" />
										</template>
									</el-input>
									<!-- 日期选择器弹出层 -->
									<Teleport to="body">
										<Transition enter-active-class="transition duration-200 ease-out" enter-from-class="opacity-0 transform scale-95 -translate-y-2" enter-to-class="opacity-100 transform scale-100 translate-y-0" leave-active-class="transition duration-150 ease-in" leave-from-class="opacity-100 transform scale-100 translate-y-0" leave-to-class="opacity-0 transform scale-95 -translate-y-2">
											<div v-if="showCalendar" :style="calendarStyle" class="fixed z-[4000] shadow-lg origin-top calendar-dropdown">
												<CustomCalendar v-model="selectedDate" @confirm="handleDateConfirm" @cancel="showCalendar = false" />
											</div>
										</Transition>
									</Teleport>
								</div>
							</el-form-item>

							<!-- 出生时间选择器 -->
							<el-form-item :label="'Birth Time'" prop="birthTime">
								<template #label>
									<span data-content="(*Required )">Birth Time</span>
								</template>
								<div class="relative w-full" ref="birthTimeRef">
									<el-input :placeholder="'Fill your birth time'" v-model="createOrderForm.birthTime" readonly @click="toggleTimePicker"> </el-input>
									<!-- 时间选择器弹出层 -->
									<Teleport to="body">
										<Transition enter-active-class="transition duration-200 ease-out" enter-from-class="opacity-0 transform scale-95 -translate-y-2" enter-to-class="opacity-100 transform scale-100 translate-y-0" leave-active-class="transition duration-150 ease-in" leave-from-class="opacity-100 transform scale-100 translate-y-0" leave-to-class="opacity-0 transform scale-95 -translate-y-2">
											<div v-if="showTimePicker" :style="timePickerStyle" class="fixed z-[4000] shadow-lg origin-top calendar-dropdown">
												<CustomTimePicker v-model="selectedTime" @confirm="handleTimeConfirm" @cancel="showTimePicker = false" />
											</div>
										</Transition>
									</Teleport>
								</div>
							</el-form-item>

							<!-- 出生地选择器 -->
							<el-form-item :label="'Birthplace'" prop="birthPlace" class="white-bg">
								<template #label>
									<span data-content="(*Required )">Birthplace</span>
								</template>
								<SmSearchRemote v-model="createOrderForm.birthPlace" :remote-api="searchBirthPlace"
									@changePlace="handleChangePlace" />
							</el-form-item>

							<!-- 性别选择器 -->
							<el-form-item :label="'Gender'" prop="gender">
								<template #label>
									<span data-content="(*Required )">Gender</span>
								</template>
								<div class="bg-white rounded-[6px] w-full flex items-center lg:bg-[#F8F8FA]">
									<div class="gender-item relative flex-1 flex flex-col justify-center items-center pt-[10px] pb-[12px] cursor-pointer lg:pt-[13px] lg:pb-[5px]" v-for="(item, index) in genderOptions" :class="{ 'active-gender lg:bg-white': createOrderForm.gender == item.value }" @click="changeGender(item.value)">
										<img :src="createOrderForm.gender == item.value ? item.selectIcon : item.noSelectIcon" alt="" class="w-[24px] h-[24px]" />
										<span class="text-[12px] font-['Source_Sans_Pro'] mt-[6px] opacity-50 leading-[12px] lg:text-[14px] lg:leading-[14px] lg:mt-[10px]" :class="{ 'text-[#4484FF]': createOrderForm.gender == item.label }">{{ item.label }}</span>
									</div>
								</div>
							</el-form-item>

							<!-- 提交按钮区域 -->
							<div class="form-button px-[20px] mt-[36px] lg:px-[0] lg:mt-[43px]">
								<button class="confirm-button w-full py-[5px] rounded-[6px] h-[50px] lg:h-[80px]" type="button" @click="createOrderFunc(createOrderRef)" v-loading-mask="createOrderLoading" :disabled="createOrderLoading">
									<div class="confirm-button-top flex justify-center items-center">
										<!-- 价格显示 -->
										<span class="text-[#FFEB39] font-[20px] font-['Philosopher'] lg:text-[28px]">
											{{ augurFortune.discountCoins < augurFortune.coins ? augurFortune.coidiscountCoinsns : augurFortune.coins }}
										</span>
										<img src="@/assets/common/money.png" alt="" class="ml-1 w-[18px] h-[18px] lg:w-[24px] lg:h-[24px]" />
										<span class="ml-[5px] text-white font-[18px] font-['Philosopher'] lg:text-[24px]">/min</span>
									</div>
									<!-- 折扣价格显示 -->
									<span class="text-white text-[12px] font-['Source_Sans_Pro'] line-through mt-1 opacity-50 lg:text-[20px]" v-if="augurFortune.discountCoins < augurFortune.coins">{{ augurFortune.coins }}</span>
								</button>
							</div>
						</el-form>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
// 导入所需的Vue核心功能
import { ref, reactive, watch, nextTick, computed, onMounted } from 'vue'
// 导入Element Plus图标
import { ArrowRight } from '@element-plus/icons-vue'
// 导入用户状态管理
import { useUserStore } from '@/stores/user'
// 导入路由功能
import { useRoute,useRouter } from 'vue-router'
import type { FormInstance } from 'element-plus'
// 导入API接口
import { createAugurOrder, getAugurOrderCommonQuestion } from '@/api/order'
import { getAugurDetail } from '@/api/augur'
import { searchBirthPlace } from '@/api/common'
// 导入自定义组件
import SmRate from '@/components/smRate/smRate.vue'
import CustomCalendar from '@/components/CustomCalendar.vue'
import CustomeSelect from '@/components/customeSelect/customeSelect.vue'
import CustomTimePicker from '@/components/CustomTimePicker.vue'
import SmSearchRemote from '@/components/smSearchRemote/smSearchRemote.vue'

// 初始化路由和状态管理
const route = useRoute()
const userStore = useUserStore()
const router = useRouter()
// 定义常用问题列表
const specificQuestionList = ref<string[]>([])
// 计算是否显示免费内容
const showFree = computed(() => userStore.showFree)

// 初始化评分值
const rateValue = ref(4.8)
// 占卜师信息对象
const augurFortune = ref<any>({})

const createOrderRef = ref<FormInstance>()
const createOrderLoading = ref(false)
// 表单验证规则
const createOrderRules = reactive({
	specificQuestion: [{ required: true, message: 'Please select a specific question', trigger: 'blur' }],
	name: [{ required: true, message: 'Please enter your name', trigger: 'blur' }],
	birthDate: [{ required: true, message: 'Please select your birth date', trigger: 'change' }],
	birthTime: [{ required: true, message: 'Please select your birth time', trigger: 'change' }],
	gender: [{ required: true, message: 'Please select your gender', trigger: 'change' }],
	birthPlace: [{ required: true, message: 'Please select your birthplace', trigger: 'change' }],
	generalSituation: [{ required: true, message: 'Please enter your general situation', trigger: 'change' }],
})

// 性别选项配置
const genderOptions = [
	{
		label: 'Male',
		value: '1',
		selectIcon: new URL('@/assets/common/boy-select.png', import.meta.url).href,
		noSelectIcon: new URL('@/assets/common/boy-unselect.png', import.meta.url).href,
	},
	{
		label: 'Female',
		value: '2',
		selectIcon: new URL('@/assets/common/gril-select.png', import.meta.url).href,
		noSelectIcon: new URL('@/assets/common/gril-unselect.png', import.meta.url).href,
	},
	{
		label: 'Non-binary',
		value: '3',
		selectIcon: new URL('@/assets/common/no-select.png', import.meta.url).href,
		noSelectIcon: new URL('@/assets/common/no-unselect.png', import.meta.url).href,
	},
]

// 表单数据对象
const createOrderForm = reactive({
	augurFortuneGroupId: '',
	augurFortuneId: '',
	augurUid: '',
	name: '',
	lastName: '',
	gender: '1',
	birth: '0',
	birthDate: '',
	birthTime: '',
	birthPlace: '',
	generalSituation: '',
	specificQuestion: '',
	latitude: '',
	longitude: '',
})

// 日历相关状态
const showCalendar = ref(false)
const selectedDate = ref<Date>()
const birthDateRef = ref<HTMLElement | null>(null)
const calendarPosition = ref({ top: 0, left: 0 })

// 占卜师详细信息
const augurDetail = ref<any>({
	grade: 4.8,
	avatar: '',
	username: '',
	about: '',
})

// 时间选择器相关状态
const showTimePicker = ref(false)
const selectedTime = ref<{ hour: number; minute: number } | undefined>(undefined)
const timePickerStyle = ref({})
const birthTimeRef = ref()

// 更改性别的处理函数
const changeGender = (gender: string) => {
	createOrderForm.gender = gender
}

// 计算日历样式
const calendarStyle = computed(() => {
	return {
		top: `${calendarPosition.value.top}px`,
		left: `${calendarPosition.value.left}px`,
	}
})

// 更新日历位置
const updateCalendarPosition = () => {
	if (!birthDateRef.value) return

	const rect = birthDateRef.value.getBoundingClientRect()
	const inputHeight = rect.height
	const windowHeight = window.innerHeight

	// 根据屏幕宽度调整日历位置
	if (document.documentElement.clientWidth < 1330) {
		calendarPosition.value = {
			top: rect.top - 412 + window.scrollY,
			left: rect.left + window.scrollX,
		}
	} else {
		calendarPosition.value = {
			top: rect.top - 448 + window.scrollY,
			left: rect.left + window.scrollX,
		}
	}
}

// 切换日历显示状态
const toggleCalendar = () => {
	if (!showCalendar.value) {
		nextTick(() => {
			updateCalendarPosition()
		})
	}
	showCalendar.value = !showCalendar.value
}

// 处理日期确认
const handleDateConfirm = (date: Date) => {
	if (date) {
		createOrderForm.birthDate = date.toLocaleDateString()
	}
	showCalendar.value = false
}

// 处理页面滚动
const handleScroll = () => {
	if (showCalendar.value) {
		updateCalendarPosition()
	}
}

// 点击外部关闭日历
const closeCalendarOnClickOutside = (e: MouseEvent) => {
	const target = e.target as HTMLElement
	if (showCalendar.value && birthDateRef.value && !birthDateRef.value.contains(target) && !target.closest('.calendar-dropdown')) {
		showCalendar.value = false
	}
}

// 监听日历显示状态变化
watch(showCalendar, (val) => {
	if (val) {
		setTimeout(() => {
			document.addEventListener('click', closeCalendarOnClickOutside)
			window.addEventListener('scroll', handleScroll, true)
			window.addEventListener('resize', updateCalendarPosition)
		}, 0)
	} else {
		document.removeEventListener('click', closeCalendarOnClickOutside)
		window.removeEventListener('scroll', handleScroll, true)
		window.removeEventListener('resize', updateCalendarPosition)
	}
})

// 更新时间选择器位置
function updateTimePickerPosition() {
	if (!birthTimeRef.value) return
	const rect = birthTimeRef.value.getBoundingClientRect()
	timePickerStyle.value = {
		left: rect.left + 'px',
		top: rect.bottom + 8 + 'px',
		position: 'fixed',
	}
}

// 监听时间选择器显示状态变化
watch(showTimePicker, (val) => {
	if (val) {
		setTimeout(() => {
			window.addEventListener('scroll', updateTimePickerPosition, true)
			window.addEventListener('resize', updateTimePickerPosition)
		}, 0)
		nextTick(() => {
			updateTimePickerPosition()
		})
	} else {
		window.removeEventListener('scroll', updateTimePickerPosition, true)
		window.removeEventListener('resize', updateTimePickerPosition)
	}
})

// 切换时间选择器显示状态
function toggleTimePicker() {
	showTimePicker.value = !showTimePicker.value
	nextTick(() => {
		if (showTimePicker.value) {
			updateTimePickerPosition()
		}
	})
}

// 处理时间确认
function handleTimeConfirm(val: { hour: number; minute: number }) {
	createOrderForm.birthTime = `${val.hour.toString().padStart(2, '0')}:${val.minute.toString().padStart(2, '0')}`
	showTimePicker.value = false
}

// 获取常见问题列表
const getAugurOrderCommonQuestionFunc = () => {
	getAugurOrderCommonQuestion()
		.then((response) => {
			specificQuestionList.value = response.data
			// createOrderForm.specificQuestion = response.data[0]
		})
		.catch((error) => {})
}

// 获取占卜师详细信息
const getAugurDetailData = async () => {
	try {
		const res = await getAugurDetail({ augurUid: createOrderForm.augurUid })
		console.log(res)
		augurDetail.value = res.data
		augurFortune.value = res.data.augurFortune.filter((item: any) => item.id == route.query.augurFortuneId)[0]
	} catch (error) {
		console.log(error)
	} finally {
	}
}

// 处理标签显示
const returnItemTags = (tags: any[]) => {
	if (!tags) return ''
	return (tags.map((item) => item.name).join('/') + '/').slice(0, -1)
}

// 更改具体问题
const changeSpecificQuestion = (item: string) => {
	createOrderForm.specificQuestion = item
}

const handleChangePlace = (data: any) => {
	// createOrderForm.latitude = data.latitude
	// createOrderForm.longitude = data.longitude
}

const createOrderFunc = async (formRef: FormInstance|undefined) => {
    if (!formRef) return
    const isValid = await formRef.validate()
		if (isValid) {
            createOrderLoading.value = true
			createOrderForm.birth = String(new Date(createOrderForm.birthDate + ' ' + createOrderForm.birthTime).getTime() / 1000)
			createAugurOrder({
				augurFortuneGroupId: createOrderForm.augurFortuneGroupId,
				augurFortuneId: createOrderForm.augurFortuneId,
				augurUid: createOrderForm.augurUid,
				name: createOrderForm.name,
				gender: createOrderForm.gender,
				birth: createOrderForm.birth,
				generalSituation: createOrderForm.generalSituation,
				specificQuestion: createOrderForm.specificQuestion,
                birthPlace:createOrderForm.birthPlace,
			})
				.then((res) => {
					console.log(res)
                    router.push({
                        path:'/onlineOrder',
                        query:{
                            augurUid: createOrderForm.augurUid,
                            orderNo:res.data.orderNo,
                            discountRatio:res.data.discountRatio,
                            imGroupId:res.data.imGroupId,
                            expireSeconds:res.data.expireSeconds,
                            augurFortuneGroupId:createOrderForm.augurFortuneGroupId
                        }
                    })
				})
				.catch((err) => {
					console.log(err)
				}).finally(()=>{
                    createOrderLoading.value = false
                })
		} else {
			console.log('error submit!!')
        return false
    }
}
// 组件挂载时初始化数据
onMounted(() => {
	// 从路由参数中获取必要信息
	createOrderForm.augurFortuneGroupId = route.query.augurFortuneGroupId as string
	createOrderForm.augurFortuneId = route.query.augurFortuneId as string
	createOrderForm.augurUid = route.query.uid as string
	// 获取初始数据
	getAugurDetailData()
	getAugurOrderCommonQuestionFunc()
})
</script>
<style lang="scss" scoped>
/* 权益卡片背景样式 */
.equity {
	background: linear-gradient(91.29deg, rgba(122, 175, 255, 0.1) 1.1%, rgba(122, 135, 255, 0.1) 95.36%);
}

/* Element Plus表单样式重写 */
:deep(.el-form) {
	.el-form-item {
		/* 表单标签样式 */
		.el-form-item__label {
			font-size: 14px;
			font-weight: 700;
			font-family: 'Philosopher';

			/* 必填标记样式 */
			span {
				&::after {
					content: attr(data-content);
					font-size: 12px;
					color: #ff5353;
					margin-left: 4px;
					font-weight: 400;
				}
			}
		}

		/* 白色背景表单项样式 */
		&.white-bg {
			.el-form-item__content {
				background-color: #fff;
			}
		}

		/* 表单控件容器样式 */
		.el-form-item__content {
			.el-input__wrapper,
			.el-select__wrapper {
				height: 44px;
				font-size: 15px;
				border-radius: 6px;
				font-weight: 400;
				color: #1c2158;
				font-family: 'Open Sans';
			}

			/* 输入框文本颜色 */
			.el-input__inner {
				color: #1c2158;
			}
		}
	}

	/* 大屏幕响应式样式 */
	@media screen and (min-width: 1330px) {
		.el-form-item {
			margin-bottom: 24px;

			/* 大屏幕下的标签样式 */
			.el-form-item__label {
				font-size: 22px;
				font-weight: 700;
				font-family: 'Philosopher';
			}

			/* 大屏幕下的白色背景样式 */
			&.white-bg {
				.el-form-item__content {
					background-color: #f8f8fa;
				}
			}

			/* 大屏幕下的表单控件样式 */
			.el-form-item__content {
				background-color: #f8f8fa;

				/* 文本域样式 */
				.el-textarea__inner {
					font-size: 16px;
					border-radius: 12px;
					background-color: #f8f8fa;
					color: #1c2158;
					font-family: 'Open Sans';
				}

				/* 输入框和选择器样式 */
				.el-input__wrapper,
				.el-select__wrapper {
					height: 50px;
					font-size: 20px;
					border-radius: 12px;
					background-color: transparent;
					color: #1c2158;
					font-family: 'Open Sans';
				}
			}
		}
	}
}

/* 性别选择器激活状态样式 */
.active-gender {
	&::before {
		content: '';
		position: absolute;
		inset: 0;
		width: 100%;
		height: 100%;
		padding: 1px;
		border-radius: 6px;
		background: linear-gradient(104.04deg, #96baff 0%, #7064ff 86.39%);
		-webkit-mask: linear-gradient(#96baff 0 0) content-box, linear-gradient(#96baff 0 0);
		-webkit-mask-composite: xor;
		mask-composite: exclude;
		pointer-events: none;
	}
}

/* 确认按钮样式 */
.confirm-button {
	background: linear-gradient(91.29deg, #7aafff 1.1%, #7a87ff 95.36%);

	/* 悬停效果 */
	&:hover {
		box-shadow: 0px 0px 12px 0px rgba(0, 0, 0, 0.25);
	}
}

/* 问题选择激活状态样式 */
.question-active {
	color: rgba(68, 132, 255, 0.7);
	border-color: #4484ff;
}
</style>
