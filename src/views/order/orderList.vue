<!-- app -->
<template>
    <div class="app-container">
        <div class="main-content pb-[20px] px-[15px] md:px-[15px] lg:px-[120px]">
            <el-breadcrumb :separator-icon="ArrowRight"
                class="font-['Open_Sans_3'] font-[600] text-[14px] pt-[8px] mb-[14px] md:pt-6 lg:pt-6 md:mb-[30px] lg:mb-[30px]">
                <el-breadcrumb-item class="text-[#1C2158]">Home</el-breadcrumb-item>
                <el-breadcrumb-item>My Orders</el-breadcrumb-item>
            </el-breadcrumb>
            <el-skeleton :loading="isFirstLoad && loading" animated>
                <template #template>
                    <div class="order-list-container flex">
                        <OrderLeft class="shrink-0 hidden md:block" activeItem="My Orders" />
                        <div class="order-list-right flex-1 md:bg-white md:rounded-[30px] md:p-[40px] overflow-auto">
                            <div class="order-list-right-header flex items-center justify-between">
                                <el-skeleton-item variant="h1" style="width:120px;height:28px;" />
                                <div class="flex items-center">
                                    <el-skeleton-item variant="rect"
                                        style="width:120px;height:36px;border-radius:6px;margin-right:10px;" />
                                    <el-skeleton-item variant="rect"
                                        style="width:350px;height:44px;border-radius:6px;" />
                                </div>
                            </div>
                            <div class="order-list-container mt-6">
                                <el-skeleton-item variant="rect" style="width:100%;height:400px;border-radius:16px;" />
                            </div>
                        </div>
                    </div>
                </template>
                <template #default>
                    <div class="order-list-container flex">
                        <OrderLeft class="shrink-0 hidden md:block" activeItem="My Orders" />
                        <div class="order-list-right flex-1 md:bg-white md:rounded-[30px] md:p-[40px] overflow-auto">
                            <div class="order-list-right-header flex items-center justify-between">
                                <div
                                    class="order-list-right-header-title text-[18px] leading-[20px] text-[#1C2158] font-['Philosopher'] font-bold md:text-[28px] md:leading-[32px]">
                                    My Orders</div>
                                <div class="flex items-center">
                                    <el-popover placement="bottom-start" trigger="click" :visible="popoverVisible"
                                        :teleported="false" popper-class="recharge-popover">
                                        <template #reference>
                                            <div class="order-list-right-header-type px-[10px] py-[6px] rounded-[6px] flex items-center justify-center border border-[#e7e9ef] border-solid cursor-pointer md:py-[14px]"
                                                @click="popoverVisible = !popoverVisible">
                                                <span class="text-[14px] leading-[15px] text-[#1C2158] opacity-[0.6]">{{
                                                    selectType }}</span>
                                                <img src="@/assets/order/type-arrow-down.png" alt="type-arrow-down"
                                                    class="w-[10px] h-[10px] ml-2 lg:ml-[52px]" />
                                            </div>
                                        </template>
                                        <template #default>
                                            <div class="type-list cursor-pointer">
                                                <div class="type-list-item text-[16px] leading-[20px] text-[#1C2158] pl-[20px] py-[17px]"
                                                    @click="changeType('Text Reading', '1')">Text Reading</div>
                                                <div class="type-list-item text-[16px] leading-[20px] text-[#1C2158] pl-[20px] py-[17px]"
                                                    @click="changeType('Live Text Chat', '4')">Live Text Chat</div>
                                            </div>
                                        </template>
                                    </el-popover>
                                    <div
                                        class="order-list-right-header-search-lg hidden ml-5 h-[44px] w-[350px] rounded-[6px] bg-[#F8F8FA] md:block">
                                        <div class="w-full h-full flex items-center justify-between pl-[10px] pr-1">
                                            <img src="@/assets/order/search.png" alt="search"
                                                class="w-[20px] h-[20px] cursor-pointer" @click="getMySelfOrderListFunc(1)"/>
                                            <input type="text"
                                                v-model="keyword" class="flex-1 outline-none ml-[10px] bg-transparent font-['Open_Sans_3'] font-[400] text-[14px] text-[#1C2158]"
                                                placeholder="Search by Consultant Name" @keydown.enter="getMySelfOrderListFunc(1)"/>
                                            <button class="search-btn text-white text-[16px] h-[36px] px-[15px] rounded-[4px] cursor-pointer" @click="getMySelfOrderListFunc(1)">Search</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!--小于md 时显示的搜索框-->
                            <div
                                class="order-list-right-header-search w-full flex items-center bg-white rounded-[4px] h-[38px] mt-[14px] p-[10px] md:hidden">
                                <img src="@/assets/order/search.png" alt="search"
                                    class="w-[16px] h-[16px] cursor-pointer" />
                                <input type="text"
                                    class="flex-1 outline-none ml-[10px] font-['Open_Sans_3'] font-[400] text-[14px] text-[#1C2158]"
                                    placeholder="Search by Consultant Name" />
                            </div>
                            <!--订单列表-->
                            <div class="order-list-container">
                                <el-table :data="orderList" style="width: 100%" :border="false"
                                    :header-row-class-name="'order-list-table-header'" :row-class-name="rowClassName"
                                    v-loading="loading">
                                    <el-table-column width="25">
                                        <template #default="scope">
                                            <!-- <span >{{ scope.row.index }}</span> -->
                                            <div v-if="scope.row.userUnread !== 0"
                                                class="w-[12px] h-[12px] rounded-[50%] bg-[#FF0000] border border-white border-solid shrink-0">
                                            </div>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="date" label="Order Details" show-overflow-tooltip>
                                        <template #default="scope">
                                            <div class="flex items-center w-full overflow-hidden">
                                                <img :src="scope.row.augur.avatar" alt=""
                                                    class="w-[50px] h-[50px] mr-3 rounded-[5px]" />
                                                <div
                                                    class="order-list-table-column-name flex-1 min-w-0 text-[16px] leading-[20px] text-[#1C2158] font-['Open_Sans_3'] font-[400] line-clamp-1">
                                                    {{ scope.row.augur.username }}</div>
                                            </div>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="name" label="Order Type">
                                        <template #default="scope">
                                            <div
                                                class="text-[16px] leading-[20px] text-[#1C2158] font-['Open_Sans_3'] font-[400]">
                                                {{ returnOrderType(scope.row.augurFortuneGroupId) }}</div>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="address" label="Order Date">
                                        <template #default="scope">
                                            <div
                                                class="text-[16px] leading-[20px] text-[#1C2158] font-['Open_Sans_3'] font-[400]">
                                                {{ returnOrderCreateTime(scope.row.createTimestamp) }}</div>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="address" label="Order Status">
                                        <template #default="scope">
                                            <div v-if="statusMap[scope.row.status]"
                                                :class="['table px-[14px] py-[6px] rounded-[29px] text-[16px] leading-[20px] font-[\'Open_Sans_3\'] font-[400]', statusMap[scope.row.status].class]">
                                                {{ statusMap[scope.row.status].text }}
                                            </div>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="address" label="Operation">
                                        <template #default="scope">
                                            <div class="flex items-center">
                                                <div class="text-[16px] leading-[20px] text-[#1C2158] font-['Open_Sans_3'] opacity-[0.5] font-[400] mr-[19px] cursor-pointer"
                                                    @click="goOrderDetail(scope.row)">
                                                    Detail</div>
                                                <img src="@/assets/order/delete-order.png" alt="delete-order"
                                                    class="w-[16px] h-[16px] cursor-pointer"
                                                    @click="handleDeleteOrder(scope.row)" />
                                            </div>
                                        </template>
                                    </el-table-column>
                                    <template #empty>
                                        <div
                                            class="order-list-empty flex flex-col items-center justify-center mt-[60px]">
                                            <img src="@/assets/order/order-empty.png" alt="order-empty"
                                                class="w-[300px] h-[300px]" />
                                            <div
                                                class="order-list-empty-text font-['Open_Sans_3'] font-[600] text-[16px] leading-[22px] text-[#1C2158] opacity-[0.5] mt-[21px]">
                                                You don't have any orders</div>
                                        </div>
                                    </template>
                                </el-table>
                                <!--小屏幕下展示的订单列表-->
                                <div class="order-list-container-mobile md:hidden">
                                    <div class="order-list-container-mobile-item px-3 py-[10px] bg-white rounded-[6px] mt-[10px] cursor-pointer"
                                        v-for="(item, index) in orderList" :key="index"
                                        :class="{ 'awaiting-reply': item.userUnread !== 0 }"
                                        @click="goOrderDetail(item)">
                                        <div class="flex justify-between">
                                            <div class="flex items-center">
                                                <img :src="item.augur.avatar" alt=""
                                                    class="w-[50px] h-[50px] mr-3 rounded-[50%]" />
                                                <div class="order-list-table-column-name">
                                                    <div
                                                        class="text-[16px] leading-[20px] lg:font-semibold text-[#1C2158] font-['Open_Sans'] font-[400]">
                                                        {{ item.augur.username }}</div>
                                                    <div class="flex items-center mt-[6px]">
                                                        <!--订单类型-->
                                                        <div class="flex items-center">
                                                            <img src="@/assets/order/medal.png" alt=""
                                                                class="w-[18px] h-[18px] mr-[2px]"
                                                                v-if="item.expeditingType == 2" />
                                                            <span
                                                                class="whitespace-nowrap text-[14px] leading-[18px] text-[#1C2158] font-['Open_Sans_3'] font-[400] opacity-[0.9]">{{
                                                                    returnOrderType(item.augurFortuneGroupId) }}</span>
                                                        </div>
                                                        <!--订单类型分割线-->
                                                        <div class="line h-[14px] w-[1px] bg-[#1C215814] mx-[8px]">
                                                        </div>
                                                        <!--订单状态-->
                                                        <div class="order-status">
                                                            <div v-if="statusMap[item.status]"
                                                                :class="['text-[14px] leading-[18px] font-[\'Open_Sans_3\'] font-[400] bg-transparent', statusMap[item.status].class]">
                                                                {{ statusMap[item.status].text }}
                                                            </div>
                                                        </div>
                                                        <!--真实评价的展示-->
                                                        <div class="line h-[14px] w-[1px] bg-[#1C215814] mx-[8px]"
                                                            v-if="item.comments && item.comments.length"></div>
                                                        <div v-if="item.comments && item.comments.length"
                                                            class="flex items-center">
                                                            <img src="@/assets/common/one-star.png"
                                                                class="w-[12px] h-[12px] mr-[4px]">
                                                            <span
                                                                class="text-[13px] text-[#FF7527] leading-[16px] font-[500]">{{
                                                                item.grade.toFixed(1) }}</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div
                                                class="order-time text-[12px] leading-[15px] text-[#1C2158] font-['Source_Sans_3'] font-[400] opacity-[0.5]">
                                                {{ returnTimes(item.createTimestamp, 'hours') }}</div>
                                        </div>
                                        <!--订单留言，有或者没有-->
                                        <div class="order-message flex items-center justify-between px-[10px] py-[6px] rounded-[6px] bg-[#F8F8FA] mt-3"
                                            v-if="item.augurFortuneGroupId == 4">
                                            <div class="flex items-center">
                                                <img src="@/assets/order/message.png" alt=""
                                                    class="w-[24px] h-[24px] mr-1" />
                                                <span
                                                    class="text-[14px] leading-[18px] text-[#1C2158] font-['Source_Sans_3'] font-[400] line-clamp-1 opacity-[0.6]">Live
                                                    text chat replies</span>
                                            </div>
                                            <img src="@/assets/order/arrow-right.png" alt=""
                                                class="w-[16px] h-[16px]" />
                                        </div>
                                        <div class="order-message flex items-center justify-between px-[10px] py-[6px] rounded-[6px] bg-[#F8F8FA] mt-3"
                                            v-else-if="item.augurFortuneGroupId == 1">
                                            <div class="flex items-center line-clamp-1">
                                                <span
                                                    class="text-[14px] leading-[18px] text-[#1C2158] font-['Source_Sans_3'] font-[400] line-clamp-1 opacity-[0.6]">{{
                                                    item.generalSituation }}</span>
                                            </div>
                                            <img src="@/assets/order/arrow-right.png" alt=""
                                                class="w-[16px] h-[16px]" />
                                        </div>
                                    </div>
                                    <div class="order-list-empty flex flex-col items-center justify-center mt-[60px]"
                                        v-if="finished && !orderList.length">
                                        <img src="@/assets/order/order-empty.png" alt="order-empty"
                                            class="w-[300px] h-[300px]" />
                                        <div
                                            class="order-list-empty-text font-['Open_Sans_3'] font-[600] text-[16px] leading-[22px] text-[#1C2158] opacity-[0.5] mt-[21px]">
                                            You don't have any orders</div>
                                    </div>
                                </div>
                                <div class="show-more-btn flex items-center justify-center mt-[18px] px-[27px] md:px-[290px] md:mt-[40px]"
                                    v-if="!finished && orderList.length">
                                    <div class="show-more-btn-text w-full py-[14px] rounded-[10px] text-[20px] leading-[20px]  font-['Philosopher'] font-[400] text-center text-white"
                                        
                                        @click="getMySelfOrderListFunc(0)">
                                        Show More</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>
            </el-skeleton>
            <DeleteOrderDialog :dialog-delete-order-visible="dialogDeleteOrderVisible"
                @update:dialog-delete-order-visible="handleDeleteOrderVisible"></DeleteOrderDialog>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { ArrowRight } from '@element-plus/icons-vue'
import OrderLeft from '@/components/orderLeft.vue'
import DeleteOrderDialog from './components/deleteOrderDialog.vue'
import { returnTimes } from '@/utils'
import { getMySelfOrderList } from '@/api/order'
import { useRouter } from 'vue-router'

const router = useRouter()

const statusMap: { [key: number]: { text: string; class: string } } = {
    6: { text: 'Completed', class: 'bg-[#4BDD7426] text-[#4BDD74]' },
    1: { text: 'Pending', class: 'bg-[#FC791A26] text-[#FC791A]' },
    2: { text: 'Cancel', class: 'bg-[#8D8C9226] text-[#8D8C92]' },
    4: { text: 'Expired', class: 'bg-[#F4B00026] text-[#F4B000]' },
    5: { text: 'Awaiting Receipt', class: 'bg-[#4073F826] text-[#4073F8]' },
    3: { text: 'Declined', class: 'bg-[#EF42BF26] text-[#EF42BF]' },
}

const popoverVisible = ref(false)
const selectType = ref('All Type')
const augurFortuneGroupId = ref('')
const keyword =ref('')
const tableData = ref([{}, {}, {}])
const loading = ref(false)
const isFirstLoad = ref(true)

const current = ref(1)
const finished = ref(false)

const orderList = ref<any[]>([])
const dialogDeleteOrderVisible = ref(false)
const rowClassName = (row: any) => {
    if (row.row.status === 'Completed') {
        return 'completed-row'
    }
    if (row.row.userUnread !== 0) {
        return 'first-row'
    }
    return ''
}
const changeType = (type: string, value: string) => {
    selectType.value = type
    augurFortuneGroupId.value = value
    popoverVisible.value = false
    getMySelfOrderListFunc(1)
}
const handleDeleteOrderVisible = (visible: boolean) => {
    dialogDeleteOrderVisible.value = visible
}
const handleDeleteOrder = (row: any) => {
    dialogDeleteOrderVisible.value = true
}
const goOrderDetail = (item: any) => {
    router.push(`/orderDetail/${item.orderNo}`)
}
const getMySelfOrderListFunc = async (type?: number) => {
    if (type === 1) {
        current.value = 1
        finished.value = false
        orderList.value.length = 0
        isFirstLoad.value = false
    }
    if (finished.value) return
    try {
        if (current.value === 1 && orderList.value.length === 0 && isFirstLoad.value) {
            loading.value = true
        } else {
            loading.value = true
        }
        const res = await getMySelfOrderList({
            current: current.value,
            keyword:keyword.value,
            augurFortuneGroupId:augurFortuneGroupId.value
        })
        console.log(res)
        orderList.value = [...orderList.value, ...res.data]
        if (res.count === orderList.value.length) {
            finished.value = true
        }
        current.value++
    } catch (error) {
        finished.value = true
    } finally {
        loading.value = false
        isFirstLoad.value = false
    }
}
const returnOrderType = (augurFortuneGroupId: number) => {
    switch (augurFortuneGroupId) {
        case 1:
            return "Text Reading";
        case 2:
            return "Audio Reading";
        case 3:
            return "Video Reading";
        case 4:
            return "Live Text Chat";
        case 5:
            return "Live Audio Chat";
        case 6:
            return "Live Video Chat";
        case 7:
            return "Public Reading";
        case 8:
            return "Premium Service";
        default:
            return ''
    }
}
const returnOrderCreateTime = (timeStamp: number) => {
    const time = new Date(timeStamp * 1000).toUTCString()
    return time.split('GMT')[0]
}
onMounted(() => {
    getMySelfOrderListFunc()
})
</script>
<style lang="scss" scoped>
:deep(.recharge-popover) {
    background: #fff;
    border-radius: 16px;
    padding: 0;
    box-sizing: border-box;
    width: 148px !important;
    z-index: 20 !important;
    box-shadow: 0px 0px 4px 0px #bac5ff;

    .el-popper__arrow {
        &::before {
            background: #fff !important;
        }
    }

    .el-popover__title {
        display: none;
    }
}

.search-btn {
    background: linear-gradient(91.29deg, #7aafff 1.1%, #7a87ff 95.36%);
}

.el-table {
    margin-top: 20px;

    :deep(.el-table__inner-wrapper) {
        &::before {
            background: none !important;
        }
    }

    :deep().el-table__row {
        margin-top: 2px;
    }

    :deep().first-row {
        background: linear-gradient(91.29deg, rgba(122, 175, 255, 0.25) 1.1%, rgba(122, 135, 255, 0.25) 95.36%);
    }

    :deep(.el-table__cell) {
        border-bottom: none !important;
    }
}

.awaiting-reply {
    background: linear-gradient(91.29deg, rgba(122, 175, 255, 0.25) 1.1%, rgba(122, 135, 255, 0.25) 95.36%);

    .order-message {
        background-color: #fff;
    }
}

:deep().order-list-table-header {
    .el-table__cell {
        font-size: 18px;
        font-family: 'Philosopher';
        font-weight: 700;
        color: #1c2158;
        height: 50px;
        background: #f8f8fa;
    }
}

@media (max-width: 1120px) {
    .order-list-container {
        .el-table {
            display: none !important;
        }
    }
}

.show-more-btn-text {
    cursor: pointer;
    background: linear-gradient(91.29deg, #7aafff 1.1%, #7a87ff 95.36%);

    &:hover {
        box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.3);
    }
}
</style>
