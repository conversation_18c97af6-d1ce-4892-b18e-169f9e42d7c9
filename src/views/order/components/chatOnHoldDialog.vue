<!-- 取消订单二次确认弹窗 -->
<template>
	<div class="">
		<el-dialog :model-value="dialogChatOnHoldVisible" @update:model-value="$emit('update:dialogChatOnHoldVisible', $event)" :close-on-click-modal="false" :before-close="handleChatOnHoldClose" :show-close="false" class="flex flex-col h-max w-[345px] absolute mb-[0] left-[50%] top-[50%] mt-[0px] translate-y-[-40%] translate-x-[-50%] rounded-[18px] md:w-[400px] lg:w-[500px] p-0">
			<div class="chat-on-hold border  border-[#fff] rounded-[22px] border-solid pt-[13px] px-[10px] w-full max-h-[690px] overflow-y-auto">
				<div class="advisor-info flex items-center absolute top-[-20px] pl-[12px] pr-[40px] left-0 w-full h-[60px] bg-white rounded-[12px] md:hidden">
					<div class="flex items-center">
						<div class="flex items-center">
							<img :src="augurDetail.avatar"  class="w-[36px] h-[36px] rounded-[50%] mr-[8px] md:w-[50px] md:h-[50px] md:mr-[15px]" />
							<div class="">
								<span class="advisor-name leading-[20px] font-['Open_Sans_3'] text-[15px]  font-bold">{{ augurDetail.username }}</span>
								<div class="advisor-info-desc text-[#1C2158] text-normal-opacity-70 leading-[16px] text-[13px] line-clamp-1">I See.your daily horscope will be lucky.it...I See.your daily horscope will be lucky.it...</div>
							</div>
						</div>
					</div>
				</div>
				<div class="chat-on-hold-top flex items-center justify-between">
					<div class="chat-on-hold-top-left">
						<div class="chat-on-hold-top-left-title leading-[20px] font-['Source_Sans_Pro'] text-[16px] text-[#1C2158] cursor-pointer" @click="dialogCancelConfirmVisible = true">End chat</div>
					</div>
					<div class="chat-on-hold-top-right flex items-center px-[10px] py-[5px] rounded-[8px] border border-[#1C2158] border-solid cursor-pointer">
						<span class="text-[#1C2158] leading-[18px] font-500 text-[14px]">65</span>
						<img src="@/assets/common/money.png"  class="w-[14px] h-[14px] ml-[3px]" />
					</div>
				</div>
				<!---倒计时-->
				<div class="chat-on-hold-countdown flex items-center justify-center mt-[4px]">
					<div class="flex items-center justify-center">
						<div class="chat-on-hold-countdown-item mr-[7px]">{{ minutes1 }}</div>
						<div class="chat-on-hold-countdown-item">{{ minutes2 }}</div>
					</div>
					<div class="text-[#F53A3A] font-bold text-[22px] font-['Philosopher'] leading-[25px] w-[22px] text-center">:</div>
					<div class="flex items-center justify-center">
						<div class="chat-on-hold-countdown-item mr-[7px]">{{ seconds1 }}</div>
						<div class="chat-on-hold-countdown-item">{{ seconds2 }}</div>
					</div>
				</div>
				<!--title-->
				<div class="chat-on-hold-title mt-2 font-['Open_Sans_3'] text-[20px] text-[#1C2158] font-bold leading-[27px] text-center lg:mt-[14px]">Chat on Hold</div>
				<!--content-->
				<div class="chat-on-hold-content mt-[10px] px-[10px] font-['Open_Sans_3'] text-[13px] text-[#4484FF] font-semibold leading-[18px] text-center lg:mt-[12px]">Looking for deeper insights? {{ augurDetail.username }} is still waiting for your return.</div>
				<div class="text-center font-['Open_Sans_3'] text-[15px] text-[#1C2158] mt-3 font-normal leading-[20px] lg:mt-[3px]">Additional credits required to continue the chat：</div>
				<!--所需要的金币-->
				<div class="flex items-center justify-center mt-[6px]">
					<span class="text-[#FF7C44] text-[20px] font-bold leading-[30px]">12</span>
					<img src="@/assets/common/money.png"  class="w-[24px] h-[24px] ml-[4px]" />
				</div>
				<!--6分钟免费-->
				<div class="relative flex items-center justify-between mt-[10px] px-[15px] py-[11px] bg-white rounded-[10px] pl-[12px]">
					<div class="flex items-center justify-center">
						<img src="@/assets/common/6minutes.png"  class="w-[50px] h-[50px]" />
						<div class="flex flex-col ml-[8px]">
							<span class="text-[#1C2158] text-[19px] font-semibold leading-[26px]">6-Min Live chat</span>
							<span class="text-[#1C2158] text-[15px] leading-[19px] mt-[-2px]">Limited-Time Offer</span>
						</div>
					</div>
					<div class="recharge-popover-list-item-right px-[21px] py-[9px] leading-[23px] rounded-[10px]">
						<span class="font-['Open_Sans_3'] text-[17px] font-bold leading-[23px] text-white">$1.99</span>
					</div>
					<div class="six-minutes-free absolute left-0 top-0 px-[8px] text-[13px] text-white font-['TTChocolates'] leading-[16px] rounded-tl-[10px] rounded-br-[10px]">
						{{ ' 23:59:00' }}
					</div>
				</div>
				<!--活动图片-->
				<img src="@/assets/common/activity.png"  class="w-full mt-[10px]" />
				<!--充值列表 普通金额-->
				<div class="recharge-popover-list">
					<div class="recharge-popover-list-item mt-[10px] px-[10px] py-[8px] bg-white rounded-[8px] flex items-center justify-between cursor-pointer" v-for="item in 3">
						<div class="recharge-popover-list-item-left flex items-center">
							<img src="@/assets/common/dbmoney.png"  class="w-[42px] h-[42px] mr-2" />
							<div class="flex flex-col">
								<span class="flex itmems-center font-['Open_Sans_3'] text-[19px] font-bold leading-[22px] text-[#1C2158]">100<span class="text-[#1C2158] text-[13px] font-semibold"> Credits</span></span>
								<span class="flex itmems-center font-['Open_Sans_3'] text-[14px] font-bold leading-[19px] text-[#FF7C44]">+10<span class="text-[13px]"> Free Bonus</span></span>
							</div>
						</div>
						<div class="recharge-popover-list-item-right px-[21px] py-[9px] leading-[23px] rounded-[10px]">
							<span class="font-['Open_Sans_3'] text-[17px] font-bold leading-[23px] text-white">$1.99</span>
						</div>
					</div>
				</div>
			</div>
		</el-dialog>
        <!--结束按钮-->
        <el-dialog :model-value="dialogCancelConfirmVisible" :close-on-click-modal="false" :before-close="handleCancelConfirmClose" :show-close="false" class="flex flex-col h-max w-[340px] absolute mb-[0] left-[50%] top-[50%] mt-[0px] translate-y-[-40%] translate-x-[-50%] rounded-[18px] md:w-[400px] lg:w-[480px] p-0">
			<div class="py-[30px] px-[16px] w-full flex flex-col items-center lg:p-[40px]">
				<div class="font-bold  font-['Philosopher'] text-[20px] text-center text-[#1c2158] lg:text-[22px]">Confirm ending the chat?</div>
                <div class="text-normal-opacity-70 text-[16px] text-center mt-[13px] leading-[20px] lg:mt-[10px]">
                    Continuing will disconnect the advisor.
                </div>
				<!--按钮-->
				<div class="flex gap-[13px] flex-row-reverse w-full mt-[21px] lg:gap-[30px] lg:flex-row">
					<div class="flex-1 order-btn py-[13px] rounded-[6px] border border-[#4484FF] border-solid text-[#4484FF] font-['Philosopher'] text-[16px] leading-[18px] text-center cursor-pointer lg:leading-[22px] lg:text-[20px]" >End Chat</div>
					<div class="flex-1 order-btn check-detail py-[13px] rounded-[6px] text-white font-['Philosopher'] text-[16px] leading-[18px] text-center cursor-pointer lg:leading-[22px] lg:text-[20px]" @click="handleCancelConfirmClose">Think again</div>
				</div>
			</div>
		</el-dialog>
	</div>
</template>

<script lang="ts" setup name="ChatOnHoldDialog">
import { ref, onMounted, onUnmounted, watch } from 'vue'
const emit = defineEmits(['update:dialogChatOnHoldVisible'])

const dialogCancelConfirmVisible = ref(false)

const props = defineProps({
	dialogChatOnHoldVisible: {
		type: Boolean,
		default: false,
	},
	duration: {
		// 支持传入倒计时秒数
		type: Number,
		default: 1000,
	},
    augurDetail: {
        type: Object,
        default: () => ({}),
    },
    augurFortune: {
        type: Object,
        default: () => ({}),
    },
})

let timer: number | null = null
const minutes1 = ref(0)
const minutes2 = ref(0)
const seconds1 = ref(0)
const seconds2 = ref(0)
const leftSeconds = ref(props.duration)

const handleChatOnHoldClose = () => {
	emit('update:dialogChatOnHoldVisible', false)
}
const handleCancelConfirmClose = () => {
	dialogCancelConfirmVisible.value = false
}


const updateDigits = (totalSeconds: number) => {
	const mins = Math.floor(totalSeconds / 60)
	const secs = totalSeconds % 60
	minutes1.value = Math.floor(mins / 10)
	minutes2.value = mins % 10
	seconds1.value = Math.floor(secs / 10)
	seconds2.value = secs % 10
}

const startCountdown = () => {
	leftSeconds.value = props.duration
	updateDigits(leftSeconds.value)
	if (timer) clearInterval(timer)
	timer = window.setInterval(() => {
		if (leftSeconds.value > 0) {
			leftSeconds.value--
			updateDigits(leftSeconds.value)
		} else {
			clearInterval(timer!)
			timer = null
			handleChatOnHoldClose()
		}
	}, 1000)
}

onMounted(() => {
	if (props.dialogChatOnHoldVisible) {
		startCountdown()
	}
})

watch(
	() => props.dialogChatOnHoldVisible,
	(val) => {
		if (val) {
			startCountdown()
		} else {
			if (timer) {
				clearInterval(timer)
				timer = null
			}
		}
	}
)

onUnmounted(() => {
	if (timer) {
		clearInterval(timer)
		timer = null
	}
})
</script>
<style lang="scss" scoped>
:deep(.el-dialog) {
	padding: 0;
	display: flex;
	flex-direction: column;

	.el-dialog__header {
		display: none;
	}
	.el-dialog__body {
		flex: 1;
		padding: 0 !important;
	}
	.chat-on-hold {
		background: linear-gradient(180deg, #d7e7ff 0%, #f0f5fd 100%);
	}
	.chat-on-hold-countdown-item {
		width: 40px;
		height: 45px;
		box-shadow: 0px 0px 6px 0px #7badf7;
		background: linear-gradient(180deg, #f5f9ff 0%, rgba(255, 255, 255, 0.1) 100%);
		border-radius: 10px;
		border: 1.5px solid #fff;
		display: flex;
		align-items: center;
		justify-content: center;
		font-family: Philosopher;
		font-weight: 700;
		font-size: 22px;
		line-height: 25px;
		text-align: center;
		vertical-align: middle;
		color: #f53a3a;
	}
	.recharge-popover-list-item-right {
		background: linear-gradient(91.29deg, #7aafff 1.1%, #7a87ff 95.36%);
	}
	.six-minutes-free {
		background: linear-gradient(90deg, #ff9d42 0%, #ff5353 100%);
		box-shadow: 0px -1px 3px 0px #ffffff40 inset;
	}
    .advisor-name{
        background: linear-gradient(91.29deg, #7AAFFF 1.1%, #7A87FF 95.36%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }
    .check-detail {
		background: linear-gradient(91.29deg, #7aafff 1.1%, #7a87ff 95.36%);
	}
}
</style>
