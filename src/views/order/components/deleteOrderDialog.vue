<!-- 取消订单二次确认弹窗 -->
<template>
	<div class="">
		<el-dialog :model-value="dialogDeleteOrderVisible" @update:model-value="$emit('update:dialogDeleteOrderVisible', $event)" :close-on-click-modal="false" :before-close="handleDeleteOrderClose" :show-close="false" class="flex flex-col h-max w-[340px] absolute mb-[0] left-[50%] top-[50%] mt-[0px] translate-y-[-40%] translate-x-[-50%] rounded-[18px] md:w-[400px] lg:w-[480px] p-0">
			<div class="relative pb-[20px] pt-[61px] px-[28px] w-full flex flex-col items-center lg:py-[40px] lg:px-[45px]">
				<div class="chat-close lg:hidden absolute top-[-43px] right-0 left-0 mx-auto flex items-center justify-center w-[98px] h-[98px] rounded-[50%]">
                    <img src="@/assets/family.png" alt="" class="w-[74px] h-[74px] rounded-[50%]">
                </div>
				<div class="font-bold font-['Philosopher'] text-[20px] text-center text-[#1c2158] lg:text-[22px]">Sure to Delete?</div>

				<div class="text-[#1C2158B2] font-['Philosopher'] text-[16px] leading-[20px] text-center mt-[20px]">Order cannot be recovered once deleted and your advisor will no longer be able to access this order either. Confirm to delete?</div>
				<!--按钮-->
				<div class="flex  gap-[13px] w-full mt-[21px] lg:gap-[30px] lg:flex-row">
					<div class="flex-1 order-btn py-[12px] font-['Philosopher'] text-[16px] leading-[18px] text-center cursor-pointer lg:leading-[22px] lg:text-[20px]  border border-[#4484FF] border-solid text-[#4484FF] lg:rounded-[6px] lg:opacity-100">Still Deleted</div>
					<div class="flex-1 order-btn check-detail py-[13px] rounded-[6px] text-white font-['Philosopher'] text-[16px] leading-[18px] text-center cursor-pointer lg:leading-[22px] lg:text-[20px]" @click="handleDeleteOrderClose">No, thanks</div>
				</div>
			</div>
		</el-dialog>
	</div>
</template>

<script lang="ts" setup name="DeleteOrderDialog">
import { ref } from 'vue'
const emit = defineEmits(['update:dialogDeleteOrderVisible'])
const props = defineProps({
	dialogDeleteOrderVisible: {
		type: Boolean,
		default: false,
	},
})
const handleDeleteOrderClose = () => {
	emit('update:dialogDeleteOrderVisible', false)
}
</script>
<style lang="scss" scoped>
:deep(.el-dialog) {
	padding: 0;
	display: flex;
	flex-direction: column;

	.el-dialog__header {
		display: none;
	}
	.el-dialog__body {
		flex: 1;
		padding: 0 !important;
	}
	.chat-close {
		background: linear-gradient(91.29deg, rgba(122, 175, 255, .1) 1.1%, rgba(122, 135, 255, .1) 95.36%);
        &::before{
			content: '';
			position: absolute;
			left: 0;
			right: 0;
			top: 0;
			bottom: 0;
			margin: auto;
			width: 86px;
			height: 86px;
			border-radius: 50%;
            background: linear-gradient(91.29deg, rgba(122, 175, 255, .2) 1.1%, rgba(122, 135, 255, .2) 95.36%);
        }
	}
	.order-btn {
		&:hover {
			box-shadow: 0px 0px 10px 0px rgba(122, 135, 255, 0.5);
		}
	}
	.check-detail {
		background: linear-gradient(91.29deg, #7aafff 1.1%, #7a87ff 95.36%);
	}
}
</style>
