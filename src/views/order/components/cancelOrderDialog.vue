<!-- 连接顾问弹窗 -->
<template>
    <div class="">
        <el-dialog :model-value="dialogCancelOrderVisible"
            @update:model-value="$emit('update:dialogCancelOrderVisible', $event)" :close-on-click-modal="false"
            :modal="false" :before-close="handleCancelOrderClose" :show-close="false"
            class="flex flex-col h-max w-[345px] absolute mb-[0] left-[50%] top-[50%] mt-[0px] translate-y-[-40%] translate-x-[-50%] rounded-[18px] md:w-[500px] lg:w-[500px] p-0">
            <div class="online-order-content flex flex-col items-center py-[30px] px-[16px] lg:pt-[30px] lg:px-[40px]">
                <div class="close-btn absolute top-[10px] left-[10px] cursor-pointer lg:top-[20px] lg:left-[20px]"
                    @click="handleCancelOrderClose">
                    <img src="@/assets/order/close.png" alt="" class="w-[20px] h-[20px] lg:w-[24px] lg:h-[24px]" />
                </div>
                <!--title-->
                <div class="flex items-center">
                    <img src="@/assets/order/cancel-order.png" alt="" class="w-[20px] h-[20px] mr-1" />
                    <div class="font-bold  font-['Philosopher'] text-[20px] text-[#1c2158] lg:text-[22px]">Cancel Order
                    </div>
                </div>
                <!--content-->
                <div class="flex flex-col w-full items-center mt-[15px] lg:mt-[22px]">
                    <div class="relative flex justify-center">
                        <img :src="augurDetail.avatar" alt=""
                            class="w-[50px] h-[50px] rounded-[4px] lg:w-[70px] lg:h-[70px]" />
                        <div class="online absolute top-[3px] right-[3px] w-[8px] h-[8px] rounded-[50%] bg-[#3AD953] border-1 border-[#fff] border-solid"
                            v-if="augurDetail.state == 4 || augurDetail.state == 2"></div>
                        <div class="offline absolute top-[3px] right-[3px] w-[8px] h-[8px] rounded-[50%] bg-[#BFC3C5] border-1 border-[#fff] border-solid"
                            v-else-if="augurDetail.state == 1"></div>
                        <div class="busy absolute top-[3px] right-[3px] w-[8px] h-[8px] rounded-[50%] bg-[#EA3232] border-1 border-[#fff] border-solid"
                            v-else-if="augurDetail.state == 3"></div>

                    </div>
                    <div
                        class="font-['Philosopher'] text-[#1C2158] text-[16px] font-bold mt-[6px] leading-[25px] lg:text-[22px] lg:mt-[15px] lg:leading-[28px]">
                        {{ augurDetail.username }}</div>
                    <!--当有这一条信息以及不是lg 时显示这个-->
                    <div class="message-tip relative text-[#1C2158] text-[14px] leading-[18px] mt-[6px] px-[12px] py-[6px] rounded-[4px]"
                        v-if="rejectMessage">
                        {{ rejectMessage }}</div>
                    <div class="mt-[19px] text-[#1C2158] text-[12px] leading-[15px] ">Not Successful connected？ Maybe
                        you can</div>
                    <!--按钮-->
                    <div class="flex gap-[15px] w-full mt-[15px]">
                        <div class="flex-1 order-btn py-[13px] rounded-[6px] border border-[#4484FF] border-solid text-[#4484FF] font-['Philosopher'] text-[16px] leading-[18px] text-center cursor-pointer"
                            @click="reorder">
                            Reorder</div>
                        <div class="flex-1 order-btn check-detail py-[13px] rounded-[6px] text-white font-['Philosopher'] text-[16px] leading-[18px] text-center cursor-pointer"
                            @click="checkOrderDetail">
                            Check Details</div>
                    </div>
                    <!--顾问line-->
                    <img src="@/assets/common/lg-line.png" alt="" class="w-full h-[15px] mt-[21px] lg:h-[20px]" />
                    <p class="text-[12px] font-['Source_Sans_3'] text-[#1C2158] font-normal mt-[20px] text-center">Or
                        try recommended advisor</p>
                    <!--顾问详细信息-->
                    <div
                        class="advisor-content-info w-full rounded-[10px] p-[10px] mt-[13px] flex items-center justify-between lg:p-[20px]">
                        <div class="advisor-content-info-left flex items-center flex-1 overflow-hidden">
                            <div class="advisor-content-info-left-avatar relative shrink-0">
                                <img :src="recommendAugur.avatar"
                                    class="w-[50px] h-[50px] rounded-[4px] lg:w-[70px] lg:h-[70px]" />
                                    <div class="online absolute top-[3px] right-[3px] w-[8px] h-[8px] rounded-[50%] bg-[#3AD953] border-1 border-[#fff] border-solid"
                            v-if="recommendAugur.state == 4 || recommendAugur.state == 2"></div>
                        <div class="offline absolute top-[3px] right-[3px] w-[8px] h-[8px] rounded-[50%] bg-[#BFC3C5] border-1 border-[#fff] border-solid"
                            v-else-if="recommendAugur.state == 1"></div>
                        <div class="busy absolute top-[3px] right-[3px] w-[8px] h-[8px] rounded-[50%] bg-[#EA3232] border-1 border-[#fff] border-solid"
                            v-else-if="recommendAugur.state == 3"></div>
                            </div>
                            <div class="advisor-content-info-left-name flex-1  ml-3 flex flex-col justify-between lg:h-[70px]">
                                <span class="text-[16px] font-['Philosopher'] text-[#1C2158] font-bold">{{
                                    recommendAugur.username }}</span>
                                <span
                                    class="text-[12px] font-['Source_Sans_3'] text-[#1C2158] font-normal  opacity-50 mt-[2px] line-clamp-1">{{
                                        recommendAugur.about }}</span>
                                <div class="price mt-[2px] flex">
                                    <span
                                        class="font-['Open_Sans_3'] font-bold text-[12px] text-[#F53A3A] lg:text-[16px]">{{
                                            augurFortune.discountCoins
                                                < augurFortune.coins ? augurFortune.discountCoins : augurFortune.coins }}</span>
                                            <img src="@/assets/common/money.png" alt=""
                                                class="w-[12px] h-[12px] ml-[2px] lg:w-[18px] lg:h-[18px]" />
                                </div>
                            </div>
                        </div>
                        <div class="advisor-content-info-right ml-[12px] cursor-pointer shrink-0">
                            <div
                                class="advisor-content-info-right-connect px-[4px] py-[8px] rounded-[4px] text-[#fff] text-[12px] font-['Philosopher'] font-bold lg:px-[13px] lg:py-[11px] lg:text-[16px]">
                                Connect Now</div>
                        </div>
                    </div>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script lang="ts" setup name="CancelOrderDialog">
import { ref, watch, onMounted } from 'vue'
const emit = defineEmits(['update:dialogCancelOrderVisible', 'reorder'])
import { recommendAugurInOrder } from '@/api/augur'
import {useRouter} from 'vue-router'
const router = useRouter()
const currentTime = ref('00:00')
const recommendAugur = ref({
    username: '',
    introduction: '',
    avatar: '',
    about: '',
    augurFortune: [],
    state:4
})
const augurFortune = ref<any>({})
const props = defineProps({
    dialogCancelOrderVisible: {
        type: Boolean,
        default: false,
    },
    augurDetail: {
        type: Object,
        default: () => ({

        })
    },
    rejectMessage: {
        type: String,
        default: '',
    },
    augurFortuneGroupId: {
        type: String,
        default: '',
    },
    orderNo: {
        type: String,
        default: ''
    }
})
watch(() => props.dialogCancelOrderVisible, (newVal) => {
    if (newVal) {
        getRemmendAugur()
    }
})

const getRemmendAugur = () => {
    recommendAugurInOrder({
        augurFortuneGroupId: props.augurFortuneGroupId
    }).then(response => {
        recommendAugur.value = response.data[0]
        augurFortune.value = recommendAugur.value.augurFortune.filter(item => item.augurFortuneGroupId == props.augurFortuneGroupId)[0]
    })
}
const handleCancelOrderClose = () => {
    emit('update:dialogCancelOrderVisible', false)
}
const reorder = () => {
    emit('reorder')
}
const checkOrderDetail = () => {
    router.push(`/orderDetail/${props.orderNo}`)
}
</script>
<style lang="scss" scoped>
:deep(.el-dialog) {
    padding: 0;
    display: flex;
    flex-direction: column;

    .el-dialog__header {
        display: none;
    }

    .el-dialog__body {
        flex: 1;
        padding: 0 !important;

        .message-tip {
            background: linear-gradient(91.29deg, rgba(122, 175, 255, 0.25) 1.1%, rgba(122, 175, 255, 0.25) 95.36%);

            &::before {
                content: '';
                position: absolute;
                width: 0;
                height: 0;
                border-left: 10px solid transparent;
                border-bottom: 10px solid #e4ebff;
                border-right: 10px solid transparent;
                top: -3px;
                left: 50%;
                transform: translateY(-50%);
            }
        }
    }

    .advisor-content-info {
        background: linear-gradient(269.4deg, #efefff 2.43%, #fff8f4 99.62%);
    }

    .advisor-content-info-right-connect {
        background: linear-gradient(91.29deg, #7aafff 1.1%, #7a87ff 95.36%);
        white-space: nowrap;
    }

    .order-btn {
        &:hover {
            box-shadow: 0px 0px 10px 0px rgba(122, 135, 255, 0.5);
        }
    }

    .check-detail {
        background: linear-gradient(91.29deg, #7aafff 1.1%, #7a87ff 95.36%);
    }
}
</style>
