<!-- 支付弹窗 -->
<template>
	<div class="">
		<el-drawer :model-value="dialogVisible"  direction="btt" :before-close="handleClose" size="90%" :show-close="false">
			<div class="purchase-container-right-top p-[20px] bg-white rounded-[10px]">
				<div class="font-['Open_Sans_3'] font-[700] text-[19px] leading-[26px] text-[#1C2158] pb-[16px] border-b-[1px] border-[#EDEDF2] border-solid">Summary</div>
				<div class="py-[16px] flex flex-col gap-[16px] border-b-[1px] border-[#EDEDF2] border-solid">
					<div class="flex items-center justify-between opacity-50">
						<span class="font-['Open_Sans_3'] font-[400] text-[15px] leading-[20px] text-[#1C2158B2]">Balance Starmet</span>
						<span class="font-['Open_Sans_3'] font-[400] text-[15px] leading-[20px] text-[#1C2158B2]">$4.99</span>
					</div>
					<div class="flex items-center justify-between opacity-50">
						<span class="font-['Open_Sans_3'] font-[400] text-[15px] leading-[20px] text-[#1C2158B2]">Free Bonus Balance</span>
						<span class="font-['Open_Sans_3'] font-[400] text-[15px] leading-[20px] text-[#1C2158B2]">$4.99</span>
					</div>
				</div>
				<!--Toatl-->
				<div class="flex justify-between pt-[16px]">
					<div class="font-['Open_Sans_3'] font-semibold text-[15px] leading-[20px] text-[#1C2158]">Total Balance</div>
					<div class="font-['Open_Sans_3'] font-semibold text-[15px] leading-[20px] text-[#1C2158]">
						<div>$4.99</div>
						<!--折扣-->
						<div class="text-[#FF861C] font-['Open_Sans_3'] font-[400] text-[15px] leading-[20px] mt-2">12%Off</div>
					</div>
				</div>
				<!--YOU Pay-->
				<div class="flex justify-between pt-[16px]">
					<div class="font-['Open_Sans_3'] font-semibold text-[15px] leading-[20px] text-[#1C2158]">You Pay</div>
					<div class="font-['Open_Sans_3'] font-semibold text-[15px] leading-[20px] text-[#1C2158]">US$3.99</div>
				</div>
			</div>
			<!----支付方式-->
			<div class="purcahse-channel-list mt-5" >
				<div class="purcahse-channel-list-item cursor-pointer flex items-center justify-between mt-3 px-[20px] py-[10px] rounded-[10px] bg-white" v-for="item in 5" :key="item">
					<div class="purcahse-channel-list-item-left flex items-center">
						<img src="@/assets/cardPurchase/pay.png" alt="" class="w-[64px] h-[40px] bg-white object-cover" />
						<div class="purcahse-channel-list-item-left-title ml-[14px] font-['Open_Sans_3'] font-bold text-[15px] leading-[20px] text-[#1C2158]">
							<span>LinkAja</span>
							<div class="flex items-center mt-1 text-[#FF7C44]">
								<span class="text-[14px] font-bold leading-[20px]">+300</span>
								<span class="text-[13px]">Free Balance</span>
							</div>
						</div>
					</div>
					<div class="purcahse-channel-list-item-right">
						<img src="@/assets/cardPurchase/arrow-right.png" alt="" class="w-[20px] h-[20px]" />
					</div>
				</div>
			</div>
		</el-drawer>
	</div>
</template>

<script lang="ts" setup name="cardPurchaseDialog">
import { ref } from 'vue'
const { dialogVisible } = defineProps<{
	dialogVisible: boolean
}>()

const emit = defineEmits(['update:dialogVisible'])
const handleClose = () => {
	emit('update:dialogVisible', false)
}
</script>
<style lang="scss" scoped>
:deep(.el-drawer){
    background: #F8F8FA;
    box-shadow: 0px 0px 4px 0px #BAC5FF4F;
    border-top-left-radius: 20px;
    border-top-right-radius: 20px;
    .el-drawer__header{
        display: none;
    }
}
</style>
