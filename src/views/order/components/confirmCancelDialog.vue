<!-- 取消订单二次确认弹窗 -->
<template>
	<div class="">
		<el-dialog :model-value="dialogCancelConfirmVisible" @update:model-value="$emit('update:dialogCancelConfirmVisible', $event)" :close-on-click-modal="false" :before-close="handleCancelConfirmClose" :show-close="false" class="flex flex-col h-max w-[340px] absolute mb-[0] left-[50%] top-[50%] mt-[0px] translate-y-[-40%] translate-x-[-50%] rounded-[18px] md:w-[400px] lg:w-[480px] p-0">
			<div class="py-[30px] px-[16px] w-full flex flex-col items-center lg:p-[40px]">
				<div class="font-bold  font-['Philosopher'] text-[20px] text-center text-[#1c2158] lg:text-[22px]">Sure to pass up this opportunity for counseling?</div>
				<!--按钮-->
				<div class="flex gap-[13px] w-full mt-[21px] lg:gap-[30px]">
					<div class="flex-1 order-btn py-[13px] rounded-[6px] border border-[#4484FF] border-solid text-[#4484FF] font-['Philosopher'] text-[16px] leading-[18px] text-center cursor-pointer lg:leading-[22px] lg:text-[20px]" @click="handleCancelConfirm">Cancel</div>
					<div class="flex-1 order-btn check-detail py-[13px] rounded-[6px] text-white font-['Philosopher'] text-[16px] leading-[18px] text-center cursor-pointer lg:leading-[22px] lg:text-[20px]" @click="handleCancelConfirmClose">Wait it</div>
				</div>
			</div>
		</el-dialog>
	</div>
</template>

<script lang="ts" setup name="ConfirmCancelDialog">
import { ref } from 'vue'
const emit = defineEmits(['update:dialogCancelConfirmVisible','handlerCancelOrder'])
const props = defineProps({
	dialogCancelConfirmVisible: {
		type: Boolean,
		default: false,
	},
})
const handleCancelConfirmClose = () => {
	emit('update:dialogCancelConfirmVisible', false)
}

const handleCancelConfirm = () => {
	emit('handlerCancelOrder', false)
}
</script>
<style lang="scss" scoped>
:deep(.el-dialog) {
	padding: 0;
	display: flex;
	flex-direction: column;

	.el-dialog__header {
		display: none;
	}
	.el-dialog__body {
		flex: 1;
		padding: 0 !important;
	}
	.order-btn {
		&:hover {
			box-shadow: 0px 0px 10px 0px rgba(122, 135, 255, 0.5);
		}
	}
	.check-detail {
		background: linear-gradient(91.29deg, #7aafff 1.1%, #7a87ff 95.36%);
	}
}
</style>
