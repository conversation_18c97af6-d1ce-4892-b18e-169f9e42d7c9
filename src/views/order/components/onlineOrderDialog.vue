<!-- 连接顾问弹窗 -->
<template>
    <div class="">
        <el-dialog :model-value="dialogOnlineOrderVisible"
            @update:model-value="$emit('update:dialogOnlineOrderVisible', $event)" :modal="false"
            :before-close="handleOnlineOrderClose" :close-on-click-modal="false" :show-close="false"
            class="flex flex-col h-max w-[345px] absolute mb-[0] bottom-[20px] left-[50%] translate-x-[-50%] rounded-[18px] md:w-[500px] md:top-[50%] md:bottom-0 md:mt-[0px] md:translate-y-[-50%] lg:w-[700px] lg:bottom-0 md:lg-[0px] lg:translate-y-[-50%] p-0">
            <div class="online-order-content pt-[22px] lg:pt-[43px]">
                <div
                    class="online-order-content-title font-['Philosopher'] text-[#1C2158] text-center text-[16px] font-bold px-[32px] lg:text-[24px] lg:leading-[27px]">
                    We're informing {{ augurDetail.username }} to enter the chat room.</div>
                <div class="online-order-content-other mt-[12px] flex flex-col items-center lg:mt-[42px]">
                    <div class="spinning-circle w-[108px] h-[108px] rounded-[50%] flex items-center justify-center lg:w-[200px] lg:h-[200px]"
                        :class="{ paused: spinning === 'pause' }">
                        <img :src="augurDetail.avatar" alt=""
                            class="relative w-[48px] h-[48px] rounded-[50%] lg:w-[90px] lg:h-[90px]" />
                    </div>
                    <div
                        class="spinning-time mt-1 font-['Open_Sans'] text-[18px] text-[#1C2158] leading-[25px] lg:mt-[20px] lg:leading-[38px] lg:text-[28px]">
                        {{ currentTime }}
                    </div>
                    <div
                        class="spinning-text mt-[6px] font-['Source_Sans_Pro'] text-[12px] text-[#1C2158] leading-[15px] opacity-50 lg:text-[22px] lg:mt-[12px] lg:leading-28px]">
                        Your waiting time is usually up to 90s</div>
                    <div class="cancel-order-btn mt-[9px] font-['Open_Sans'] text-[14px] text-[#4484FF] leading-[19px] lg:mt-[38px] cursor-pointer lg:leading-[28px] lg:text-[22px]"
                        @click="handleCancelConfirmVisible(true)">Cancel Order</div>
                </div>
                <!--tips-->
                <div
                    class="tips px-[18px] py-[11px] rounded-b-[18px] mt-[13px] flex items-center lg:mt-[28px] lg:py-[15px] lg:px-[20px]">
                    <img src="@/assets/order/tip.png" alt="" class="w-[20px] h-[20px] lg:w-[37px] lg:h-[37px]" />
                    <div class="ml-[8px] text-[12px] text-[#4484FF] leading-[15px] lg:text-[16px] lg:leading-[20px]">
                        Stay on the current page until connected successfully,ol the session may not start normally.
                    </div>
                </div>
            </div>
        </el-dialog>
        <ConfirmCancelDialog :dialog-cancel-confirm-visible="dialogCancelConfirmVisible"
            @update:dialogCancelConfirmVisible="handleCancelConfirmVisible" @handlerCancelOrder="handlerCancelOrder">
        </ConfirmCancelDialog>
    </div>
</template>

<script lang="ts" setup name="OnlineOrderDialog">
import { ref, onMounted,watch } from 'vue'
import ConfirmCancelDialog from './confirmCancelDialog.vue'
const emit = defineEmits(['update:dialogOnlineOrderVisible','handlerCancelOrder'])
const currentTime = ref('00:00')

const dialogCancelConfirmVisible = ref(false)

const props = defineProps({
    dialogOnlineOrderVisible: {
        type: Boolean,
        default: false,
    },
    augurDetail: {
        type: Object,
        default: () => {
            return {}
        }
    }
})

const spinning = ref('play')
let timer: number | null | undefined = null
const getCurrentTime = () => {
    let duration = 90
    timer = window.setInterval(() => {
        const minute = Math.floor(duration / 60) < 10 ? '0' + Math.floor(duration / 60) : Math.floor(duration / 60)
        const second = duration % 60 < 10 ? '0' + (duration % 60) : duration % 60
        currentTime.value = `${minute}:${second}`
        duration--
        if (duration < 0) {
            window.clearInterval(timer!)
            spinning.value = 'pause'
        }
    }, 1000)
}
const handleOnlineOrderClose = () => {
    emit('update:dialogOnlineOrderVisible', false)
}

const handleCancelConfirmVisible = (visible: boolean) => {
    dialogCancelConfirmVisible.value = visible
}

const handlerCancelOrder = () => {
    emit('handlerCancelOrder', '1')
}
watch(()=>props.dialogOnlineOrderVisible,(newVal)=>{
    if(newVal){
        currentTime.value = "01:30"
        getCurrentTime()
    }else{
        if(timer){
            window.clearTimeout(timer)
            timer  = null
        }
    }
})
// onMounted(() => {
//     getCurrentTime()
// })
</script>
<style lang="scss" scoped>
:deep(.el-dialog) {
    padding: 0;
    display: flex;
    flex-direction: column;

    .el-dialog__header {
        display: none;
    }

    .el-dialog__body {
        flex: 1;
        padding: 0 !important;

        .spinning-circle {
            background: url('@/assets/order/time-2.png') no-repeat center center;
            background-size: 100% 100%;
            position: relative;

            &::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: url('@/assets/order/time-1.png') no-repeat center center;
                background-size: 100% 100%;
                animation: spinning 1.5s linear infinite;
            }

            &.paused {
                &::before {
                    animation-play-state: paused;
                }
            }
        }
    }

    @keyframes spinning {
        0% {
            transform: rotate(0deg);
        }

        100% {
            transform: rotate(360deg);
        }
    }

    .tips {
        background: linear-gradient(91.29deg, rgba(122, 175, 255, 0.1) 1.1%, rgba(122, 135, 255, 0.1) 95.36%);
    }
}
</style>
