<!-- 取消订单二次确认弹窗 -->
<template>
    <div class="">
        <el-dialog :model-value="dialogEvaluateOrderVisible"
            @update:model-value="$emit('update:dialogEvaluateOrderVisible', $event)" :close-on-click-modal="false"
            :before-close="handleEvaluateOrderClose" :show-close="false"
            class="flex flex-col h-max w-[340px] absolute mb-[0] left-[50%] top-[50%] mt-[0px] translate-y-[-40%] translate-x-[-50%] rounded-[18px] md:w-[400px] lg:w-[500px] p-0">
            <div class="relative py-[30px] px-[16px] w-full flex flex-col items-center lg:p-[40px]">
                <div class="close-btn absolute top-[10px] left-[10px] cursor-pointer lg:top-[20px] lg:left-[20px]"
                    @click="handleEvaluateOrderClose">
                    <img src="@/assets/order/close.png" alt="" class="w-[20px] h-[20px] lg:w-[24px] lg:h-[24px]" />
                </div>
                <div class="font-bold text-[#1C2158] font-['Philosopher'] text-[20px] text-center lg:text-[22px]">Reivew
                </div>
                <div
                    class="advisor-content-info w-full rounded-[10px] p-[10px] mt-[13px] flex items-center justify-between lg:p-[20px]">
                    <div class="advisor-content-info-left flex items-center">
                        <div class="advisor-content-info-left-avatar shrink-0 relative">
                            <img :src="augurDetail.avatar" alt=""
                                class="w-[50px] h-[50px] rounded-[4px] lg:w-[60px] lg:h-[60px]" />
                            <div class="online absolute top-[3px] right-[3px] w-[8px] h-[8px] rounded-[50%] bg-[#3AD953] border-1 border-[#fff] border-solid"
                                v-if="augurDetail.state == 4 || augurDetail.state == 2"></div>
                            <div class="offline absolute top-[3px] right-[3px] w-[8px] h-[8px] rounded-[50%] bg-[#BFC3C5] border-1 border-[#fff] border-solid"
                                v-else-if="augurDetail.state == 1"></div>
                            <div class="busy absolute top-[3px] right-[3px] w-[8px] h-[8px] rounded-[50%] bg-[#EA3232] border-1 border-[#fff] border-solid"
                                v-else-if="augurDetail.state == 3"></div>
                        </div>
                        <div class="advisor-content-info-left-name ml-3 flex flex-col justify-center">
                            <span class="text-[16px] font-['Philosopher'] text-[#1C2158] font-bold">{{
                                augurDetail.username }}</span>
                            <span
                                class="text-[12px] font-['Source_Sans_3'] text-[#1C2158] font-normal leading-[15px] line-clamp-1 opacity-50 mt-[2px] lg:mt-[10px]">{{
                                    augurDetail.about }}</span>
                        </div>
                    </div>
                </div>
                <!-- order详情 -->
                <div class="order-info w-full">
                    <div
                        class="order-info-item flex items-center justify-between py-[12px] border-b border-[#1C215814] border-solid">
                        <div
                            class="order-info-item-title font-['Open_Sans'] text-[14px] text-[#1C2158] font-semibold leading-[19px]">
                            Duration</div>
                        <div class="flex items-center">
                            <span class="font-['Open_Sans'] text-[14px] text-[#1C2158] font-semibold leading-[19px]">{{
                                returnOrderTime(orderDetail.endTimestamp - orderDetail.startTimestamp) }}</span>
                            <img src="@/assets/order/order-time.png" alt="" class="w-4 h-4 ml-[3px] mb-[2px]" />
                        </div>
                    </div>
                    <div
                        class="order-info-item flex items-center justify-between py-[12px] border-b border-[#1C215814] border-solid">
                        <div
                            class="order-info-item-title font-['Open_Sans'] text-[14px] text-[#1C2158] font-semibold leading-[19px]">
                            Expense</div>
                        <span class="card flex items-center shrink-0" v-if="orderDetail.m10CalllCardNumber">
                            <span class="line w-[1px] h-[14px] bg-[#E8E8E8] mx-[10px]"></span>
                            <span class="flex items-center">
                                <span class="text-[#FF7C44] text-[14px] leading-[18px]">{{
                                    orderDetail.m10CalllCardNumber }}x</span>
                                <img src="@/assets/order/10MCalllCard.png" class="w-[38px] h-[20px] ml-[4px]">
                            </span>
                        </span>
                        <span class="card flex items-center shrink-0" v-if="orderDetail.callCardNumber">
                            <span class="line w-[1px] h-[14px] bg-[#E8E8E8] mx-[10px]"></span>
                            <span class="flex items-center">
                                <span class="text-[#FF7C44] text-[14px] leading-[18px]">{{ orderDetail.callCardNumber
                                }}x</span>
                                <img src="@/assets/order/callCard.png" class="w-[38px] h-[20px] ml-[4px]">
                            </span>
                        </span>
                        <span class="card flex items-center shrink-0" v-if="orderDetail.experienceCardNumber">
                            <span class="line w-[1px] h-[14px] bg-[#E8E8E8] mx-[10px]"></span>
                            <span class="flex items-center">
                                <span class="text-[#FF7C44] text-[14px] leading-[18px]">{{
                                    orderDetail.experienceCardNumber }}x</span>
                                <img src="@/assets/order/experienceCard.png" class="w-[38px] h-[20px] ml-[4px]">
                            </span>
                        </span>
                        <span class="card flex items-center shrink-0" v-if="orderDetail.firstOrderCardNumber">
                            <span class="line w-[1px] h-[14px] bg-[#E8E8E8] mx-[10px]"></span>
                            <span class="flex items-center">
                                <!-- <img src="@/assets/order/firstOrderCard.png" class="w-[38px] h-[20px] ml-[4px]"> -->
                                <span class="text-[#FF4444] text-[14px] leading-[18px] ml-[14px]">(30% Off)</span>
                            </span>
                        </span>
                        <span class="ml-1 text-[14px] font-semibold text-[#FF7C44]">{{ orderDetail.discountCoins
                            }}</span>
                    </div>
                    <div
                        class="order-info-item flex items-center justify-between py-[12px] border-b border-[#1C215814] border-solid">
                        <div
                            class="order-info-item-title font-['Open_Sans'] text-[14px] text-[#1C2158] font-semibold leading-[19px]">
                            Rating</div>
                        <div class="flex items-center">
                            <sm-rate v-model="rateValue" size="small" :showSmall="true" :textColor="'#FF7527'" />
                        </div>
                    </div>
                    <!---当评分小余3时显示-->
                    <!-- <div class="order-info-item py-[12px] border-b border-[#1C215814] border-solid"
                        v-if="rateValue <= 3">
                        <div class="font-['Open_Sans'] text-[14px] text-[#1C2158] font-semibold leading-[19px]">It's
                            kind of you to select a tag and write down the reason for your dissatisfaction.</div>
                        <div class="flex flex-wrap gap-[2px]">
                            <div class="text-[14px] text-[#1C2158] leading-[18px] p-2 rounded-[4px] border border-[#1C215814] border-dashed mt-[7px] cursor-pointer"
                                v-for="item in 5">No Clarification</div>
                        </div>
                    </div> -->
                    <!--评价-->
                    <div class="order-info-item py-[12px]">
                        <div
                            class="order-info-item-title flex items-center justify-between font-['Open_Sans'] text-[14px] text-[#1C2158] font-semibold leading-[19px]">
                            <span>Write Review</span>
                            <span class="text-normal-opacity-50 text-[12px] leading-[15px]">{{ comment.length
                                }}/300</span>
                        </div>
                        <div class="order-info-item-content mt-[10px]">
                            <textarea name="" id="" v-model="comment" :maxlength="300"
                                class="w-full outline-none bg-[#F8F8FA] font-[14px] h-[90px] border border-[#1C215814] border-solid rounded-[6px] p-[10px]"
                                placeholder="Share your feeling about the session. Your comment will be shown on public after reviewing."></textarea>
                        </div>
                    </div>
                    <div>
                        <el-checkbox label="Hide my username" v-model="hideUsername" true-value="1" false-value="0" />
                    </div>
                    <div class="submit-button mt-4 w-full rounded-[6px]  text-[#fff] text-[16px] py-[12px] leading-[22px] font-['Philosopher'] font-bold text-center"
                        @click="handleConfirm">
                        Submit
                    </div>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script lang="ts" setup name="EvaluateOrderDialog">
import { ref } from 'vue'
import { commentOrder } from '@/api/order'
import smRate from '@/components/smRate/smRate.vue'
const emit = defineEmits(['update:dialogEvaluateOrderVisible', 'confirm'])
const comment = ref('')
const rateValue = ref(5)

const hideUsername = ref("0")
const props = defineProps({
    dialogEvaluateOrderVisible: {
        type: Boolean,
        default: false,
    },
    augurDetail: {
        type: Object,
        default: () => {
            return {}
        }
    },
    orderDetail: {
        type: Object,
        default: () => {
            return {}
        }
    }
})
const handleEvaluateOrderClose = () => {
    emit('update:dialogEvaluateOrderVisible', false)
}
const handleConfirm = () => {
    commentOrder({
        orderNo: props.orderDetail.orderNo,
        grade: rateValue.value,
        comment: comment.value,
        hideUsername: hideUsername.value
    }).then(response => {
        emit('confirm')
    })

}

const returnOrderTime = (time: number) => {
    const hours = Math.floor(time / 60) < 10 ? '0' + Math.floor(time / 60) : Math.floor(time / 60)
    const minutes = time % 60 < 10 ? '0' + time % 60 : time % 60
    return `${hours}:${minutes}`
}
</script>
<style lang="scss" scoped>
:deep(.el-dialog) {
    padding: 0;
    display: flex;
    flex-direction: column;

    .el-dialog__header {
        display: none;
    }

    .el-dialog__body {
        flex: 1;
        padding: 0 !important;
    }

    .chat-close {
        background: linear-gradient(91.29deg, rgba(122, 175, 255, 0.1) 1.1%, rgba(122, 135, 255, 0.1) 95.36%);

        &::before {
            content: '';
            position: absolute;
            left: 0;
            right: 0;
            top: 0;
            bottom: 0;
            margin: auto;
            width: 86px;
            height: 86px;
            border-radius: 50%;
            background: linear-gradient(91.29deg, rgba(122, 175, 255, 0.2) 1.1%, rgba(122, 135, 255, 0.2) 95.36%);
        }
    }

    .order-btn,
    .submit-button {
        cursor: pointer;

        &:hover {
            box-shadow: 0px 0px 10px 0px rgba(122, 135, 255, 0.5);
        }
    }

    .submit-button {
        background: linear-gradient(91.29deg, #7aafff 1.1%, #7a87ff 95.36%);
    }

    .advisor-content-info {
        background: linear-gradient(269.4deg, #efefff 2.43%, #fff8f4 99.62%);
    }

    .el-checkbox__label {
        color: #1c2158 !important;
    }
}
</style>
