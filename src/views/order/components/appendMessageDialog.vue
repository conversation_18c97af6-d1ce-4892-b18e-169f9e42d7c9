<!-- 追加留言弹窗 -->
<template>
	<div class="">
		<!-- 追加留言 -->
		<el-dialog :model-value="dialogAppendMessageVisible" :close-on-click-modal="false" :before-close="handleAppendMessageClose" :show-close="false" class="flex flex-col h-max w-[340px] absolute mb-[0] left-[50%] top-[50%] mt-[0px] translate-y-[-40%] translate-x-[-50%] rounded-[18px] md:w-[400px] lg:w-[480px] p-0">
			<div class="py-[45px] px-[40px] w-full flex flex-col items-center">
				<div class="font-bold text-[#000000] font-['Philosopher'] text-[20px] text-center text-[#1c2158] lg:text-[22px]">Add</div>
				<el-input type="textarea" name="" id="" class="h-[140px] outline-none w-full bg-[#F8F8FA] rounded-[8px] mt-[10px]" :maxlength="300" :show-word-limit="true" v-model="appendMessage"></el-input>

				<div class="flex gap-[13px] w-full mt-[21px] lg:gap-[30px]">
					<div class="flex-1 order-btn py-[13px] rounded-[6px] border border-[#4484FF] border-solid text-[#4484FF] font-['Philosopher'] text-[16px] leading-[18px] text-center cursor-pointer lg:leading-[22px] lg:text-[20px]">Cancel</div>
					<div class="flex-1 order-btn check-detail py-[13px] rounded-[6px] text-white font-['Philosopher'] text-[16px] leading-[18px] text-center cursor-pointer lg:leading-[22px] lg:text-[20px]" @click="handleCancelConfirmClose">Confirm</div>
				</div>
			</div>
		</el-dialog>
		<!--结束按钮-->
		<el-dialog :model-value="dialogCancelConfirmVisible" :close-on-click-modal="false" :before-close="handleCancelConfirmClose" :show-close="false" class="flex flex-col h-max w-[340px] absolute mb-[0] left-[50%] top-[50%] mt-[0px] translate-y-[-40%] translate-x-[-50%] rounded-[18px] md:w-[400px] lg:w-[480px] p-0">
			<div class="py-[30px] px-[16px] w-full flex flex-col items-center lg:p-[40px]">
				<div class="font-bold text-[#000000] font-['Philosopher'] text-[20px] text-center text-[#1c2158] lg:text-[22px]">Sure to send?</div>
				<div class="text-[#1C2158] text-[16px] text-center mt-[13px] leading-[20px] lg:mt-[10px]">You can only append one message to this reading. Please note that advisor may not be able to respond to your message if she/he is busy. You can place an new order if you have other questions.</div>
				<!--按钮-->
				<div class="flex gap-[13px] w-full mt-[21px] lg:gap-[30px]">
					<div class="flex-1 order-btn py-[13px] rounded-[10px] border border-[#4484FF] border-solid text-[#4484FF] font-['Philosopher'] text-[16px] leading-[18px] text-center cursor-pointer lg:leading-[22px] lg:text-[20px] md:rounded-[6px]">Cancel</div>
					<div class="flex-1 order-btn check-detail py-[13px] rounded-[10px] text-white font-['Philosopher'] text-[16px] leading-[18px] text-center cursor-pointer lg:leading-[22px] lg:text-[20px] md:rounded-[6px]" @click="handleCancelConfirmClose">Sure</div>
				</div>
			</div>
		</el-dialog>
	</div>
</template>

<script lang="ts" setup name="AppendMessageDialog">
import { ref } from 'vue'
const dialogCancelConfirmVisible = ref(false)

const emit = defineEmits(['update:dialogAppendMessageVisible'])
const handleAppendMessageClose = () => {
	emit('update:dialogAppendMessageVisible', false)
}
const appendMessage = ref('')
const handleCancelConfirmClose = () => {
	dialogCancelConfirmVisible.value = false
}
const props = defineProps({
	dialogAppendMessageVisible: {
		type: Boolean,
		default: false,
	},
})
</script>
<style lang="scss" scoped>
:deep(.el-dialog) {
	padding: 0;
	display: flex;
	flex-direction: column;

	.el-dialog__header {
		display: none;
	}
	.el-dialog__body {
		flex: 1;
		padding: 0 !important;
		.el-textarea {
			.el-textarea__inner {
				height: 100%;
				background-color: transparent;
				border: none;
				outline: none;
				box-shadow: none;
				resize: none;
			}
		}
		.check-detail {
			background: linear-gradient(91.29deg, #7aafff 1.1%, #7a87ff 95.36%);
		}
	}
	.order-btn {
		&:hover {
			box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.2);
		}
	}
}
</style>
