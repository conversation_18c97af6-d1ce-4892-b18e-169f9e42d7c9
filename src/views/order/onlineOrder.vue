<!-- app-contenier -->
<template>
    <!-- 页面主容器，包含所有内容和弹窗 -->
    <div class="app-container" v-loading-mask="pageLoading" :style="{ '--bg-image': `url(${onlineOrderBg})` }">
        <!-- 主内容区，包含面包屑和聊天区 -->
        <div class="main-content md:px-[120px] lg:px-[320px]">
            <!-- 面包屑导航，显示当前页面路径 -->
            <div class="breadcrumb-container w-full h-[37px] px-[15px] white-nowrap">
                <el-breadcrumb :separator-icon="ArrowRight" class="font-['Open_Sans_3'] font-[600] text-[14px]">
                    <el-breadcrumb-item class="text-[#1C2158]">Home</el-breadcrumb-item>
                    <el-breadcrumb-item class="text-[#1C2158]">Family &Friends</el-breadcrumb-item>
                    <el-breadcrumb-item class="text-[#1C2158]">{{ augurDetail.username }}</el-breadcrumb-item>
                    <el-breadcrumb-item>Live Text Chat</el-breadcrumb-item>
                </el-breadcrumb>
            </div>

            <!--实时单未接通时展示占卜师信息-->
            <!-- <div class="chat-header h-[44px] shrink-0 pr-[15px] pl-[7px] flex items-center justify-between md:hidden">
        			<div class="chat-header-left flex items-center">
        				<div>
        					<img src="@/assets/order/arrow-right-white.png" class="w-[24px] h-[24px] mr-[14px] md:w-[30px] md:h-[30px] md:mr-[15px]" />
        				</div>
        				<div class="flex items-center">
        					<img src="@/assets/advisor.png" class="w-[28px] h-[28px] rounded-[50%] mr-[8px] md:w-[50px] md:h-[50px] md:mr-[15px]" />
        					<div class="">
        						<p class="font-['Philosopher'] text-[18px] text-white font-blod leading-[20px] md:text-[26px] md:leading-[28px]">AliceLnken</p>
        						<span class="leading-[15px] text-[12px] text-white opacity-80 md:leading-[23px] md:text-[18px]">00:20:23</span>
        					</div>
        				</div>
        			</div>
        			<div class="chat-header-right relative bg-white flex items-center pl-[12px] py-[4px] pr-[4px] rounded-[8px] md:px-[12px] md:py-[8px] md:rounded-[10px]">
        				<span class="font-['Philosopher'] text-[16px] leading-[18px] mr-[2px] text-[#7A87FF] md:text-[26px] md:leading-[28px]">65</span>
        				<img src="@/assets/common/money.png" class="w-[14px] h-[14px] mr-[4px] md:w-[25px] md:h-[25px] md:mr-[3px]" />
        				<img src="@/assets/common/arrow-left.png" class="w-[14px] h-[14px] md:w-[25px] md:h-[25px]" />
        			</div>
        		</div> -->

            <!----实时单接通中-->
            <div class="chat-container relative">
                <!-- 隐私警告提示，保护用户隐私时弹出 -->
                <Transition enter-active-class="transition duration-200 ease-out" enter-from-class="opacity-0 scale-95"
                    enter-to-class="opacity-100 scale-100" leave-active-class="transition duration-150 ease-in"
                    leave-from-class="opacity-100 scale-100" leave-to-class="opacity-0 scale-95">
                    <div v-if="showTip && orderStatus == 'success'"
                        class=" tip absolute w-full px-[15px] top-[50px] left-0 right-0 m-auto z-[1] md:top-[90px]"
                        v-show="showTip">
                        <div
                            class="w-full px-[20px] py-[11px] bg-[rgba(255,218,218,1)] rounded-[8px] lg:bg-[#FFDADA] flex items-center">
                            <img src="@/assets/chat/warning.png" class="w-[30px] h-[30px] mr-[16px]" />
                            <div
                                class="font-['Source_Sans_3'] text-[12px] leading-[14px] text-[#1C2158] md:text-[16px] md:leading-[22px] md:font-semibold">
                                To protect privacy from being disclosed please do not provide or exchange any personal
                                contact information with advisors.</div>
                            <img src="@/assets/chat/close.png"
                                class="absolute right-[19px] top-[4px] w-[14px] h-[14px] md:top-[6px] md:right-[21px]"
                                @click="showTip = false" />
                        </div>
                    </div>
                </Transition>
                <!-- 当聊天时间不够了，弹出来的 -->
                <Transition enter-active-class="transition duration-200 ease-out" enter-from-class="opacity-0 scale-95"
                    enter-to-class="opacity-100 scale-100" leave-active-class="transition duration-150 ease-in"
                    leave-from-class="opacity-100 scale-100" leave-to-class="opacity-0 scale-95">
                    <div v-if="showRemainingTime"
                        class="tip absolute w-full px-[15px] top-[50px] left-0 right-0 m-auto z-[1] md:top-[90px]"
                        v-show="showRemainingTime">
                        <div
                            class="remaining-time w-full px-[12px] py-[11px] rounded-[8px] bg-[#FFDADA] flex items-center justify-between lg:px-[20px] lg:py-[15px]">
                            <div class="flex items-center flex-1 mr-3">
                                <img src="@/assets/chat/remaining-warning.png" class="w-[30px] h-[30px] mr-[16px]" />
                                <div
                                    class="font-['Source_Sans_3'] text-[12px] leading-[14px] text-[#1C2158] md:text-[16px] md:leading-[22px]">
                                    Conversation is about to close after {{ remainingTime }}, buy credits for more time!
                                </div>
                            </div>
                            <div
                                class="flex items-center px-[6px] py-[10px] rounded-[8px] bg-white font-['Philosopher'] text-[12px] leading-[13px] text-[#4484FF] lg:text-[16px] lg:leading-[18px] lg:px-[14px] lg:py-[11px]">
                                Purchase Now</div>
                            <img src="@/assets/chat/close.png"
                                class="absolute right-[19px] top-[4px] w-[14px] h-[14px] md:top-[6px] md:right-[20px]"
                                @click="showTip = false" />
                        </div>
                    </div>
                </Transition>
                <!-- 当余额不足，弹出来的 -->
                <Transition enter-active-class="transition duration-200 ease-out" enter-from-class="opacity-0 scale-95"
                    enter-to-class="opacity-100 scale-100" leave-active-class="transition duration-150 ease-in"
                    leave-from-class="opacity-100 scale-100" leave-to-class="opacity-0 scale-95">
                    <div v-if="showInsufficient"
                        class="tip absolute w-full top-[44px] left-0 right-0 m-auto z-[1] md:top-[80px]">
                        <div
                            class="bg-[#E8F1FF] w-full px-[15px] py-[10px] flex items-center justify-between lg:px-[20px] opacity-95 lg:py-[10px]">
                            <div class="flex items-center flex-1 mr-3">
                                <div
                                    class="font-['Source_Sans_3'] text-[13px] leading-[16px] text-[#1C2158B2]  md:leading-[19px] md:font-semibold md:text-[14px]">
                                    Insufficient credits. Close the session after <span class="text-[#F53A3A]">{{
                                        remainTime }}</span> seconds.</div>
                            </div>
                            <div
                                class="buy-credits flex items-center px-[5px] py-[6px] rounded-[6px] font-['Open_Sans_3'] text-[13px] leading-[18px] text-white ml-[5px] lg:text-[16px] lg:leading-[18px] lg:px-[14px] lg:py-[11px]">
                                Buy Credits</div>
                        </div>
                    </div>
                </Transition>

                <div class="chat-header h-[44px] shrink-0 pr-[15px] pl-[7px] flex items-center justify-between md:bg-white md:h-[80px] md:pl-[15px]"
                    v-if="orderStatus == 'success'"
                    :class="{ 'bg-white ': orderStatus === 'success', 'md:hidden': orderStatus != 'success' }">
                    <!-- 头部内容 -->
                    <div class="chat-header-left flex items-center">
                        <div>

                            <img src="@/assets/order/arrow-right-black.png"
                                class="w-[24px] h-[24px] mr-[14px] md:w-[30px] md:h-[30px] md:mr-[15px] md:hidden"
                                @click="handleCloseChat" />
                            <img src="@/assets/order/message-close.png"
                                class="hidden w-[30px] h-[30px] mr-[15px] md:block" @click="handleCloseChat" />
                        </div>
                        <div class="flex items-center">
                            <img :src="augurDetail.avatar"
                                class="w-[28px] h-[28px] rounded-[50%] mr-[8px] md:w-[50px] md:h-[50px] md:mr-[15px]" />
                            <div class="">
                                <p class="font-['Philosopher'] text-[18px] font-blod leading-[20px] md:text-[26px] md:leading-[28px]"
                                    :class="{ 'text-[#1C2158]': orderStatus === 'success', 'text-[#fff]': orderStatus !== 'success' }">
                                    {{ augurDetail.username }}</p>
                                <span class="leading-[15px] text-[12px] opacity-80 md:leading-[23px] md:text-[18px]"
                                    :class="{ 'text-[#1C2158]': orderStatus === 'success', 'text-[#fff]': orderStatus !== 'success' }">
                                    {{ callDuration }}
                                </span>
                            </div>
                        </div>
                    </div>
                    <el-popover placement="top-start" trigger="click" :visible="popoverVisible" :teleported="false"
                        popper-class="recharge-popover">
                        <template #reference>
                            <div class="chat-header-right relative bg-white flex items-center pl-[12px] py-[4px] pr-[4px] rounded-[8px] cursor-pointer md:px-[12px] md:py-[8px] md:rounded-[10px]"
                                @click="changePopoverVisible">
                                <span
                                    class="font-['Philosopher'] text-[16px] leading-[18px] mr-[2px] text-[#7A87FF] md:text-[26px] md:leading-[28px]">{{
                                        userInfo.coins || 0 }}</span>
                                <img src="@/assets/common/money.png"
                                    class="w-[14px] h-[14px] mr-[4px] md:w-[25px] md:h-[25px] md:mr-[3px]" />
                                <img src="@/assets/common/arrow-left.png"
                                    class="w-[14px] h-[14px] md:w-[25px] md:h-[25px]" />
                            </div>
                        </template>

                        <template #default>
                            <div
                                class="recharge-popover-top text-[#1C2158] text-center font-['Source_Sans_3'] text-[13px] leading-[16px]">
                                <span>You have 5 minutes left to chat. Top up to add minutes.</span>
                            </div>
                            <div class="recharge-popover-list">
                                <div class="recharge-popover-list-item mt-[6px] px-[10px] py-[8px] bg-white rounded-[8px] flex items-center justify-between cursor-pointer"
                                    v-for="item in coinPackList" :key="item.price">
                                    <div class="recharge-popover-list-item-left flex items-center">
                                        <img src="@/assets/common/money.png" class="w-[42px] h-[42px] mr-2" />
                                        <span
                                            class="flex itmems-center font-['Open_Sans_3'] text-[19px] font-bold leading-[22px] text-[#1C2158]">{{ item.title}}
                                            <!-- <span class="text-[#1C2158] text-[13px] font-semibold"> Credits</span> -->
                                            </span>
                                    </div>
                                    <div
                                        class="recharge-popover-list-item-right px-[21px] py-[8px] leading-[23px] rounded-[10px]">
                                        <span
                                            class="font-['Open_Sans_3'] text-[17px] font-bold leading-[23px] text-white">${{ item.price }}</span>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </el-popover>
                </div>
                <div class="chat-box relative" v-if="orderStatus === 'success'" ref="messagesRef">
                    <!-- 消息列表渲染 -->
                    <div v-for="msg in currentMessages" :key="msg.ID" class="message-wrapper"
                        :class="[msg.flow === 'out' ? 'is-me' : 'is-other']">
                        <img class="avatar" :src="msg.flow === 'out' ? userInfo.avatar : augurDetail.avatar"
                            alt="avatar" />
                        <div class="content-row flex items-center">
                            <template v-if="msg.flow === 'out'">
                                <span v-if="msg.sendStatus === 'pending'"
                                    class="msg-status-icon mr-2 flex items-center">
                                    <el-icon :size="22">
                                        <Loading />
                                    </el-icon>
                                </span>
                                <span v-else-if="msg.sendStatus === 'fail'"
                                    class="msg-status-icon mr-2 flex items-center" @click="retrySend(msg)"
                                    style="cursor:pointer;">
                                    <el-icon color="red" :size="22">
                                        <WarningFilled />
                                    </el-icon>
                                </span>
                            </template>
                            <div class="content">
                                <div class="text-bubble" v-if="msg.type === 'TIMTextElem'">
                                    {{ msg.payload.text }}
                                </div>
                                <div class="image-bubble" v-else-if="msg.type === 'TIMImageElem'">
                                    <img :src="msg.payload.imageInfoArray[0]?.url"
                                        style="max-width: 180px; border-radius: 8px;" />
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--折扣-->
                    <Transition enter-active-class="transition duration-200 ease-out"
                        enter-from-class="opacity-0 scale-95" enter-to-class="opacity-100 scale-100"
                        leave-active-class="transition duration-150 ease-in" leave-from-class="opacity-100 scale-100"
                        leave-to-class="opacity-0 scale-95">
                        <div v-if="showFirstOrder"
                            class="discount-container m-auto z-[1] w-full md:px-[15px] max-w-[365px] cursor-pointer md:w-[365px]">
                            <img src="@/assets/chat/discount-bg.png" class="w-full">
                        </div>
                    </Transition>
                </div>
                <div class="chat-bottom shrink-0 bg-white pt-[10px] px-[10px] pb-[40px] flex items-center md:flex-col"
                    v-if="orderStatus === 'success'">
                    <!-- 隐藏的图片上传 input -->
                    <input ref="fileInput" type="file" accept="image/*" style="display: none"
                        @change="handleImageChange" />
                    <img src="@/assets/chat/add.png" class="w-[30px] h-[30px] mr-[10px] md:hidden"
                        @click="triggerFileInput" />
                    <img src="@/assets/chat/md-add.png" class="w-[30px] h-[30px] hidden md:block"
                        @click="triggerFileInput" />
                    <div class="flex-1 mr-[6px]">
                        <input type="text" v-model="message"
                            class="w-full h-[38px] rounded-[8px] bg-[#F8F8FA] pl-[10px] pr-[10px] text-[16px] leading-[38px] text-[#1C2158] outline-none md:hidden"
                            placeholder="Type a message" @keydown.enter="handleKeydown($event)" />
                        <textarea v-model="message"
                            class="w-full hidden pl-[10px] pr-[10px] text-[16px] text-[#1C2158] outline-none resize-none md:h-full md:mt-[10px] md:bg-white md:block"
                            placeholder="Type a message" @keydown.enter="handleKeydown($event)"></textarea>
                    </div>
                    <img src="@/assets/chat/send.png" class="w-[34px] h-[34px] md:hidden" @click="sendMessage" />
                </div>
            </div>
        </div>
        <!-- 在线订单弹窗 -->
        <OnlineOrderDialog :dialog-online-order-visible="dialogOnlineOrderVisible"
            @update:dialogOnlineOrderVisible="handleOnlineDialogVisible" :augurDetail="augurDetail"
            @handlerCancelOrder="handleCancelOrder"></OnlineOrderDialog>
        <!-- 取消订单弹窗 -->
        <CancelOrderDialog :dialog-cancel-order-visible="dialogCancelOrderVisible"
            @update:dialogCancelOrderVisible="handleCancelDialogVisible" :augurDetail="augurDetail"
            :rejectMessage="rejectMessage" :augurFortuneGroupId="augurFortuneGroupId" @reorder="reorderAugur"
            :orderNo="orderNo">
        </CancelOrderDialog>
        <!-- 结束对话弹窗 -->
        <EndChartDialog :dialog-end-chat-visible="dialogEndChatVisible"
            @update:dialogEndChatVisible="handleEndChatDialogVisible" @stillCancelChat="stillCancelChat">
        </EndChartDialog>
        <!-- 聊天暂停弹出价格弹窗 -->
        <ChatOnHoldDialog :dialog-chat-on-hold-visible="dialogChatOnHoldVisible" :augurDetail="augurDetail"
            :augurFortune="augurFortune" @update:dialogChatOnHoldVisible="handleChatOnHoldDialogVisible">
        </ChatOnHoldDialog>
        <!-- 评价订单弹窗 -->
        <EvaluateOrderDialog :dialog-evaluate-order-visible="dialogEvaluateOrderVisible" :augurDetail="augurDetail"
            :orderDetail="orderDetail" @update:dialogEvaluateOrderVisible="handleEvaluateOrderDialogVisible"
            @confirm="handleEvaluateOrderDialogConfirm">
        </EvaluateOrderDialog>
        <!--封装一个Toast-->
        <transition name="toast" enter-active-class="transition duration-200 ease-out"
            enter-from-class="opacity-0 scale-95" enter-to-class="opacity-100 scale-100"
            leave-active-class="transition duration-150 ease-in" leave-from-class="opacity-100 scale-100"
            leave-to-class="opacity-0 scale-95">
            <div v-if="showToast"
                class="toast-container w-max absolute top-[50%] left-[50%] translate-x-[-50%] translate-y-[-50%] z-[3000] py-[5px] px-[15px] lg:p-[20px] rounded-[10px] lg:rounded-[18px] bg-[#00000080]">
                <div class="toast-content flex items-center">
                    <div class="toast-content-icon mr-[8px]">
                        <img src="@/assets/order/evaluate.png" class="w-[50px] h-[50px]">
                    </div>
                    <div
                        class="toast-content-title font-['Philosopher'] text-[16px] leading-[20px] text-[#fff] lg:text-[18px]">
                        Submission Successful!
                    </div>
                </div>
            </div>
        </transition>
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted, nextTick, watch, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useLayoutStore } from '@/stores/layout'
import { useUserStore } from '@/stores/user'
import { useIMStore } from "@/stores/im";
import { storeToRefs } from "pinia";
import { ElMessage } from 'element-plus'
import { ArrowRight, Loading, WarningFilled } from '@element-plus/icons-vue'
import { useThrottleFn } from '@vueuse/core'

import { getAugurDetail } from '@/api/augur'
import { cancelOrder, completeOrder, getOrderDetail, getCoinPackList, endOrder } from '@/api/order'
import { getUserInfo, activityCardList } from '@/api/user'
import { eventemitter } from "@/utils";

import OnlineOrderDialog from './components/onlineOrderDialog.vue'
import CancelOrderDialog from './components/cancelOrderDialog.vue'
import EndChartDialog from './components/endChartDialog.vue'
import ChatOnHoldDialog from './components/chatOnHoldDialog.vue'
import EvaluateOrderDialog from './components/evaluateOrderDialog.vue'


// ================================================================================================
//                                    状态定义 (State)
// ================================================================================================

// --------------------------------- Store & Router ---------------------------------
const userStore = useUserStore()
const useLayout = useLayoutStore()
const imStore = useIMStore();
const route = useRoute()
const router = useRouter()

// ------------------------------- 响应式Store State --------------------------------
const userInfo = computed(() => userStore.userInfo)
const userSig = computed(() => userStore.userSig)
const hasExperienceCard = computed(() => userStore.userInfo.hasExperienceCard) //体验卡 3分钟
const hasTenCallCard = computed(() => userStore.userInfo.hasTenCallCard) //10分钟
const hasCallCard = computed(() => userStore.userInfo.hasCallCard) //时间卡

const firstOrder = computed(() => userStore.userInfo.firstOrder) //是否有资格买1.99的礼品卡
const showFirstOrder = ref(false) //是否显示1.99的礼品卡
const { currentMessages, isAdvisorInRoom } = storeToRefs(imStore);

// ---------------------------------- 路由参数 ----------------------------------
const augurUid = ref(route.query.augurUid as string)
const imGroupId = ref(route.query.imGroupId as string)
const augurFortuneGroupId = ref(route.query.augurFortuneGroupId as string)
const orderNo = ref(route.query.orderNo as string)
const augurFortune = ref<any>({})
const augurFortuneLeftMinutes = ref(0)
// ---------------------------------- 页面核心状态 ----------------------------------
const pageLoading = ref(false)
const onlineOrderBg = ref('')
const augurDetail = ref<any>({})
const orderStatus = ref('pending') // 'pending', 'success'
const invitationStatus = ref('pending') // 'pending', 'accepted', 'rejected', 'timeout'
const rejectMessage = ref('')
const orderDetail = ref<any>({})
const coinPackList = ref<any[]>([])
// --------------------------------- 对话框可见性 ---------------------------------
const dialogOnlineOrderVisible = ref(false)
const dialogCancelOrderVisible = ref(false)
const dialogEndChatVisible = ref(false)
const dialogChatOnHoldVisible = ref(false)
const dialogEvaluateOrderVisible = ref(false)
const remainTime = ref(0)

// ------------------------------------ UI状态 ------------------------------------
const showTip = ref(false) // 隐私警告提示
const showRemainingTime = ref(false) // 剩余时间提示
const showInsufficient = ref(false) // 余额不足提示
const remainingTime = ref('1:00')
const popoverVisible = ref(false) // 价格弹窗
const showToast = ref(false)

// ------------------------------------ 聊天状态 ------------------------------------
const message = ref('')
const messagesRef = ref<HTMLElement | null>(null)

// ================== 通话时长计时 ==================
const callStartTime = ref<number | null>(null)
const callDuration = ref('00:00:00')
let callTimer: number | null = null

function formatDuration(seconds: number) {
    const h = String(Math.floor(seconds / 3600)).padStart(2, '0')
    const m = String(Math.floor((seconds % 3600) / 60)).padStart(2, '0')
    const s = String(seconds % 60).padStart(2, '0')
    return `${h}:${m}:${s}`
}

watch(orderStatus, (val) => {
    if (val === 'success') {
        callStartTime.value = Date.now()
        callDuration.value = '00:00:00'
        if (callTimer) clearInterval(callTimer)
        remainTime.value = calculateRemainTime() * 60
        callTimer = window.setInterval(() => {
            if (callStartTime.value) {

                const seconds = Math.floor((Date.now() - callStartTime.value) / 1000)
                callDuration.value = formatDuration(seconds)
                remainTime.value--
                if (remainTime.value <= 120) {
                    showTip.value = false
                    showInsufficient.value = true
                    if (firstOrder.value) {
                        showFirstOrder.value = true
                    }


                }
                console.log('remainTime', remainTime.value)
            }
        }, 1000)
    } else {
        if (callTimer) {
            clearInterval(callTimer)
            callTimer = null
        }
        callDuration.value = '00:00:00'
    }
})
const calculateRemainTime = () => {
    const experienceCardLeftMinutes = hasExperienceCard.value ? 1 : 0 //体验卡可以用一张
    const tenCallCardLeftMinutes = hasTenCallCard.value // 10分钟卡(无限用)
    const callCardLeftMinutes = hasCallCard.value // 6分钟卡(无限用)
    const coinsLeftMinutes = userStore.userInfo.coins / augurFortuneLeftMinutes.value //余额
    return experienceCardLeftMinutes + tenCallCardLeftMinutes + callCardLeftMinutes + coinsLeftMinutes

}
// ================================================================================================
//                                    逻辑实现 (Logic)
// ================================================================================================

// --------------------------------- 主要业务流程 ---------------------------------
const getAugurDetailFunc = () => {
    pageLoading.value = true
    getAugurDetail({
        augurUid: augurUid.value as string
    }).then(async (response: any) => {
        augurDetail.value = response.data
        onlineOrderBg.value = response.data.avatar
        console.log('response.data.augurFortune', response.data.augurFortune)
        augurFortune.value = response.data.augurFortune.filter((item: any) => item.augurFortuneGroupId == augurFortuneGroupId.value)[0]
        console.log('augurFortune', augurFortune.value)
        augurFortuneLeftMinutes.value = augurFortune.value.discountCoins < augurFortune.value.coins ? augurFortune.value.discountCoins : augurFortune.value.coins
        dialogOnlineOrderVisible.value = true
        pageLoading.value = false
        // 获取到占卜师信息后，进行IM登录和设置
        try {
            // 1. 登录并等待SDK Ready
            if (!imStore.isLogin) await imStore.login(String(userInfo.value.uid), userSig.value);

            const groupId = imGroupId.value;
            const advisorId = augurDetail.value?.uid;
            const currentOrderNo = orderNo.value as string;

            if (!groupId || !advisorId || !currentOrderNo) {
                const errorMessage = `Failed to start chat: Missing Group ID (${groupId}), Advisor ID (${advisorId}), or Order Number (${currentOrderNo}).`;
                ElMessage.error(errorMessage);
                throw new Error(errorMessage);
            }

            // 设置要监听的占卜师ID
            imStore.setAdvisor(String(advisorId));

            // 2. 向占卜师发送C2C通话邀请
            try {
                const advisorIdStr = String(advisorId);
                console.log(`[Chat] Preparing to send C2C invitation. Advisor ID: ${advisorIdStr}`);
                await imStore.sendCallInvitation(advisorIdStr, currentOrderNo, groupId);
                console.log(`[Chat] C2C Call invitation sent successfully.`);
            } catch (error) {
                console.error(`[Chat] Failed to send C2C call invitation:`, error);
                ElMessage.error('Could not send call invitation to the advisor.');
                return;
            }

            // 此处不再等待，等待逻辑已移至事件处理器中

        } catch (error: any) {
            console.error('IM setup failed', error)
            ElMessage.error(`Chat service connection failed: ${error.message || 'Unknown error'}`)
        }
    })
}

const reorderAugur = async () => {
    try {
        const advisorIdStr = String(augurUid.value);
        handleCancelDialogVisible(false)
        dialogOnlineOrderVisible.value = true
        console.log(`[Chat] Preparing to resend C2C invitation. Advisor ID: ${advisorIdStr}`);
        await imStore.sendCallInvitation(advisorIdStr, orderNo.value as string, imGroupId.value);
        console.log(`[Chat] C2C Call invitation resent successfully.`);
    } catch (error) {
        console.error(`[Chat] Failed to resend C2C call invitation:`, error);
        ElMessage.error('Could not resend call invitation to the advisor.');
    }
}

// ----------------------------------- 事件总线处理 -----------------------------------
const handleInvitationUpdate = async ({ status, payload }: { status: 'accepted' | 'rejected' | 'timeout', payload: any }) => {
    console.log(`[Event Bus] Received invitation status update: ${status}`, payload);
    invitationStatus.value = status;
    dialogOnlineOrderVisible.value = false

    // 如果占卜师接受，则等待后端将自己拉入群组
    if (status === 'accepted') {
        pageLoading.value = true; // 显示全屏加载
        console.log(`[Chat] Advisor accepted. Waiting to be added to group ${imGroupId.value}...`);
        imStore.setTargetGroupId(imGroupId.value);

        // 不再主动joinGroup，等待 'group-joined' 事件
    }

    // 如果被拒绝或超时，弹出取消订单对话框
    if (status === 'rejected' || status === 'timeout') {

        if (status === 'timeout') {
            handleCancelOrder('2') // 超时自动取消订单
        } else {
            rejectMessage.value = payload.data // 记录拒绝信息
        }

        dialogCancelOrderVisible.value = true
    }
}

const handleGroupJoined = async ({ conversationID }: { conversationID: string }) => {
    console.log(`[Event Bus] Received group-joined event. Conversation ID: ${conversationID}`);
    pageLoading.value = false; // 隐藏全屏加载

    try {
        await imStore.setCurrentConversation(conversationID);
        orderStatus.value = 'success';
        showTip.value = true
        console.log(`[Chat] Chat room is ready. Switched to conversation ${conversationID}.`);
    } catch (error) {
        console.error(`[Chat] Failed to set current conversation after joining group:`, error);
        ElMessage.error('Failed to prepare the chat room. Please try again.');
    }
};

// ----------------------------------- API 调用 -----------------------------------
const handleCancelOrder = (data: any) => {
    cancelOrder({
        orderNo: orderNo.value as string,
        status: data
    }).then(() => {
        ElMessage.success('Order cancelled successfully')
    }).catch(error => {
        console.error('Failed to cancel order:', error);
        ElMessage.error('An error occurred while cancelling the order.');
    })
}

// ----------------------------------- IM 聊天逻辑 -----------------------------------
// 监听占卜师是否加入房间的状态
watch(isAdvisorInRoom, (ready) => {
    if (ready) {
        ElMessage.success('Advisor has connected.');
    }
});

// 消息发送逻辑
const sendMessage = async () => {
    const content = message.value.trim()
    if (!content || !imGroupId.value) return;
    try {
        await imStore.sendMessage(String(imGroupId.value), content, 'GROUP')
        message.value = '' // 发送后清空输入框
        scrollToBottom()
    } catch (error) {
        ElMessage.error('Message failed to send. Please try again.')
        console.error('send message error', error)
    }
}

// 处理键盘回车事件
const handleKeydown = (e: KeyboardEvent) => {
    if (!e.shiftKey) {
        e.preventDefault()
        sendMessage()
    }
}

// --------------------------------- UI & DOM 辅助函数 --------------------------------
const scrollToBottom = () => {
    nextTick(() => {
        if (messagesRef.value) {
            messagesRef.value.scrollTop = messagesRef.value.scrollHeight;
        }
    });
};

// 监听消息变化，自动滚动
watch(
    currentMessages,
    () => { scrollToBottom(); },
    { deep: true, immediate: true }
);

const handleResize = useThrottleFn(() => {
    const layoutElement = document.getElementsByClassName('layout')[0] as HTMLElement
    if (layoutElement) {
        layoutElement.style.height = '100vh'
    }
}, 200)

const changePopoverVisible = () => {
    popoverVisible.value = !popoverVisible.value
}


// ----------------------------------- 对话框处理器 -----------------------------------
const handleOnlineDialogVisible = (visible: boolean) => { dialogOnlineOrderVisible.value = visible }
const handleCancelDialogVisible = (visible: boolean) => { dialogCancelOrderVisible.value = visible }
const handleEndChatDialogVisible = (visible: boolean) => { dialogEndChatVisible.value = visible }
const handleChatOnHoldDialogVisible = (visible: boolean) => { dialogChatOnHoldVisible.value = visible }
const handleEvaluateOrderDialogVisible = (visible: boolean) => { dialogEvaluateOrderVisible.value = visible }
const handleEvaluateOrderDialogConfirm = () => {
    showToast.value = true
    setTimeout(() => { showToast.value = false }, 2000)
    dialogEvaluateOrderVisible.value = false

}

const retrySend = async (msg: any) => {
    try {
        await imStore.resendMessage(msg);
    } catch (error) {
        ElMessage.error('重发失败，请检查网络后重试');
    }
};

function handleGroupDisbanded() {
    ElMessage.error('群聊已被解散，订单已取消')
    // handleCancelOrder('2')
    dialogCancelOrderVisible.value = true
    orderStatus.value = 'cancel'
}

const handleCloseChat = () => {
    dialogEndChatVisible.value = true
    showInsufficient.value = false
    showTip.value = false
}
const stillCancelChat = () => {
    endOrder(orderNo.value).then(response => {
        if (callTimer) clearInterval(callTimer)
        getOrderDetailFunc()
        dialogEndChatVisible.value = false
    })
}
const getOrderDetailFunc = () => {
    getOrderDetail(orderNo.value).then(response => {
        dialogEvaluateOrderVisible.value = true
        orderDetail.value = response.data
    }).catch(error => {
        console.error('Failed to get order detail:', error);
    })
}

const getCoinPackListFunc = () => {
    getCoinPackList().then(response => {
        coinPackList.value = response.data
    })
}
// ================================================================================================
//                                    个人信息处理（每60秒一次）
const getUserInfoFunc = () => {
    getUserInfo().then(response => {
        userStore.setUserInfo(response.data)
    })
}
let userTimer: number | null = null
function startUserInfoTimer() {
    if (userTimer) return
    getUserInfoFunc()
    userTimer = window.setInterval(() => {
        getUserInfoFunc()
    }, 60000)
}
function stopUserInfoTimer() {
    if (userTimer) {
        clearInterval(userTimer)
        userTimer = null
    }
}

// const getTimeCardListFunc = () => {
//     activityCardList({
//         current: 1,
//         size: 100
//     }).then(response => {
//         console.log('response', response)
//     })
// }
// ================================================================================================
// ================================================================================================
//                                    生命周期钩子 (Lifecycle)
// ================================================================================================
onMounted(() => {
    useLayout.setShowFooter(false);
    handleResize()
    window.addEventListener('resize', handleResize)
    getAugurDetailFunc()
    // getTimeCardListFunc()
    eventemitter.on('invitation-status-update', handleInvitationUpdate);
    eventemitter.on('group-joined', handleGroupJoined);
    eventemitter.on('group-disbanded', handleGroupDisbanded);
    startUserInfoTimer()
    getCoinPackListFunc()
    // 离开前提示
    window.addEventListener('beforeunload', handleBeforeUnload)
})

onUnmounted(() => {
    window.removeEventListener('resize', handleResize)
    eventemitter.off('invitation-status-update', handleInvitationUpdate);
    eventemitter.off('group-joined', handleGroupJoined);
    eventemitter.off('group-disbanded', handleGroupDisbanded);
    imStore.logout()
    if (callTimer) clearInterval(callTimer)
    stopUserInfoTimer()
    // 移除离开前提示
    window.removeEventListener('beforeunload', handleBeforeUnload)
})

function handleBeforeUnload(e: BeforeUnloadEvent) {
    // 只有在聊天进行中才提示
    if (orderStatus.value === 'success') {
        e.preventDefault()
        e.returnValue = 'You are in a chat session, leaving will interrupt the conversation. Are you sure you want to leave?'
        return e.returnValue
    }
}

const fileInput = ref<HTMLInputElement | null>(null)

function triggerFileInput() {
    fileInput.value?.click()
}

async function handleImageChange(e: Event) {
    const files = (e.target as HTMLInputElement).files
    if (!files || !files.length) return
    const file = files[0]
    // 这里假设 imStore 有 sendImageMessage 方法，实际项目可根据实际 API 调整
    try {
        await imStore.sendImageMessage(String(imGroupId.value), file, 'GROUP')
        // 发送后自动滚动到底部
        scrollToBottom()
    } catch (error) {
        ElMessage.error('图片发送失败，请重试')
    }
}
</script>

<style lang="scss" scoped>
.app-container {
    flex: 1;
    position: relative;
    isolation: isolate;

    &::before {
        content: '';
        position: absolute;
        inset: 0;
        z-index: -1;
        background-image: var(--bg-image, url('@/assets/order/order-bg.png'));
        background-repeat: no-repeat;
        background-position: center;
        background-size: cover;
        filter: blur(10px);
        transform: scale(1.1);
    }
}

@media screen and (max-width: 1120px) {
    .app-container {
        display: flex;
        flex-direction: column;
        overflow: hidden; // background-color: #f8f8fa;

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;

            .breadcrumb-container {
                flex-shrink: 0;
            }

            .chat-container {
                flex: 1;
                display: flex;
                flex-direction: column;
                overflow: hidden;

                .chat-header {
                    flex-shrink: 0;
                }

                .chat-box {
                    flex: 1;
                    overflow-y: auto;
                    padding: 15px;
                    box-sizing: border-box;
                    background-color: #f8f8fa;
                }

                .chat-bottom {
                    width: 100%;
                    height: 92px;
                    background-color: #fff;
                    flex-shrink: 0;
                }
            }
        }
    }
}

@media screen and (min-width: 1120px) {
    .chat-container {
        width: 100%;
        height: 633px;
        display: flex;
        flex-direction: column;
        overflow: hidden; // background-color: #fff;
        border-radius: 20px;

        .chat-box {
            flex: 1;
            overflow-y: scroll;
            background-color: #f8f8fa;
            padding: 20px;
            box-sizing: border-box;
        }

        .chat-bottom {
            height: 170px;
            align-items: normal;
        }
    }
}

:deep(.el-breadcrumb) {
    display: flex;
    height: 100%;
    flex-wrap: nowrap;
    overflow-x: auto;
    align-items: center;

    .el-breadcrumb__item {
        flex-shrink: 0;

        .el-breadcrumb__inner {
            color: #fff !important;
            font-family: 'Philosopher';
        }
    }
}

.chat-header-right {
    &::before {
        content: '';
        position: absolute;
        inset: 0;
        width: 100%;
        height: 100%;
        padding: 1px;
        border-radius: 6px; // border: 1px solid;
        background: linear-gradient(104.04deg, #96baff 0%, #7064ff 86.39%);
        -webkit-mask: linear-gradient(#96baff 0 0) content-box, linear-gradient(#96baff 0 0);
        -webkit-mask-composite: xor;
        mask-composite: exclude;
        pointer-events: none;
    }
}

:deep(.recharge-popover) {
    background: rgba(231, 240, 255, 1);
    border-radius: 16px;
    padding: 12px;
    box-sizing: border-box;
    width: 355px !important;
    z-index: 20 !important;

    .el-popper__arrow {
        &::before {
            background: rgba(231, 240, 255, 1) !important;
        }
    }

    .recharge-popover-list-item {
        box-shadow: 0px 0px 4px 0px #bac5ff4f;

        &:hover {
            background: linear-gradient(91.29deg, #d0d1d2 1.1%, #e4e7f9 95.36%);
        }
    }

    .recharge-popover-list-item-right {
        background: linear-gradient(91.29deg, #7aafff 1.1%, #7a87ff 95.36%);
    }

    .el-popover__title {
        display: none;
    }
}

.remaining-time {
    background: linear-gradient(91.29deg, #f8e0fb 1.1%, #e1e2fb 95.36%);
}

.buy-credits {
    background: linear-gradient(91.29deg, #7AAFFF 1.1%, #7A87FF 95.36%);
}

// 聊天消息样式
.message-wrapper {
    display: flex;
    margin-bottom: 20px;

    &.is-me {
        flex-direction: row-reverse;

        .avatar {
            margin-left: 10px;
        }

        .content-row {
            justify-content: flex-end;
        }

        .text-bubble {
            background: #fff;
            color: #1C2158;
            border-top-right-radius: 4px;
        }
    }

    &.is-other {
        flex-direction: row;

        .avatar {
            margin-right: 10px;
        }

        .content-row {
            justify-content: flex-start;
        }

        .text-bubble {
            background: #BED5FF;
            color: #1C2158;
            border-top-left-radius: 4px;
        }
    }

    .avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        object-fit: cover;
        flex-shrink: 0;
    }

    .content-row {
        display: flex;
        align-items: center;
        flex: 1;
        min-width: 0;
    }

    .content {
        display: flex;
        flex-direction: column;
        max-width: 70%;
        min-width: 0;
    }

    .text-bubble {
        padding: 10px 15px;
        border-radius: 18px;
        font-size: 16px;
        line-height: 1.5;
        word-break: break-word;
    }
}

.msg-status-icon {
    display: flex;
    align-items: center;
    font-size: 18px;
}
</style>
