<!-- app -->
<template>
    <div class="app-container">
        <div class="main-content px-[15px] md:px-[15px] lg:px-[120px]">
            <el-breadcrumb :separator-icon="ArrowRight"
                class="font-['Open_Sans_3'] font-[600] h-[54px] text-[14px] pt-[8px] mb-[14px] md:h-[66px] md:mb-[10px]">
                <el-breadcrumb-item class="text-[#1C2158]">Home</el-breadcrumb-item>
                <el-breadcrumb-item>Profile Edit</el-breadcrumb-item>
            </el-breadcrumb>
            <div class="profile-container flex">
                <OrderLeft class="shrink-0 hidden md:block" :activeItem="'Profile Edit'" />
                <div class="profile-right flex-1 md:bg-white md:rounded-[30px] md:p-[40px] overflow-auto"
                    v-loading="loading">
                    <div class="profile-right-header flex items-center justify-between">
                        <div
                            class="profile-right-header-title text-[18px] leading-[20px] text-[#1C2158] font-['Philosopher'] font-bold md:text-[28px] md:leading-[32px]">
                            Profile Edit</div>
                    </div>
                    <div class="profile-right-box flex flex-col-reverse md:flex-row mt-[35px]">
                        <div class="profile-right-box-form grow-[2]">
                            <el-form :model="userForm" :rules="userFormRules" ref="userFormRef" label-width="100px"
                                label-position="top" :hide-required-asterisk="true">
                                <!-- 姓名输入框 -->
                                <el-form-item prop="username">
                                    <template #label>
                                        <span data-content="(*Required )">Name</span>
                                    </template>
                                    <el-input :placeholder="'Provide your username'" v-model="userForm.username"
                                        :clearable="true"></el-input>
                                </el-form-item>

                                <!-- 出生日期选择器 -->
                                <el-form-item :label="'Birth Date'" prop="birthDate">
                                    <template #label>
                                        <span data-content="(*Required )">Birth Date</span>
                                    </template>
                                    <div class="relative w-full" ref="birthDateRef">
                                        <el-input :placeholder="'Select your birth date'" v-model="userForm.birthDate"
                                            readonly @click="toggleCalendar">
                                            <template #suffix>
                                                <img src="@/assets/common/calendar.png" alt=""
                                                    class="w-[24px] h-[24px]" />
                                            </template>
                                        </el-input>
                                        <!-- 日期选择器弹出层 -->
                                        <Teleport to="body">
                                            <Transition enter-active-class="transition duration-200 ease-out"
                                                enter-from-class="opacity-0 transform scale-95 -translate-y-2"
                                                enter-to-class="opacity-100 transform scale-100 translate-y-0"
                                                leave-active-class="transition duration-150 ease-in"
                                                leave-from-class="opacity-100 transform scale-100 translate-y-0"
                                                leave-to-class="opacity-0 transform scale-95 -translate-y-2">
                                                <div v-if="showCalendar" ref="calendarRef" :style="calendarStyle"
                                                    class="fixed z-[4000] shadow-lg origin-top calendar-dropdown">
                                                    <CustomCalendar v-model="selectedDate" @confirm="handleDateConfirm"
                                                        @cancel="showCalendar = false" :disabledDate="disabledDate" />
                                                </div>
                                            </Transition>
                                        </Teleport>
                                    </div>
                                </el-form-item>

                                <!-- 出生时间选择器 -->
                                <el-form-item :label="'Birth Time'" prop="birthTime">
                                    <template #label>
                                        <span data-content="(*Required )">Birth Time</span>
                                    </template>
                                    <div class="relative w-full" ref="birthTimeRef">
                                        <el-input :placeholder="'Fill your birth time'" v-model="userForm.birthTime"
                                            readonly @click="toggleTimePicker">
                                        </el-input>
                                        <!-- 时间选择器弹出层 -->
                                        <Teleport to="body">
                                            <Transition enter-active-class="transition duration-200 ease-out"
                                                enter-from-class="opacity-0 transform scale-95 -translate-y-2"
                                                enter-to-class="opacity-100 transform scale-100 translate-y-0"
                                                leave-active-class="transition duration-150 ease-in"
                                                leave-from-class="opacity-100 transform scale-100 translate-y-0"
                                                leave-to-class="opacity-0 transform scale-95 -translate-y-2">
                                                <div v-if="showTimePicker" :style="timePickerStyle"
                                                    class="fixed z-[4000] shadow-lg origin-top time-picker-dropdown">
                                                    <CustomTimePicker v-model="selectedTime"
                                                        @confirm="handleTimeConfirm" @cancel="showTimePicker = false" />
                                                </div>
                                            </Transition>
                                        </Teleport>
                                    </div>
                                </el-form-item>

                                <!-- 出生地选择器 -->
                                <el-form-item :label="'Birthplace'" prop="birthPlace" class="white-bg">
                                    <template #label>
                                        <span data-content="(*Required )">Birthplace</span>
                                    </template>
                                    <SmSearchRemote v-model="userForm.birthPlace" :remote-api="searchBirthPlace"
                                        @changePlace="handleChangePlace" />
                                </el-form-item>

                                <!-- 性别选择器 -->
                                <el-form-item :label="'Gender'" prop="gender">
                                    <template #label>
                                        <span data-content="(*Required )">Gender</span>
                                    </template>
                                    <div class="bg-white rounded-[6px] w-full flex items-center lg:bg-[#F8F8FA]">
                                        <div class="gender-item relative flex-1 flex flex-col justify-center items-center pt-[10px] pb-[12px] cursor-pointer lg:pt-[13px] lg:pb-[5px]"
                                            v-for="(item, index) in genderOptions"
                                            :class="{ 'active-gender lg:bg-white': userForm.gender == item.value }"
                                            @click="changeGender(item.value)">
                                            <img :src="userForm.gender == item.value ? item.selectIcon : item.noSelectIcon"
                                                alt="" class="w-[24px] h-[24px]" />
                                            <span
                                                class="text-[12px] font-['Source_Sans_Pro'] mt-[6px] opacity-50 leading-[12px] lg:text-[14px] lg:leading-[14px] lg:mt-[10px]"
                                                :class="{ 'text-[#4484FF]': userForm.gender == item.label }">{{
                                                    item.label
                                                }}</span>
                                        </div>
                                    </div>
                                </el-form-item>
                            </el-form>
                        </div>
                        <div class="profile-right-input grow-[1] flex justify-center mb-[10px]">
                            <div class="avatar-box w-[180px] h-[180px] relative" v-loading-mask="imgUploadLoading">
                                <img :src="userForm.avatar" alt="your avatar"
                                    class="w-full h-full object-cover rounded-[18px]">
                                <div class="upload-box absolute w-[40px] h-[40px] bottom-[-10px] right-[-10px]">
                                    <img src="@/assets/user/upload.png" alt="" class="w-full h-full">
                                    <input type="file" accept="image/*"
                                        class="absolute opacity-0 w-full h-full top-0 right-0 cursor-pointer"
                                        @change="handleFileChange($event)">
                                </div>

                            </div>
                        </div>
                    </div>
                    <div class="profile-right-bottom mt-[20px] md:mt-[30px] md:flex md:justify-center">
                        <div @click="updateUserInfoFunc(userFormRef)" v-loading-mask="updateUserLoading"
                            class="profile-right-bottom-button w-full h-[50px] md:w-[400px] rounded-[10px] text-white md:text-[20px] font-['Philosopher'] font-bold leading-[20px] text-center flex items-center justify-center">
                            Save
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, reactive, watch, nextTick, computed } from 'vue'
import { ArrowRight } from '@element-plus/icons-vue'
import OrderLeft from '@/components/orderLeft.vue'
import { ElMessage } from 'element-plus'
import type { FormInstance } from 'element-plus'

// 导入自定义组件
import CustomCalendar from '@/components/CustomCalendar.vue'
import CustomTimePicker from '@/components/CustomTimePicker.vue'
import SmSearchRemote from '@/components/smSearchRemote/smSearchRemote.vue'
import { searchBirthPlace } from '@/api/common'
import { getUserInfo, updateUserInfo } from '@/api/user'
import { uploadFile } from '@/api/common'
import { useUserStore } from '@/stores/user'
interface ChangePlaceData {
    latitude: string
    longitude: string
}
const useUser = useUserStore()
const loading = ref(false)

const disabledDate = (date: Date) => {
    return date.getTime() > new Date().getTime()
}
const userFormRef = ref<FormInstance>() // Add this ref
const imgUploadLoading = ref(false)

const updateUserLoading = ref(false)
// 表单数据对象
const userForm = reactive({
    avatar: '',
    username: '',
    gender: '1',
    birth: '0',
    birthDate: '',
    birthTime: '',
    birthPlace: '',
    latitude: '',
    longitude: '',
    birthday: '',
})

// 表单验证规则
const userFormRules = reactive({
    username: [{ required: true, message: 'Please enter your name', trigger: 'blur' }],
    birthDate: [{ required: true, message: 'Please select your birth date', trigger: 'change' }],
    birthTime: [{ required: true, message: 'Please select your birth time', trigger: 'change' }],
    gender: [{ required: true, message: 'Please select your gender', trigger: 'change' }],
    birthPlace: [{ required: true, message: 'Please select your birthplace', trigger: 'change' }],
})

// 性别选项配置
const genderOptions = [
    {
        label: 'Male',
        value: '1',
        selectIcon: new URL('@/assets/common/boy-select.png', import.meta.url).href,
        noSelectIcon: new URL('@/assets/common/boy-unselect.png', import.meta.url).href,
    },
    {
        label: 'Female',
        value: '2',
        selectIcon: new URL('@/assets/common/gril-select.png', import.meta.url).href,
        noSelectIcon: new URL('@/assets/common/gril-unselect.png', import.meta.url).href,
    },
    {
        label: 'Non-binary',
        value: '3',
        selectIcon: new URL('@/assets/common/no-select.png', import.meta.url).href,
        noSelectIcon: new URL('@/assets/common/no-unselect.png', import.meta.url).href,
    },
]

// 日历相关状态
const showCalendar = ref(false)
const selectedDate = ref<Date>()
const birthDateRef = ref<HTMLElement | null>(null)
const calendarRef = ref<HTMLElement | null>(null)
const calendarPosition = ref({ top: 0, left: 0 })

// 时间选择器相关状态
const showTimePicker = ref(false)
const selectedTime = ref<{ hour: number; minute: number } | undefined>(undefined)
const timePickerStyle = ref({})
const birthTimeRef = ref()

const handleChangePlace = (data: ChangePlaceData) => {
    userForm.latitude = data.latitude
    userForm.longitude = data.longitude
}

// 更改性别的处理函数
const changeGender = (gender: string) => {
    userForm.gender = gender
}

// 计算日历样式
const calendarStyle = computed(() => {
    return {
        top: `${calendarPosition.value.top}px`,
        left: `${calendarPosition.value.left}px`,
        position: 'fixed' as const,
    }
})

// 更新日历位置
const updateCalendarPosition = () => {
    if (!birthDateRef.value || !calendarRef.value) return

    const inputRect = birthDateRef.value.getBoundingClientRect()
    const calendarHeight = calendarRef.value.offsetHeight
    const calendarWidth = calendarRef.value.offsetWidth
    const viewportHeight = window.innerHeight
    const viewportWidth = window.innerWidth

    const spaceBelow = viewportHeight - inputRect.bottom
    const spaceAbove = inputRect.top

    let top: number
    let left: number = inputRect.left

    // Determine vertical position
    if (spaceBelow >= calendarHeight || spaceBelow > spaceAbove) {
        top = inputRect.bottom + 8 // Position below with 8px margin
    } else {
        top = inputRect.top - calendarHeight - 8 // Position above with 8px margin
    }

    // Prevent horizontal overflow
    if (left + calendarWidth > viewportWidth) {
        left = viewportWidth - calendarWidth - 8 // 8px margin from right edge
    }
    if (left < 0) {
        left = 8 // 8px margin from left edge
    }

    calendarPosition.value = { top, left }
}

// 切换日历显示状态
const toggleCalendar = () => {
    showCalendar.value = !showCalendar.value
    if (showCalendar.value) {
        // Defer position calculation until the calendar element is rendered
        nextTick(() => {
            updateCalendarPosition()
        })
    }
}

// 处理日期确认
const handleDateConfirm = (date: Date) => {
    if (date) {
        userForm.birthDate = date.toLocaleDateString()
    }
    showCalendar.value = false
}

// 处理页面滚动
const handleScroll = () => {
    if (showCalendar.value) {
        updateCalendarPosition()
    }
}

// 点击外部关闭日历
const closeCalendarOnClickOutside = (e: MouseEvent) => {
    const target = e.target as HTMLElement
    if (showCalendar.value && birthDateRef.value && !birthDateRef.value.contains(target) && !target.closest('.calendar-dropdown')) {
        showCalendar.value = false
    }
}

// 监听日历显示状态变化
watch(showCalendar, (val) => {
    if (val) {
        setTimeout(() => {
            document.addEventListener('click', closeCalendarOnClickOutside)
            window.addEventListener('scroll', handleScroll, true)
            window.addEventListener('resize', updateCalendarPosition)
        }, 0)
    } else {
        document.removeEventListener('click', closeCalendarOnClickOutside)
        window.removeEventListener('scroll', handleScroll, true)
        window.removeEventListener('resize', updateCalendarPosition)
    }
})

// 更新时间选择器位置
function updateTimePickerPosition() {
    if (!birthTimeRef.value) return
    const rect = birthTimeRef.value.getBoundingClientRect()
    timePickerStyle.value = {
        left: rect.left + 'px',
        top: rect.bottom + 8 + 'px',
        position: 'fixed',
    }
}

// 点击外部关闭时间选择器
const closeTimePickerOnClickOutside = (e: MouseEvent) => {
    const target = e.target as HTMLElement
    if (showTimePicker.value && birthTimeRef.value && !birthTimeRef.value.contains(target) && !target.closest('.time-picker-dropdown')) {
        showTimePicker.value = false
    }
}

// 监听时间选择器显示状态变化
watch(showTimePicker, (val) => {
    if (val) {
        setTimeout(() => {
            document.addEventListener('click', closeTimePickerOnClickOutside)
            window.addEventListener('scroll', updateTimePickerPosition, true)
            window.addEventListener('resize', updateTimePickerPosition)
        }, 0)
        nextTick(() => {
            updateTimePickerPosition()
        })
    } else {
        document.removeEventListener('click', closeTimePickerOnClickOutside)
        window.removeEventListener('scroll', updateTimePickerPosition, true)
        window.removeEventListener('resize', updateTimePickerPosition)
    }
})

// 切换时间选择器显示状态
function toggleTimePicker() {
    showTimePicker.value = !showTimePicker.value
    nextTick(() => {
        if (showTimePicker.value) {
            updateTimePickerPosition()
        }
    })
}

// 处理时间确认
function handleTimeConfirm(val: { hour: number; minute: number }) {
    userForm.birthTime = `${val.hour.toString().padStart(2, '0')}:${val.minute.toString().padStart(2, '0')}`
    showTimePicker.value = false
}
const getBirthday = (timestamp: number): string => {
    const date = new Date(Number(timestamp) * 1000)
    const Y = date.getFullYear() + '/'
    const M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '/'
    const D = (date.getDate() + 1 <= 10 ? '0' + date.getDate() : date.getDate()) + ' '
    return Y + M + D
}

// 文件上传处理
const handleFileChange = async (event: Event) => {
    event.stopPropagation()
    const input = event.target as HTMLInputElement

    if (!input.files || !input.files[0]) return

    imgUploadLoading.value = true
    try {
        const response: any = await uploadFile(input.files[0])
        if (response?.data?.[0]) {
            userForm.avatar = response.data[0]
        }
    } catch (error) {
        console.error('Failed to upload file:', error)
    } finally {
        imgUploadLoading.value = false
        // 清空input值，允许重复选择同一文件
        input.value = ''
    }
}
const getUserInfoFunc = () => {
    loading.value = true
    getUserInfo()
        .then((res) => {
            console.log(res)
            userForm.username = res.data.username
            userForm.birthDate = getBirthday(res.data.birthday)
            if (res.data.birthday) {
                selectedDate.value = new Date(Number(res.data.birthday) * 1000)
            }
            selectedTime.value = {
                hour: Number(res.data.birthTime.split(':')[0]),
                minute: Number(res.data.birthTime.split(':')[1]),
            }
            userForm.birthTime = res.data.birthTime
            userForm.birthPlace = res.data.birthPlace
            userForm.gender = res.data.gender
            userForm.latitude = res.data.latitude ?? ''
            userForm.longitude = res.data.longitude ?? ''
            userForm.avatar = res.data.avatar
        })
        .finally(() => {
            loading.value = false
        })
}

const updateUserInfoFunc = async (formRef: FormInstance | undefined) => {
    if (!formRef) return
    const isValid = await formRef.validate()
    if (isValid) {
        updateUserLoading.value = true
        userForm.birthday = String(new Date(userForm.birthDate).getTime() / 1000)
        updateUserInfo(userForm).then(res => {
            getUserInfo().then((res: any) => {
                useUser.setUserInfo(res.data)
                ElMessage.success('Update success')

            }).finally(() => {
                updateUserLoading.value = false
            })
        })
    } else {
        console.log('error submit!!')
        return false
    }
}
onMounted(() => {
    getUserInfoFunc()
})
const followList = ref<any[]>([])
</script>
<style lang="scss" scoped>
/* Element Plus表单样式重写 */
:deep(.el-form) {
    .el-form-item {

        /* 表单标签样式 */
        .el-form-item__label {
            font-size: 14px;
            font-weight: 700;
            font-family: 'Philosopher';

            /* 必填标记样式 */
            span {
                &::after {
                    content: attr(data-content);
                    font-size: 12px;
                    color: #ff5353;
                    margin-left: 4px;
                    font-weight: 400;
                }
            }
        }

        /* 白色背景表单项样式 */
        &.white-bg {
            .el-form-item__content {
                background-color: #fff;
            }
        }

        /* 表单控件容器样式 */
        .el-form-item__content {

            .el-input__wrapper,
            .el-select__wrapper {
                height: 44px;
                font-size: 15px;
                border-radius: 6px;
                font-weight: 400;
                color: #1c2158;
                font-family: 'Open Sans';
            }

            /* 输入框文本颜色 */
            .el-input__inner {
                color: #1c2158;
            }
        }
    }

    /* 大屏幕响应式样式 */
    @media screen and (min-width: 1330px) {
        .el-form-item {
            margin-bottom: 24px;

            /* 大屏幕下的标签样式 */
            .el-form-item__label {
                font-size: 22px;
                font-weight: 700;
                font-family: 'Philosopher';
            }

            /* 大屏幕下的白色背景样式 */
            &.white-bg {
                .el-form-item__content {
                    background-color: #f8f8fa;
                }
            }

            /* 大屏幕下的表单控件样式 */
            .el-form-item__content {
                background-color: #f8f8fa;

                /* 文本域样式 */
                .el-textarea__inner {
                    font-size: 16px;
                    border-radius: 12px;
                    background-color: #f8f8fa;
                    color: #1c2158;
                    font-family: 'Open Sans';
                }

                /* 输入框和选择器样式 */
                .el-input__wrapper,
                .el-select__wrapper {
                    height: 50px;
                    font-size: 20px;
                    border-radius: 12px;
                    background-color: transparent;
                    color: #1c2158;
                    font-family: 'Open Sans';
                }
            }
        }
    }
}

/* 性别选择器激活状态样式 */
.active-gender {
    &::before {
        content: '';
        position: absolute;
        inset: 0;
        width: 100%;
        height: 100%;
        padding: 1px;
        border-radius: 6px;
        background: linear-gradient(104.04deg, #96baff 0%, #7064ff 86.39%);
        -webkit-mask: linear-gradient(#96baff 0 0) content-box, linear-gradient(#96baff 0 0);
        -webkit-mask-composite: xor;
        mask-composite: exclude;
        pointer-events: none;
    }
}

.profile-right-bottom-button {
    background: linear-gradient(91.29deg, #7AAFFF 1.1%, #7A87FF 95.36%);
    cursor: pointer;
}
</style>
