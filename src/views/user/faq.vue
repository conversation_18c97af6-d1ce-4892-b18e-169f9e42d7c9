<!-- app -->
<template>
	<div class="app-container">
		<div class="main-content px-[0] md:px-[120px]">
			<el-breadcrumb :separator-icon="ArrowRight" class="font-['Open_Sans_3'] font-[600] h-[54px] text-[14px] pt-[8px] mb-[14px] md:h-[66px] md:mb-[10px] px-[12px] md:px-[0]">
				<el-breadcrumb-item class="text-[#1C2158]">Home</el-breadcrumb-item>
				<el-breadcrumb-item>FAQ</el-breadcrumb-item>
			</el-breadcrumb>
			<div class="follow-list-container flex">
				<OrderLeft class="shrink-0 hidden md:block" :activeItem="'FAQ'" />
				<div class="follow-list-right flex-1 md:bg-white md:rounded-[30px] md:p-[40px] overflow-auto">
					<div class="follow-list-right-header flex items-center justify-between px-[12px] md:px-[0]">
						<div class="follow-list-right-header-title text-[18px] leading-[20px] text-[#1C2158] font-['Philosopher'] font-bold md:text-[28px] md:leading-[32px]">FAQ</div>
					</div>
					<div class="follow-list-right-content grid bg-white grid-cols-1 gap-[12px] mt-[20px] md:mt-[23px] md:gap-[20px]">
						<el-collapse v-model="activeNames" @change="handleChange" class="p-[12px]">
							<el-collapse-item title="Consistency" name="1">
								<template #title>
									<img src="@/assets/common/tag.png" alt="" class="absolute w-[18px] top-[22px] h-[18px] mt-[3px] md:w-[26px] md:h-[26px] " />
									<span class="text-[16px] leading-[22px] text-left text-[#1C2158] font-['Open_Sans_3'] font-[600] ml-[22px] md:text-[20px] md:leading-[22px] md:font-bold md:font-['Philosopher'] md:ml-[28px]"> Are your advisors certified? </span>
								</template>
								<template #default>
									<div class="px-[10px] py-[17px] bg-[#F8F8FA] rounded-[8px] text-[14px] leading-[18px] text-[#1C2158] font-['Source_Sans_3'] font-[400] ">ome of them are certfied and licensed.The satisfaction is guaranteed because all our advisors on site have undergone a strict election by StarMet recruitment team.We are making sure that every advisor has the true gifted talent to offer the readings with high-quality</div>
								</template>
							</el-collapse-item>
                            <el-collapse-item title="Consistency" name="2">
								<template #title>
									<img src="@/assets/common/tag.png" alt="" class="absolute w-[18px] top-[22px] h-[18px] mt-[3px] md:w-[26px] md:h-[26px]" />
									<span class="text-[16px] leading-[22px] text-left text-[#1C2158] font-['Open_Sans_3'] font-[600] ml-[22px] md:text-[20px] md:leading-[22px] md:font-bold md:font-['Philosopher'] md:ml-[28px]">just re-login StarMet account,why my history orders and credits on my account are all disappeared? </span>
								</template>
								<template #default>
									<div class="px-[10px] py-[17px] bg-[#F8F8FA] rounded-[8px] text-[14px] leading-[18px] text-[#1C2158] font-['Source_Sans_3'] font-[400]">ome of them are certfied and licensed.The satisfaction is guaranteed because all our advisors on site have undergone a strict election by StarMet recruitment team.We are making sure that every advisor has the true gifted talent to offer the readings with high-quality</div>
								</template>
							</el-collapse-item>
							<!-- <el-collapse-item title="Feedback" name="2">
								<div>Operation feedback: enable the users to clearly perceive their operations by style updates and interactive effects;</div>
								<div>Visual feedback: reflect current state by updating or rearranging elements of the page.</div>
							</el-collapse-item>
							<el-collapse-item title="Efficiency" name="3">
								<div>Simplify the process: keep operating process simple and intuitive;</div>
								<div>Definite and clear: enunciate your intentions clearly so that the users can quickly understand and make decisions;</div>
								<div>Easy to identify: the interface should be straightforward, which helps the users to identify and frees them from memorizing and recalling.</div>
							</el-collapse-item>
							<el-collapse-item title="Controllability" name="4">
								<div>Decision making: giving advices about operations is acceptable, but do not make decisions for the users;</div>
								<div>Controlled consequences: users should be granted the freedom to operate, including canceling, aborting or terminating current operation.</div>
							</el-collapse-item> -->
						</el-collapse>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { ArrowRight } from '@element-plus/icons-vue'
import OrderLeft from '@/components/orderLeft.vue'
const activeNames = ref(['1'])
const handleChange = (val: string[]) => {
	console.log(val)
}
</script>
<style lang="scss" scoped>
.el-collapse {
	border: none;
}
:deep(.el-collapse-item__header){
    // align-items: flex-start;
    position: relative;
}
@media (min-width: 1120px) {
	:deep(.el-collapse-item__content) {
		padding-left: 40px;
	}
	:deep(.el-collapse-item__header) {
		//    height: 64px;
		//    font-size: 16px;
        padding: 26px 0;
		height: max-content;
		
	}
}
@media (max-width: 1120px) {
	:deep(.el-collapse-item__header) {
		border-bottom: none;
		padding: 21px 0;
		height: max-content;
        
	}
}
</style>
