<!-- app -->
<template>
    <div class="app-container">
        <div class="main-content px-[15px] md:px-[15px] lg:px-[120px]">
            <el-breadcrumb :separator-icon="ArrowRight"
                class="font-['Open_Sans_3'] font-[600] h-[54px] text-[14px] pt-[8px] mb-[14px] md:h-[66px] md:mb-[10px]">
                <el-breadcrumb-item class="text-[#1C2158]">Home</el-breadcrumb-item>
                <el-breadcrumb-item>My Card</el-breadcrumb-item>
            </el-breadcrumb>
            <div class="my-card-container flex">
                <OrderLeft class="shrink-0 hidden md:block" :activeItem="'My Card'" />
                <div class="my-card-right flex-1 md:bg-white md:rounded-[30px] md:p-[40px] overflow-auto"
                    v-loading="loading">
                    <div class="my-card-right-header flex items-center justify-between">
                        <div
                            class="my-card-right-header-title text-[18px] leading-[20px] text-[#1C2158] font-['Philosopher'] font-bold md:text-[28px] md:leading-[32px]">
                            My Card</div>
                    </div>
                    <div class="my-card-right-content grid grid-cols-1 gap-[12px] mt-[20px] bg-[#F8F8FA] rounded-[30px] md:grid-cols-2 md:p-[30px] md:mt-[23px] md:gap-[20px]"
                        v-if="couponList.length > 0">
                        <!-- 优惠券1 -->
                        <div class="coupon-card w-full h-[96px] rounded-[8px] md:h-[112px]" v-for="item in couponList"
                            :key="item"
                            :class="item.status == 3 ? 'gray' : item.type == 2 ? 'pink' : (item.type == 1 || item.type == 3 || item.type == 6) ? 'purple' : (item.type == 4 || item.type == 5) ? 'purple-gradient' : ''">
                            <div class="coupon-inner">
                                <div
                                    class="coupon-left flex flex-col items-center justify-center w-[83px] h-[100%] md:w-[96px]">
                                    <div class="coupon-left-bg w-full h-full absolute top-0 left-0 z-[0]">
                                        <img src="@/assets/card/bg-left.png" alt=""
                                            class="w-full h-full object-cover" />
                                    </div>
                                    <div v-if="item.type == 4 || item.type == 5">
                                        <div
                                            class="value relative text-[24px] leading-[33px] text-[#fff] text-center font-['Open_Sans_3'] font-semibold md:text-[30px] md:leading-[41px]">
                                            {{ (100 - item.discountRadio * 100) + '%' }}</div>
                                        <div
                                            class="unit relative text-[12px] leading-[15px] text-[#fff] font-['Source_Sans_3'] font-[400] mt-[7px] text-center md:text-[14px] md:leading-[18px] md:mt-[7px]">
                                            OFF</div>
                                    </div>
                                    <div class="" v-else-if="item.type == 1 || item.type == 3 || item.type == 6">
                                        <div
                                            class="value relative text-[24px] leading-[33px] text-[#fff] font-['Open_Sans_3'] text-center font-semibold md:text-[30px] md:leading-[41px]">
                                            {{ item.duration / 60 }}</div>
                                        <div
                                            class="unit relative text-[12px] leading-[15px] text-[#fff] font-['Source_Sans_3'] font-[400] mt-[7px] text-center md:text-[14px] md:leading-[18px] md:mt-[7px]">
                                            MINUTES<br />FREE</div>
                                    </div>
                                    <div class="" v-else-if="item.type == 2">
                                        <div
                                            class="value relative text-[24px] leading-[33px] text-[#fff] font-['Open_Sans_3'] text-center font-semibold md:text-[30px] md:leading-[41px]">
                                            {{ item.duration / 60 }}</div>
                                        <div
                                            class="unit relative text-[12px] leading-[15px] text-[#fff] font-['Source_Sans_3'] font-[400] mt-[7px] text-center md:text-[14px] md:leading-[18px] md:mt-[7px]">
                                            MINUTES<br />EXTRA</div>
                                    </div>

                                </div>
                                <div
                                    class="coupon-right flex items-center pl-[11px] pr-[8px] justify-between md:pr-[10px]">
                                    <div class="flex flex-col justify-center">
                                        <div
                                            class="title font-['Open_Sans_3'] font-[600] text-[14px] leading-[22px] text-[#1C2158] md:text-[20px] md:leading-[28px]">
                                            {{ returnCardText(item) }}</div>
                                        <div
                                            class="validity font-['Source_Sans_3'] font-[400] text-[12px] leading-[15px] text-[#1C2158] opacity-80 mt-[13px] md:mt-[11px] md:text-[16px] md:leading-[20px]">
                                            Valid until {{ returnDays(item.expireTimestamp) }}</div>
                                    </div>
                                    <button
                                        class="use-btn px-[12px] py-[7px] rounded-[7px] font-['Philosopher'] font-bold bg-white text-[14px] leading-[21px] text-[#4484FF] md:px-[13px] md:py-[11px] md:text-[16px] md:leading-[18px]"
                                        v-if="item.status != 3">Use Now</button>
                                    <img src="@/assets/card/card-expried.png" alt=""
                                        class="w-[58px] h-[58px] md:w-[62px] md:h-[62px]" v-else />
                                </div>
                            </div>
                        </div>
                    </div>
                    <div v-else
                        class="w-full flex flex-col items-center justify-center pt-[94px] md:pt-[36px] md:pb-[17px]">
                        <img src="@/assets/card/no-data.png" alt=""
                            class="w-[200px] h-[200px] md:w-[300px] md:h-[300px]" />
                        <span
                            class="text-[14px] leading-[18px] text-[#1C2158] font-['Source_Sans_3'] font-[400] mt-[16px] mt-[23px] opacity-50 md:text-[16px] md:leading-[22px] md:mt-[21px]">You
                            do not have a free trial card</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { ArrowRight } from '@element-plus/icons-vue'
import OrderLeft from '@/components/orderLeft.vue'
import { activityCardList } from '@/api/user'
import { returnDays } from '@/utils'
const couponList = ref<any[]>([])
const current = ref(1)
const loading = ref(false)
const finished = ref(false)
const getActivityCardList = async () => {
    if (finished.value) return
    loading.value = true
    try {
        const { data, count } = await activityCardList({ isOnlyExpire: 0, current: current.value, size: 100 })
        couponList.value = [...couponList.value, ...data]
        current.value++
        if (couponList.value.length >= count) finished.value = true
    } catch (error) {
        finished.value = true
        console.log(error)
    } finally {
        loading.value = false
    }
}
const returnCardText = (item: any) => {
    const couponsTypeExperience = 1;
    const couponsTypeDuration = 2;
    const couponsTypeLiveText = 3;
    const couponsFirstOrder = 4;
    const couponsSales = 5;
    let title = ''
    if (item.type == couponsTypeExperience) {
        title = 'live Text Chat Only';
    } else if (item.type == couponsTypeDuration) {
        title = 'live Service';
    } else if (item.type == couponsTypeLiveText || item.type == 6) {
        title = 'Live Text Chat Only';
    } else if (item.type == couponsFirstOrder) {
        title = 'All Reading';
    } else if (item.type == couponsSales) {
        if (item.discountType == 3) {
            title = 'All Reading';
        } else if (item.discountType == 2) {
            title = 'live Service';
        } else if (item.discountType == 1) {
            title = 'Text Reading';
        }
    }
    return title
}
    onMounted(() => {
        getActivityCardList()
    })
</script>
<style lang="scss" scoped>
.coupon-card {
    position: relative;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    background-color: #fff;

    &.pink {
        .coupon-left {
            background: url('@/assets/card/extra.png') no-repeat;
            background-size: 100% 100%;
        }
    }

    &.purple {
        .coupon-left {
            background: url('@/assets/card/free.png') no-repeat;
            background-size: 100% 100%;
        }
    }

    &.purple-gradient {
        .coupon-left {
            background: url('@/assets/card/discount.png') no-repeat;
            background-size: 100% 100%;
        }
    }

    &.gray {
        .coupon-left {
            background: url('@/assets/card/expried.png') no-repeat;
            background-size: 100% 100%;
        }
    }

    .coupon-inner {
        position: relative;
        display: flex;
        width: 100%;
        height: 100%;
        background: transparent;
    }

    .coupon-left {
        position: relative;

        &::after {
            content: '';
            position: absolute;
            right: -2px;
            width: 4px;
            height: 94px;
            background-repeat: repeat-y;
            background-size: 4px 6px; // 4px波浪 + 2px间距
            background-image: radial-gradient(circle at 2px 2px, white 2px, transparent 2px);
        }
    }

    @media (max-width: 1120px) {
        .coupon-left {
            position: relative;

            &::after {
                content: '';
                position: absolute;
                right: -2px;
                width: 4px;
                height: 80px;
                background-repeat: repeat-y;
                background-size: 4px 6px; // 4px波浪 + 2px间距
                background-image: radial-gradient(circle at 2px 2px, white 2px, transparent 2px);
            }
        }
    }

    .coupon-right {
        flex: 1;
        position: relative;

        &::before {
            content: '';
            position: absolute;
            width: 12px;
            height: 6px;
            border-radius: 0 0 6px 6px;
            background-color: #f8f8fa;
            left: -6px;
            top: 0px;
            z-index: 1;
        }

        &::after {
            content: '';
            position: absolute;
            width: 12px;
            height: 6px;
            border-radius: 6px 6px 0 0;
            background-color: #f8f8fa;
            left: -6px;
            bottom: 0px;
            z-index: 1;
        }

        .use-btn {
            position: relative;

            &::before {
                content: '';
                inset: 0;
                position: absolute;
                width: 100%;
                height: 100%;
                padding: 1px;
                border-radius: 6px;
                border: 1px solid;
                // background: linear-gradient(91.29deg, rgba(122, 175, 255, 0.1) 1.1%, rgba(122, 135, 255, 0.1) 95.36%);
                -webkit-mask-composite: xor;
                mask-composite: exclude;
                pointer-events: none;
            }
        }
    }
}
</style>
