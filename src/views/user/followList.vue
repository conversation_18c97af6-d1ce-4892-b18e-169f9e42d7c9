<!-- app -->
<template>
    <div class="app-container">
        <div class="main-content px-[15px] md:px-[15px] lg:px-[120px]">
            <el-breadcrumb :separator-icon="ArrowRight"
                class="font-['Open_Sans_3'] font-[600] h-[54px] text-[14px] pt-[8px] mb-[14px] md:h-[66px] md:mb-[10px]">
                <el-breadcrumb-item class="text-[#1C2158]">Home</el-breadcrumb-item>
                <el-breadcrumb-item>Follow List</el-breadcrumb-item>
            </el-breadcrumb>
            <div class="follow-list-container flex">
                <OrderLeft class="shrink-0 hidden md:block" :activeItem="'Follow List'" />
                <div class="follow-list-right flex-1 md:bg-white md:rounded-[30px] md:p-[40px] overflow-auto"
                    v-loading="loading">
                    <div class="follow-list-right-header flex items-center justify-between">
                        <div
                            class="follow-list-right-header-title text-[18px] leading-[20px] text-[#1C2158] font-['Philosopher'] font-bold md:text-[28px] md:leading-[32px]">
                            Follow List</div>
                    </div>
                    <div class="follow-list-right-content grid grid-cols-1 gap-[12px] mt-[20px] rounded-[30px]  md:mt-[23px] md:gap-[20px]"
                        v-if="followList.length > 0">
                        <div class="follow-list-item flex items-center justify-between p-[8px] bg-[#fff] rounded-[10px] md:px-[20px] md:py-[15px] md:gap-[20px] md:rounded-[16px] md:bg-[#F8F8FA]"
                            v-for="item in followList" :key="item.id">
                            <div class="follow-list-item-avatar flex items-center">
                                <img :src="item.avatar" alt=""
                                    class="w-[54px] h-[54px] rounded-[12px] mr-2 md:w-[80px] md:h-[80px] object-cover md:mr-5" />
                                <div class="follow-list-item-avatar-right">
                                    <div
                                        class="follow-list-item-avatar-right-name text-[16px] leading-[22px] text-[#1C2158] font-['Open_Sans_3'] font-semibold md:text-[20px] md:leading-[22px]">
                                        {{ item.username }}</div>
                                    <div
                                        class="follow-list-item-avatar-right-description text-[12px] mt-[6px] leading-[15px] line-clamp-1 md:text-[16px] md:leading-[20px] md:mt-[14px] text-[#1C2158] font-['Source_Sans_3'] font-[400] opacity-80">
                                        {{ item.introduction }}</div>
                                </div>
                            </div>
                            <div class="follow-list-item-right ml-[17px] shrink-0">
                                <div class="follow-list-item-right-button w-[88px] h-[34px] flex items-center justify-center rounded-[8px] text-[16px] text-[#fff] leading-[18px] font-['Philosopher'] font-bold md:w-[102px] md:h-[42px] md:text-[18px] md:leading-[20px]"
                                    :class="{ 'followed': item.isFollowed, 'unfollowed': !item.isFollowed }">
                                    {{ item.isFollowed ? 'Followed' : '+ Follow' }}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div v-else
                        class="w-full flex flex-col items-center justify-center pt-[94px] md:pt-[36px] md:pb-[17px]">
                        <img src="@/assets/common/no-follow.png" alt=""
                            class="w-[200px] h-[200px] md:w-[300px] md:h-[300px]" />
                        <span
                            class="text-[14px] leading-[18px] text-[#1C2158] font-['Source_Sans_3'] font-[400] text-center mt-[23px] opacity-50 md:text-[16px] md:leading-[22px] md:mt-[21px]">You
                            have no favorite advisor yet, browse advisor listand we are sure you will find the
                            one</span>
                    </div>
                    <div class="show-more-btn flex items-center justify-center mt-[18px] px-[27px] md:px-[290px] md:mt-[40px]"
                        v-if="!finished && followList.length" @click="getFollowListData">
                        <div
                            class="show-more-btn-text w-full py-[14px] rounded-[10px] text-[20px] leading-[20px]  font-['Philosopher'] font-[400] text-center text-white">
                            Show More</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { ArrowRight } from '@element-plus/icons-vue'
import OrderLeft from '@/components/orderLeft.vue'
import { getFollowList } from '@/api/user'
const current = ref(1)
const size = ref(10)
const loading = ref(false)
const finished = ref(false)
const getFollowListData = async () => {
    if(loading.value) return
    try {
        loading.value = true
        const res = await getFollowList({ current: current.value, size: size.value })
        res.data.forEach((item: any) => {
            item.isFollowed = true
        })
        followList.value = [...followList.value, ...res.data]
        if(res.data.length < size.value){
            finished.value = true
        }
        current.value++
    } catch (error) {
        console.log(error)
        finished.value = true
    } finally {
        loading.value = false

    }
}
onMounted(() => {
    getFollowListData()
})
const followList = ref<any[]>([])
</script>
<style lang="scss" scoped>
.followed {
    background: linear-gradient(91.29deg, #BDC6D2 1.1%, #B4B7CC 95.36%);

}

.unfollowed {
    background: linear-gradient(91.29deg, #7AAFFF 1.1%, #7A87FF 95.36%);

}

.follow-list-item {
    cursor: pointer;

    &:hover {
        box-shadow: 0px 0px 10px 0px rgba(2, 10, 20, 0.1);
    }
}
.show-more-btn-text {
	cursor: pointer;
	background: linear-gradient(91.29deg, #7aafff 1.1%, #7a87ff 95.36%);
	&:hover {
		box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.3);
	}
}
</style>
