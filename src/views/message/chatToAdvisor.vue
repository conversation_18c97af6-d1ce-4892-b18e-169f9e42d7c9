<!--  -->
<template>
    <div class="app-container relative">
        <div class="main-content md:px-[320px] md:pt-[40px]">
            <div class="breadcrumb-container h-[35px] px-[15px] white-nowrap bg-[#2a2620] md:hidden lg:hidden">
                <el-breadcrumb :separator-icon="ArrowRight"
                    class="font-['Open_Sans_3'] font-[600] text-[14px] pt-[8px]">
                    <el-breadcrumb-item class="text-[#1C2158]">Home</el-breadcrumb-item>
                    <el-breadcrumb-item class="text-[#1C2158]">Family &Friends</el-breadcrumb-item>
                    <el-breadcrumb-item class="text-[#1C2158]">{{ augurDetail.username || '...' }}</el-breadcrumb-item>
                    <el-breadcrumb-item>Live Text Chat</el-breadcrumb-item>
                </el-breadcrumb>
            </div>

            <div class="chat-container relative">
                <Transition enter-active-class="transition duration-200 ease-out" enter-from-class="opacity-0 scale-95"
                    enter-to-class="opacity-100 scale-100" leave-active-class="transition duration-150 ease-in"
                    leave-from-class="opacity-100 scale-100" leave-to-class="opacity-0 scale-95">
                    <div v-if="showTip"
                        class="tip absolute w-full px-[15px] top-[40px] left-0 right-0 m-auto z-[1] bg-[#F8F8FA]  md:top-[80px] py-[10px] md:py-[15px]"
                        v-show="showTip">
                        <div class="w-full px-[20px] py-[11px] rounded-[8px] bg-[#FFDADA] flex items-center">
                            <img src="@/assets/chat/warning.png" class="w-[30px] h-[30px] mr-[16px]" />
                            <div
                                class="font-['Source_Sans_3'] text-[12px] leading-[14px] text-[#1C2158] md:text-[16px] md:leading-[22px] md:font-semibold">
                                To protect privacy from being disclosed please do not provide or exchange any personal
                                contact information with advisors.</div>
                            <img src="@/assets/chat/close.png"
                                class="absolute right-[19px] top-[14px] w-[14px] h-[14px] md:top-[20px] md:right-[20px]"
                                @click="showTip = false" />
                        </div>
                    </div>
                </Transition>

                <div
                    class="chat-header h-[44px] bg-white shrink-0 pr-[15px] pl-[7px] flex items-center justify-between md:h-[80px]">
                    <!-- 头部内容 -->
                    <div class="chat-header-left flex items-center">
                        <div>
                            <img src="@/assets/common/arrow-right.png"
                                class="w-[24px] h-[24px] mr-[14px] md:w-[30px] md:h-[30px] md:mr-[15px]"
                                @click="goBack" />
                        </div>
                        <div class="flex items-center">
                            <img :src="augurDetail.avatar"
                                class="w-[28px] h-[28px] rounded-[50%] mr-[8px] md:w-[50px] md:h-[50px] md:mr-[15px]" />
                            <div class="">
                                <p
                                    class="font-['Philosopher'] text-[18px] font-blod leading-[20px] md:text-[26px] md:leading-[28px]">
                                    {{ augurDetail.username || '...' }}</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="chat-box relative" ref="messagesRef" :class="{ 'pt-[70px] md:pt-[70px]': showTip }">
                    <!-- 顶部加载提示 -->
                    <div v-if="loadingMore" class="load-more-loading">loading...</div>
                    <div v-else-if="isCompleted" class="load-more-finished">no more message....</div>
                    <!-- 消息列表渲染 -->
                    <div v-for="msg in currentMessages" :key="msg.ID" class="message-wrapper"
                        :class="[msg.flow === 'out' ? 'is-me' : 'is-other']">
                        <img class="avatar" :src="msg.flow === 'out' ? userInfo.avatar : augurDetail.avatar"
                            alt="avatar" />
                        <div class="content-row flex items-center">
                            <template v-if="msg.flow === 'out'">
                                <span v-if="msg.sendStatus === 'pending'"
                                    class="msg-status-icon mr-2 flex items-center">
                                    <el-icon :size="22">
                                        <Loading />
                                    </el-icon>
                                </span>
                                <span v-else-if="msg.sendStatus === 'fail'"
                                    class="msg-status-icon mr-2 flex items-center" style="cursor:pointer;"
                                    @click="retrySend(msg)">
                                    <el-icon color="red" :size="22">
                                        <WarningFilled />
                                    </el-icon>
                                </span>
                            </template>
                            <div class="content" :data-send-status="msg.sendStatus">
                                <div class="text-bubble" v-if="msg.type === 'TIMTextElem'">
                                    {{ msg.payload.text }}
                                </div>
                                <div class="image-bubble" v-else-if="msg.type === 'TIMImageElem'">
                                    <img :src="msg.payload.imageInfoArray[0]?.url"
                                        style="max-width: 180px; border-radius: 8px;" />
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="show-purchase flex justify-center" v-if="!imServiceTimes">
                        <div
                            class="bg-[rgba(190,213,255,.7)] max-w-[500px]  font-['Philosopher'] text-[14px] leading-[22px] text-[#1C2158] rounded-[8px] px-[10px] py-[10px] flex items-center justify-between">
                            <span>
                                Free chat times has been used up, purchase the service to continue.
                            </span>
                            <button
                                class="purchase-btn shrink-0 bg-[#fff] text-[#1C2158] px-[10px] py-[5px] rounded-[8px] border-[1px] border-[#4484FF]">
                                Connect Now
                            </button>
                        </div>
                    </div>
                </div>
                <div class="chat-bottom shrink-0 bg-white pt-[10px] px-[10px] pb-[40px]"
                    :class="{ 'pb-[5px]': showQuickReply }">
                    <div class="flex  md:flex-col">
                        <img src="@/assets/chat/show-quick.png" class="w-[30px] h-[30px] mr-[10px]"
                            @click="showQuickReplyFunc" />
                        <div class="flex-1 mr-[6px]">
                            <input type="text" v-model="message"
                                class="w-full h-[38px] rounded-[8px] bg-[#F8F8FA] pl-[10px] pr-[10px] text-[16px] leading-[38px] text-[#1C2158] outline-none md:hidden"
                                :placeholder="placeholder" @keydown.enter="handleSend($event)" />
                            <textarea v-model="message"
                                class="w-full hidden pl-[10px] pr-[10px] text-[16px] text-[#1C2158] outline-none resize-none md:h-full md:mt-[10px] md:bg-white md:block"
                                :placeholder="placeholder" @keydown.enter="handleSend($event)"></textarea>
                        </div>
                        <img src="@/assets/chat/send.png" class="w-[34px] h-[34px] md:hidden"
                            @click="handleSend(undefined)" />
                    </div>
                    <!-- 快捷回复 -->
                    <div class="quick-reply overflow-y-auto px-[5px] transition-all duration-300 ease-out"
                        :style="{ maxHeight: showQuickReply ? '200px' : '0px' }" v-show="true">
                        <div class="quick-reply-item mt-[10px] rounded-[8px] bg-[rgba(190,213,255,.3)] px-[10px] py-[5px] text-[14px] leading-[18px] text-[#1C2158] cursor-pointer md:text-[16px] md:leading-[22px]"
                            v-for="item in imServiceQuickReply" :key="item" @click="handleQuickReply(item)">
                            {{ item }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue'
import { ArrowRight, Loading, WarningFilled } from '@element-plus/icons-vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { useLayoutStore } from '@/stores/layout'
import { useIMStore } from '@/stores/im'
import { storeToRefs } from 'pinia'
import { useThrottleFn } from '@vueuse/core'
import { getAugurDetail } from '@/api/augur'
// import { ElMessage } from 'element-plus'
import SmMessage from '@/components/Message'
import { getImServiceTimes, sendImMessage, getImServiceQuickReply } from '@/api/imService'

// -------------------- 数据与状态 --------------------
const route = useRoute()
const router = useRouter()
const useLayout = useLayoutStore()
const userStore = useUserStore()
const imStore = useIMStore()
const { currentMessages } = storeToRefs(imStore)
const userInfo = computed(() => userStore.userInfo)
const augurDetail = ref<any>({})
const augurUid = ref(route.query.uid as string)
const showTip = ref(true)
const message = ref('')
const fileInput = ref<HTMLInputElement | null>(null)
const nextReqMessageID = ref<string | undefined>(undefined)
const isCompleted = ref(false)
const loadingMore = ref(false)

const imServiceTimes = ref<number>(0)
const imServiceQuickReply = ref<string[]>([])
const showQuickReply = ref(false)
const placeholder = ref('Say something to the advisor ~')
let isUserAtBottom = true

// -------------------- 工具函数 --------------------
function scrollToBottom() {
    nextTick(() => {
        const box = document.querySelector('.chat-box') as HTMLElement
        if (box && isUserAtBottom) box.scrollTop = box.scrollHeight
    })
}
function handleBoxScroll() {
    const box = document.querySelector('.chat-box') as HTMLElement
    if (!box) return
    isUserAtBottom = box.scrollHeight - box.scrollTop - box.clientHeight < 40
}

// -------------------- 业务逻辑 --------------------
const getAugurDetailFunc = async () => {
    try {
        const res = await getAugurDetail({ augurUid: augurUid.value })
        augurDetail.value = res.data
    } catch (error) {
        console.log(error)
    }
}
const handleSend = async (e?: KeyboardEvent) => {
    if (!e || !e.shiftKey) {
        e && e.preventDefault()
        const content = message.value.trim()
        if (!content) return
        try {
            if (imServiceTimes.value > 0) {
                await sendImMessage({
                    augurUid: augurUid.value,
                    content: content,
                    messageBO: {
                        augurUid: augurUid.value,
                        content: content,
                    }
                }).then(async res => {
                    await imStore.sendMessage(augurUid.value, content, 'C2C')
                    message.value = ''
                    imServiceTimes.value--
                    scrollToBottom()
                }).catch(err => {
                    console.log('发送消息失败', err)
                })
            } else {
                SmMessage.warning('The advisor is currently on a break, please proceed to other consultants for consultation')
                return
            }

        } catch (err) {
            SmMessage.error('send message failed, please try again')
        }
    }
}
const handleQuickReply = async (item: string) => {
    if (imServiceTimes.value > 0) {
        await sendImMessage({
            augurUid: augurUid.value,
            content: item,
            messageBO: {
                augurUid: augurUid.value,
                content: item,
            }
        }).then(async res => {
            await imStore.sendMessage(augurUid.value, item, 'C2C')
            imServiceTimes.value--
            scrollToBottom()
        }).catch(err => {
            console.log('发送消息失败', err)
        })
    } else {
        SmMessage.warning('The advisor is currently on a break, please proceed to other consultants for consultation')
        return
    }
}
const retrySend = async (msg: any) => {
    try {
        await imStore.resendMessage(msg)
    } catch (err) {
        SmMessage.error('重发失败，请检查网络后重试')
    }
}
const handleResize = useThrottleFn(() => {
    const layoutElement = document.getElementsByClassName('layout')[0] as HTMLElement
    if (layoutElement) {
        layoutElement.style.height = '100vh'
    }
    if (window.innerWidth < 1120) {
        useLayout.setShowFooter(false)
    } else {
        useLayout.setShowFooter(true)
    }
}, 200)
async function handleScrollTop() {
    const box = document.querySelector('.chat-box') as HTMLElement
    if (!box || loadingMore.value || isCompleted.value) return
    if (box.scrollTop <= 0) {
        loadingMore.value = true
        const oldHeight = box.scrollHeight
        try {
            if (!imStore.currentConversationID) return
            const res = await imStore.getMessageList(imStore.currentConversationID, nextReqMessageID.value)
            nextReqMessageID.value = res.nextReqMessageID
            isCompleted.value = res.isCompleted
            await nextTick()
            box.scrollTop = box.scrollHeight - oldHeight
        } finally {
            loadingMore.value = false
        }
    }
}
const goBack = () => {
    router.go(-1)
}


const getImServiceTimesFunc = async () => {
    const res = await getImServiceTimes()
    imServiceTimes.value = res.data.coinChatTimes + res.data.freeCard
}

//得到快捷回复模版
const getImServiceQuickReplyFunc = async () => {
    const res = await getImServiceQuickReply()
    console.log('快捷回复模版', res)
    imServiceQuickReply.value = res.data
}

const showQuickReplyFunc = () => {
    showQuickReply.value = !showQuickReply.value
}
// -------------------- 生命周期与事件 --------------------
onMounted(async () => {
    getImServiceQuickReplyFunc()
    getImServiceTimesFunc()
    getAugurDetailFunc()
    handleResize()
    window.addEventListener('resize', handleResize)
    // 自动登录IM
    if (!imStore.isLogin) {
        await imStore.login(String(userInfo.value.uid), userStore.userSig)
    }
    // 设置当前C2C会话
    const toUserId = augurUid.value
    const conversationID = `C2C${toUserId}`
    await imStore.setCurrentConversation(conversationID)
    // 拉取首屏历史消息
    const res = await imStore.getMessageList(conversationID)
    nextReqMessageID.value = res.nextReqMessageID
    isCompleted.value = res.isCompleted
    // 自动滚动到底部
    watch(currentMessages, (newVal) => {

        scrollToBottom()
    }, { immediate: true, deep: true })
    // 监听滚动事件实现下拉加载和底部判断
    nextTick(() => {
        const box = document.querySelector('.chat-box') as HTMLElement
        if (box) {
            box.addEventListener('scroll', handleScrollTop)
            box.addEventListener('scroll', handleBoxScroll)
        }
    })
})
onUnmounted(() => {
    useLayout.setShowFooter(true)
    window.removeEventListener('resize', handleResize)
    const box = document.querySelector('.chat-box') as HTMLElement
    if (box) {
        box.removeEventListener('scroll', handleScrollTop)
        box.removeEventListener('scroll', handleBoxScroll)
    }

})
</script>
<style lang="scss" scoped>
@media screen and (max-width: 1120px) {
    .app-container {
        display: flex;
        flex-direction: column;
        overflow: hidden;
        background-color: #f8f8fa;

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;

            .breadcrumb-container {
                flex-shrink: 0;
            }

            .chat-container {
                flex: 1;
                display: flex;
                flex-direction: column;
                overflow: hidden;

                .chat-header {
                    flex-shrink: 0;
                }

                .chat-box {
                    flex: 1;
                    overflow-y: auto;
                    padding-left: 15px;
                    padding-right: 15px;
                }

                .chat-bottom {
                    width: 100%;
                    min-height: 92px;
                    background-color: #fff;
                    flex-shrink: 0;
                }
            }
        }
    }
}

@media screen and (min-width: 1120px) {
    .chat-container {
        width: 100%;
        height: 633px;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        background-color: #fff;
        border-radius: 20px;

        .chat-box {
            flex: 1;
            overflow-y: scroll;
            background-color: #f8f8fa;
            padding: 20px;
        }

        .chat-bottom {
            // height: 170px;
            align-items: normal;
        }
    }
}

.app-container {
    :deep(.el-breadcrumb) {
        display: flex;
        flex-wrap: nowrap;
        overflow-x: auto;

        .el-breadcrumb__item {
            flex-shrink: 0;

            .el-breadcrumb__inner {
                color: #fff !important;
                font-family: 'Philosopher';
            }
        }
    }
}

.chat-header-right {
    &::before {
        content: '';
        position: absolute;
        inset: 0;
        width: 100%;
        height: 100%;
        padding: 1px;
        border-radius: 6px;
        background: linear-gradient(104.04deg, #96baff 0%, #7064ff 86.39%);
        -webkit-mask: linear-gradient(#96baff 0 0) content-box, linear-gradient(#96baff 0 0);
        -webkit-mask-composite: xor;
        mask-composite: exclude;
        pointer-events: none;
    }
}

// 聊天消息样式
.message-wrapper {
    display: flex;
    margin-bottom: 20px;

    &.is-me {
        flex-direction: row-reverse;

        .avatar {
            margin-left: 10px;
        }

        .content-row {
            justify-content: flex-end;
        }

        .text-bubble {
            background: #fff;
            color: #1C2158;
            border-top-right-radius: 4px;
        }
    }

    &.is-other {
        flex-direction: row;

        .avatar {
            margin-right: 10px;
        }

        .content-row {
            justify-content: flex-start;
        }

        .text-bubble {
            background: #BED5FF;
            color: #1C2158;
            border-top-left-radius: 4px;
        }
    }

    .avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        object-fit: cover;
        flex-shrink: 0;
    }

    .content-row {
        display: flex;
        align-items: center;
        flex: 1;
        min-width: 0;
    }

    .content {
        display: flex;
        flex-direction: column;
        max-width: 90%;
        min-width: 0;
    }

    .text-bubble {
        padding: 10px 15px;
        border-radius: 18px;
        font-size: 16px;
        line-height: 1.5;
        word-break: break-word;
    }
}

.msg-status-icon {
    display: flex;
    align-items: center;
    font-size: 18px;
}

.load-more-loading {
    text-align: center;
    color: #888;
    padding: 8px 0;
    font-size: 14px;
}

.load-more-finished {
    text-align: center;
    color: #bbb;
    padding: 8px 0;
    font-size: 14px;
}
</style>
