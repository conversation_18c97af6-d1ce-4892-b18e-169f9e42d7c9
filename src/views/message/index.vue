<!--  -->
<template>
    <div class="app-container">
        <div class="main-content px-[15px] lg:px-[120px]">
            <el-breadcrumb :separator-icon="ArrowRight"
                class="font-['Open_Sans_3'] font-[600] text-[14px] pt-[8px] mb-[14px] md:pt-6 lg:pt-6 md:mb-[30px] lg:mb-[30px]">
                <el-breadcrumb-item class="text-[#1C2158]">Home</el-breadcrumb-item>
                <el-breadcrumb-item>Message</el-breadcrumb-item>
            </el-breadcrumb>
            <div class="mt-[26px] font-['Philosopher'] font-[19px] font-blod mb-[12px]">Message</div>
            <div class="read-list">
                <div class="read-item flex cursor-pointer justify-between p-[10px] rounded-[10px] bg-white mt-[6px] lg:mt-[20px] lg:p-[20px]"
                    v-for="item in conversationList" :key="item.conversationID" @click="handleClick(item)">
                    <div class="read-item-left flex items-center">
                        <div class="relative flex-shrink-0">
                            <img :src="item.userProfile.avatar" alt=""
                                class="w-[50px] h-[50px] rounded-[50%] object-cover lg:w-[60px] lg:h-[60px]" />
                            <div class="onlines-tatus absolute bottom-0 right-[2px] w-[12px] h-[12px] rounded-[50%] bg-[#3AD953] border-1 border-[#fff] border-solid"
                                v-if="item.lastMessage.onlineOnlyFlag">
                            </div>
                            <div class="offline-status absolute bottom-0 right-[2px] w-[12px] h-[12px] rounded-[50%] bg-[#BFC3C5] border-1 border-[#fff] border-solid"
                                v-else>
                            </div>
                        </div>

                        <div class="read-item-left-detail ml-[10px]">
                            <p
                                class="font-['Open_Sans'] text-[16px] font-semibold leading-[22px] lg:leading-[25px] lg:text-[18px]">
                                {{ item.userProfile.nick }}</p>
                            <span
                                class="font-['Source_Sans_Pro'] text-[14px] mt-[7px] text-[#1C2158B2] leading-[18px] line-clamp-1 lg:text-[18px] lg:mt-[10px] lg:leading-[23px]">{{
                                    item.lastMessage.messageForShow }}</span>
                        </div>
                    </div>
                    <div class="read-item-right flex flex-col items-end ml-[15px] whitespace-nowrap">
                        <span
                            class="text-[#1C215866] text-[12px] mt-[7px] leading-[15px] lg:leading-[20px] lg:text-[16px]">{{
                                formatChatTime(item.lastMessage.lastTime) }}</span>
                        <div class="no-read mt-[12px] w-[19px] h-[19px] shrink-0 rounded-[50%] bg-[#F53A3A] text-white text-[14px] flex justify-center items-center lg:w-[30px] lg:h-[30px] lg:mt-[8px] lg:text-[18px]"
                            v-if="item.unreadCount > 0">
                            {{ item.unreadCount > 99 ? 99 : item.unreadCount }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, nextTick, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ArrowRight } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'
import { useIMStore } from "@/stores/im";
import { formatChatTime } from '@/utils'
import { storeToRefs } from 'pinia'
import TIM from "@tencentcloud/chat";

// --------------------------------- Store & Router ---------------------------------
const userStore = useUserStore()
const imStore = useIMStore();
const route = useRoute()
const router = useRouter()
const showFree = computed(() => userStore.showFree)
const userInfo = computed(() => userStore.userInfo)
const userSig = computed(() => userStore.userSig)
const conversationList = ref<any>([])
const { messageList } = storeToRefs(imStore)

watch(() => messageList.value, (newVal) => {
    console.log('messageList', newVal)
    for (var key in newVal) {
        console.log('key', key)
        const conversation = conversationList.value.find((conversation: any) => conversation.conversationID === key)
        const firstMessage  = newVal[key][0]
        if(conversation){
            conversation.lastMessage.lastTime = firstMessage.clientTime
            if(firstMessage.type==TIM.TYPES.MSG_TEXT){
                conversation.lastMessage.messageForShow = firstMessage.payload.text
            }else if(firstMessage.type==TIM.TYPES.MSG_IMAGE){
                conversation.lastMessage.messageForShow = 'Image'
            }else if(firstMessage.type==TIM.TYPES.MSG_VIDEO){
                conversation.lastMessage.messageForShow = 'Video'
            }else if(firstMessage.type==TIM.TYPES.MSG_FILE){
                conversation.lastMessage.messageForShow = 'File'
            }else if(firstMessage.type==TIM.TYPES.MSG_CUSTOM){
                conversation.lastMessage.messageForShow = 'Custom'
            }else if(firstMessage.type==TIM.TYPES.MSG_LOCATION){
                conversation.lastMessage.messageForShow = 'Location'
            }else if(firstMessage.type==TIM.TYPES.MSG_GRP_TIP){
                conversation.lastMessage.messageForShow = 'Group Tips'
            }
            conversation.unreadCount = conversation.unreadCount+1
        }
    }
}, { deep: true })

const handleClick = (item: any) => {
    router.push({
        name: 'ChatToAdvisor',
        query: {
            uid: item.userProfile.userID,
            username: item.userProfile.nick,
        }
    })
}
const initIm = async () => {
    // 1. 登录并等待SDK Ready
    if (!imStore.isLogin) await imStore.login(String(userInfo.value.uid), userSig.value);
    setTimeout(async () => {
        const res = await imStore.getConversationList();
        console.log(res)
        conversationList.value = res.data.conversationList
    }, 1000)
}

onMounted(() => {
    initIm()
})
</script>
<style lang="scss" scoped>
.read-item {
    &:hover {
        background-color: #F4F4F4;
        box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.25);
    }
}
</style>
