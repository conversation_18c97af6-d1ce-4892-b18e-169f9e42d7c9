<!--  -->
<template>
    <div class="main-content-item mt-[24px] lg:mt-[30px]">
        <div
            class="main-content-title text-[#1C2158] font-['Philosopher'] text-[18px] font-blod lg:text-[28px] lg:leading-[60px]">
            {{ title }}</div>
        <div class="main-content-list-container relative">
            <div ref="mainItemRef" class="main-content-list w-auto relative mt-5 flex gap-2.5 lg:gap-5 overflow-x-auto">
                <div class="main-content-list-item shrink-0 w-[146px] lg:w-[calc((100%-4*10px)/5)] rounded-[8px] lg:rounded-[14px] relative cursor-pointer"
                    v-for="item in dataList" :key="item.id" @click="handleClick(item)">
                    <div class="absolute flex p-2.5 w-full items-center justify-between">
                        <div class="rate flex items-center px-2 py-0.5">
                            <img src="@/assets/home/<USER>" alt="" class="w-3.5 f-3.5 mr-0.5" />
                            <span class="text-sm text-white">{{ item.grade.toFixed(1) }}</span>
                        </div>
                        <div class="online border border-solid border-white w-3.5 h-3.5"
                            v-if="item.state == 4 "></div>
                        <div class="offline border border-solid border-white w-3.5 h-3.5" v-if="item.state == 1 || item.state == 2"></div>
                        <div class="busy border border-solid border-white w-3.5 h-3.5" v-if="item.state == 3"></div>
                    </div>
                    <img :src="item.avatar" alt=""
                        class="w-full h-[146px] md:w-full md:h-[224px] object-cover object-top rounded-tl-[8px] rounded-tr-[8px] lg:rounded-tl-[14px] lg:rounded-tr-[14px]" />
                    <div class="main-content-list-item-bottom px-3 mt-2.5 pb-3 w-full">
                        <div class="font-extrabold text-[#1C2158] font-['Philosopher'] text-sm truncate lg:text-xl">{{ item.username }}</div>
                        <p
                            class="main-content-list-item-bottom-desc mt-[5px] h-[30px] text-[#1C2158] opacity-50 text-xs line-clamp-2 lg:h-[40px] lg:text-sm">
                            {{ item.introduction }}</p>
                        <div class="main-content-list-item-bottom-bottom flex items-center mt-1.5 font-['Philosopher']">
                            <span class="text-lg mr-1 text-[#4484FF] lg:text-xl">{{ item.commentTimes }}</span>
                            <span class="text-sm text-[#1C2158] opacity-70 lg:text-base">Readings</span>
                        </div>
                        <div class="connect-button font-['Philosopher']">
                            <button v-if="userInfo.hasExperienceCard && item.state == 4"
                                class="sm-button free-button w-full py-[9px] leading-[16px] text-base text-center text-white mt-[8px] lg:mt-[13px] lg:py-[11px] lg:text-lg"
                                @click.stop="createOrder(item)">Free Chat</button>
                            <button v-else-if="!userInfo.hasExperienceCard && userInfo.giftBagRemainingSeconds"
                                class="sm-button pay-button w-full py-[9px] leading-[16px] text-base text-center text-white mt-[8px] lg:mt-[13px] lg:py-[11px] lg:text-lg"
                                @click.stop="handleClick(item)">$1.99 for 6min</button>
                            <button v-else
                                class="sm-button default-button w-full py-[9px] leading-[16px] text-base text-center text-white mt-[8px] lg:mt-[13px] lg:py-[11px] lg:text-lg"
                                @click.stop="handleClick(item)">Connect Now</button>
                        </div>
                    </div>
                    <!-- <img :src="returnNum(item.toString())" alt="" class="absolute w-[200px] h-[200px] right-0 top-[70px]" v-if="showNum" /> -->
                </div>
            </div>
            <!-- 将 free.png 图片移到滚动容器外部 -->
            <img src="@/assets/common/free.png" alt=""
                class="absolute hidden w-[200px] h-[200px] right-0 top-[70px] lg:block" v-if="showFree" />
            <img src="@/assets/home/<USER>" alt=""
                class="absolute hidden w-[52px] h-[78px] right-0 top-[145px] lg:block cursor-pointer"
                v-if="dataList.length > 5" @click="mainItemTransLationX" />
        </div>
    </div>
</template>

<script lang="ts" setup name="MainItem">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'

import { useUserStore } from '@/stores/user'

const userStore = useUserStore()
const userInfo = computed(() => {
    return userStore.userInfo
})
const router = useRouter()

interface Props {
    title: string
    dataList: Array<any>
    showFree?: boolean
    showNum?: boolean
}

const mainItemRef = ref()
const scrollPosition = ref(0)
const { title, showFree = true, showNum = false, dataList } = defineProps<Props>()

const handleClick = (item: any) => {

    router.push({
        path: '/familyfriends',
        query: {
            id: item.uid,
        },
    })
}

const createOrder = (item: any) => {
    console.log(item)
    const augurFortuneId = item.augurFortune.filter((item: any) => item.augurFortuneGroupId == 4)[0].id
    router.push({
        path: '/createOrder',
        query: {
            uid: item.uid,
            username: item.username,
            augurFortuneGroupId: 4,
            augurFortuneId: augurFortuneId
        },
    })
}

const mainItemTransLationX = () => {
    if (!mainItemRef.value) return

    const scrollContainer = mainItemRef.value
    const scrollAmount = 300 // 每次滚动的距离
    const maxScroll = scrollContainer.scrollWidth - scrollContainer.clientWidth

    // 如果已经滚动到最右边，回到开始位置
    if (scrollPosition.value >= maxScroll) {
        scrollPosition.value = 0
    } else {
        scrollPosition.value += scrollAmount
        // 确保不超过最大滚动距离
        if (scrollPosition.value > maxScroll) {
            scrollPosition.value = maxScroll
        }
    }

    // 使用平滑滚动
    scrollContainer.scrollTo({
        left: scrollPosition.value,
        behavior: 'smooth',
    })
}
</script>
<style lang="scss" scoped>
.main-content-list-container {
    position: relative;
    width: 100%;
}

.main-content-list {
    // 确保容器有合适的滚动行为
    scrollbar-width: thin;
    scrollbar-color: #dcdde5 transparent;
    scroll-behavior: smooth;

    &::-webkit-scrollbar {
        height: 4px;
    }

    &::-webkit-scrollbar-track {
        background: transparent;
    }

    &::-webkit-scrollbar-thumb {
        background: #dcdde5;
        border-radius: 2px;
    }

    // 添加过渡效果
    transition: scroll-left 0.3s ease;
}

.main-content-list-item {
    box-shadow: 0px 1px 5px 0px #dcdde5;
    background-color: #fff;
    margin-bottom: 1px;

    &:hover {
        box-shadow: 0px 0px 15px 0px #5f6bd3b2;
        transition: all 0.3s;
    }

    .online {
        border-radius: 50%;
        background-color: #3ad953;
    }

    .offline {
        background-color: #BFC3C5;
        border-radius: 50%;
    }

    .busy {
        background-color: #ea3232;
        border-radius: 50%;
    }

    .rate {
        background: linear-gradient(91.29deg, #7aafff 1.1%, #7a87ff 95.36%);
        border-radius: 22px;
    }

    .main-content-list-item-bottom-desc {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .connect-button {
        .sm-button {
            border-radius: 8px;

            &.default-button {
                background: linear-gradient(91.29deg, #7aafff 1.1%, #7a87ff 95.36%);
            }

            &.free-button {
                background: linear-gradient(90deg, #69cf5f 0%, #0daa53 100%);
            }

            &.pay-button {
                background: linear-gradient(270deg, #fd4444 3.23%, #ff8f5f 100%);
            }
        }
    }
}

// @media screen and (min-width: 1024px) {
.main-content-list-item {
    width: calc((100% - 4 * 10px) / 5);
    min-width: 146px;
    // }
}
</style>
