<!--  -->
<template>
    <div class="home-container">
        <main class="home-main">
            <!-- Hero Section with Carousel -->
            <section class="hero-section">
                <el-carousel class="hero-carousel" :interval="5000" :autoplay="true" indicator-position="none">
                    <el-carousel-item>
                        <img src="@/assets/home/<USER>" alt="Hero Banner 1" class="hero-image" />
                    </el-carousel-item>
                    <el-carousel-item>
                        <img src="@/assets/home/<USER>" alt="Hero Banner 2" class="hero-image" />
                    </el-carousel-item>
                    <el-carousel-item>
                        <img src="@/assets/home/<USER>" alt="Hero Banner 3" class="hero-image" />
                    </el-carousel-item>
                </el-carousel>

                <!-- Hero Content Overlay -->
                <div class="hero-content">
                    <h1 class="hero-title">Pursuing the light within</h1>
                    <p class="hero-subtitle">Find your peace and comfort</p>
                </div>
            </section>

            <!-- Main Content Section -->
            <section class="main-content">
                <div class="content-wrapper">
                    <!-- 骨架屏加载 -->
                    <el-skeleton :loading="loading" :count="3" animated>
                        <template #template>
                            <div v-for="i in 3" :key="i" class="skeleton-item">
                                <!-- 标题骨架 -->
                                <div class="skeleton-header mt-[10px]">
                                    <el-skeleton-item variant="h1" style="width: 200px; height: 32px;" />
                                    <el-skeleton-item variant="text"
                                        style="width: 60px; height: 20px; margin-left: 12px;" />
                                </div>
                                <!-- 卡片网格骨架 -->
                                <div class="skeleton-grid">
                                    <div v-for="j in 5" :key="j" class="skeleton-card">
                                        <el-skeleton-item variant="image"
                                            style="width: 100%; height: 146px; border-radius: 8px 8px 0 0;" />
                                        <div class="skeleton-card-content">
                                            <el-skeleton-item variant="text"
                                                style="width: 80%; height: 16px; margin-bottom: 8px;" />
                                            <el-skeleton-item variant="text"
                                                style="width: 60%; height: 14px; margin-bottom: 12px;" />
                                            <el-skeleton-item variant="button"
                                                style="width: 100%; height: 36px; border-radius: 6px;" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </template>
                        <!-- 实际内容 -->
                        <template #default>
                            <MainItem v-for="(item, index) in validModules" :key="item.id" :title="item.title"
                                :show-num="item.title === 'Online Advisors'" :data-list="item.list!"
                                :show-free="false" />
                        </template>
                    </el-skeleton>
                </div>
            </section>
        </main>
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed } from 'vue'
import MainItem from './components/mainItem.vue'
import { getAppModuleList, getBanner, getAngurList } from '@/api/index'
import { getUserInfo } from '@/api/user'
import { useUserStore } from '@/stores/user'

// Types
interface AngurItem {
    id: string
    uid: string
    name: string
    username: string
    avatar: string
    grade: number
    state: number
    introduction: string
    commentTimes: number
}

interface ModuleItem {
    id: string
    title: string
    searchKey: string
    searchValue: string
    list?: AngurItem[]
}

// Store
const userStore = useUserStore()

// Reactive Data
const modulesWithAngurs = ref<ModuleItem[]>([])
const loading = ref(false)

// Computed
const validModules = computed(() => {
    return modulesWithAngurs.value.filter((module) => module.list && module.list.length > 0)
})

// Methods
const getUserInfoFunc = async (): Promise<void> => {
    try {
        const response = await getUserInfo()
        userStore.setUserInfo(response.data)
    } catch (error) {
        console.error('获取用户信息失败:', error)
    }
}

const getAngurListFunc = async (key: string): Promise<AngurItem[]> => {
    try {
        const response = await getAngurList({ key })
        // 修复类型错误：返回正确的数据结构
        return response.data || []
    } catch (error) {
        console.error(`获取占卜师列表失败 (key: ${key}):`, error)
        return []
    }
}

const getAppModuleListFunc = async (): Promise<void> => {
    try {
        loading.value = true
        const response = await getAppModuleList()

        // 并行获取所有占卜师列表
        const modulePromises = response.data.map(async (item: ModuleItem) => {
            try {
                const angurList = await getAngurListFunc(item.id)
                return {
                    ...item,
                    list: angurList,
                }
            } catch (error) {
                console.error(`获取模块 ${item.id} 的占卜师列表失败:`, error)
                return {
                    ...item,
                    list: [],
                }
            }
        })

        modulesWithAngurs.value = await Promise.all(modulePromises)
        console.log('模块数据:', modulesWithAngurs.value)
    } catch (error) {
        console.error('获取应用模块列表失败:', error)
    } finally {
        loading.value = false
    }
}

const getBannerFunc = async (): Promise<void> => {
    try {
        const response = await getBanner()
        // 处理 banner 数据，如果需要的话可以在这里添加逻辑
        console.log('Banner 数据:', response)
    } catch (error) {
        console.error('获取 Banner 失败:', error)
    }
}

// Lifecycle
onMounted(async () => {
    try {
        // 并行执行所有初始化函数
        await Promise.all([getUserInfoFunc(), getAppModuleListFunc(), getBannerFunc()])
    } catch (error) {
        console.error('页面初始化失败:', error)
    }
})
</script>

<style lang="scss" scoped>
// Variables
$hero-height-mobile: 400px;
$hero-height-desktop: 600px;
$content-padding-mobile: 12px;
$content-padding-desktop: 120px;
$header-offset-mobile: -72px;
$header-offset-desktop: -92px;

// Container
.home-container {
    width: 100%;
    min-height: 100vh;
}

.home-main {
    width: 100%;
}

// Hero Section
.hero-section {
    position: relative;
    height: $hero-height-mobile;
    margin-top: $header-offset-mobile;

    @media (min-width: 768px) {
        height: $hero-height-desktop;
        margin-top: $header-offset-desktop;
    }
}

.hero-carousel {
    height: 100%;
    width: 100%;
}

.hero-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
}

.hero-content {
    position: absolute;
    bottom: 30px;
    left: $content-padding-mobile;
    z-index: 10;
    max-width: 600px;

    @media (min-width: 1024px) {
        bottom: 224px;
        left: $content-padding-desktop;
    }
}

.hero-title {
    font-family: 'Philosopher', serif;
    font-style: italic;
    font-weight: bold;
    font-size: 28px;
    line-height: 1.2;
    color: white;
    margin: 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);

    @media (min-width: 1024px) {
        font-size: 46px;
        line-height: 1.1;
    }
}

.hero-subtitle {
    font-family: 'ttChocolates', serif;
    font-style: italic;
    font-size: 18px;
    line-height: 1.4;
    color: white;
    margin: 16px 0 0 0;
    opacity: 0.8;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);

    @media (min-width: 1024px) {
        font-size: 34px;
        line-height: 51px;
        margin-top: 20px;
    }
}

// Main Content Section
.main-content {
    width: 100%;
    min-height: calc(100vh - #{$hero-height-mobile});
    padding: 11px $content-padding-mobile 0;

    @media (min-width: 768px) {
        min-height: calc(100vh - #{$hero-height-desktop});
    }

    @media (min-width: 1024px) {
        padding: 0 $content-padding-desktop;
    }
}

.content-wrapper {
    width: 100%;
    // max-width: 1200px;
    margin: 0 auto;
}

// Element UI Overrides
:deep(.el-carousel__container) {
    height: 100%;
}

:deep(.el-carousel__indicators) {
    display: none;
}

:deep(.el-carousel__arrow) {
    background-color: rgba(255, 255, 255, 0.8);
    color: #333;
    border-radius: 50%;
    width: 40px;
    height: 40px;

    &:hover {
        background-color: rgba(255, 255, 255, 0.9);
    }
}

// Loading State
:deep(.el-loading-mask) {
    background-color: rgba(255, 255, 255, 0.8);
}

// Dark Mode Support
@media (prefers-color-scheme: dark) {
    .hero-content {
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    }

    :deep(.el-loading-mask) {
        background-color: rgba(0, 0, 0, 0.8);
    }
}

// Animation
.hero-content {
    animation: fadeInUp 0.8s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

// Skeleton Loading Styles
.skeleton-item {
    margin-bottom: 40px;

    &:last-child {
        margin-bottom: 0;
    }
}

.skeleton-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.skeleton-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(146px, 1fr));
    gap: 10px;

    @media (min-width: 768px) {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 20px;
    }

    @media (min-width: 1024px) {
        grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
        gap: 24px;
    }
}

.skeleton-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0px 1px 5px 0px #dcdde5;
    overflow: hidden;

    @media (min-width: 768px) {
        border-radius: 14px;
    }
}

.skeleton-card-content {
    padding: 12px;

    @media (min-width: 768px) {
        padding: 16px;
    }
}

// Element Plus Skeleton Overrides
:deep(.el-skeleton) {
    width: 100%;
}

:deep(.el-skeleton__item) {
    background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 37%, #f2f2f2 63%);
    background-size: 400% 100%;
    animation: skeleton-loading 1.4s ease infinite;
}

@keyframes skeleton-loading {
    0% {
        background-position: 100% 50%;
    }

    100% {
        background-position: 0 50%;
    }
}

// Accessibility
@media (prefers-reduced-motion: reduce) {
    .hero-content {
        animation: none;
    }

    :deep(.el-carousel) {
        --el-carousel-interval: 0;
    }

    :deep(.el-skeleton__item) {
        animation: none;
    }
}
</style>
