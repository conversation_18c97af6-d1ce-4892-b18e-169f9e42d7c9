import { ElMessage } from 'element-plus' //引入message弹出框

let messageDom: any = null

interface SmMessageOptions {
  message?: string
  type?: 'success' | 'error' | 'warning' | 'info'
  [key: string]: any
}

interface SmMessageType {
  (options: SmMessageOptions): void
  success: (options: string | SmMessageOptions) => void
  error: (options: string | SmMessageOptions) => void
  warning: (options: string | SmMessageOptions) => void
  info: (options: string | SmMessageOptions) => void
}

const smMessage = ((options: SmMessageOptions) => {
  if (messageDom) messageDom.close() // 判断弹窗是否已存在,若存在则关闭
  messageDom = ElMessage(options)
}) as SmMessageType

const typeArr = ['success', 'error', 'warning', 'info'] as const
typeArr.forEach(type => {
  smMessage[type] = (options: string | SmMessageOptions) => {
    if (typeof options === 'string') options = { message: options }
    options.type = type
    return smMessage(options)
  }
})

export default smMessage
 