import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, InternalAxiosRequestConfig } from 'axios';
import smMessage from './smMessage';

// 定义响应数据的接口
interface ResponseData<T = any> {
    code: number;
    count?: number;
    data?: T;
    msg: string;
}

// 定义请求配置接口
interface RequestConfig extends AxiosRequestConfig {
    showError?: boolean; // 是否显示错误信息
    showSuccess?: boolean; // 是否显示成功信息
}

// 创建 axios 实例
const service: AxiosInstance = axios.create({
    baseURL: import.meta.env.VITE_API_REUEST_URL, // 从环境变量获取基础URL
    timeout: 15000, // 请求超时时间
    headers: {
        'Content-Type': 'application/json;charset=utf-8',
    },
});

// 请求拦截器
const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone 
service.interceptors.request.use(
    (config: InternalAxiosRequestConfig) => {
        // 从 localStorage 获取 token
        const token = localStorage.getItem('token');
        if (token) {
            config.headers['token'] = token;
        }
        config.headers['device_uuid'] = localStorage.deviceId;
        config.headers['platform'] = '1';
        config.headers['app_id'] = '46';
        config.headers['device_language'] = navigator.language;
        // 兼容 network_type 获取，部分浏览器没有 navigator.connection
        const connection = (navigator as any).connection;
        config.headers['network_type'] = connection && connection.effectiveType ? connection.effectiveType : '';
        config.headers['device_country'] = localStorage.getItem('deviceCountry') ?? ''
        config.headers['device_timezone'] = timezone;
        config.headers['version_code'] = '1'
        config.headers['version_name'] = ''
        config.headers['os_version'] = ''
        return config;
    },
    (error) => {
        return Promise.reject(error);
    }
);

// 响应拦截器
service.interceptors.response.use(
    (response: AxiosResponse<ResponseData>) => {
        const { data, config } = response;
        const { showSuccess = false } = config as RequestConfig;
        // 根据业务状态码判断请求是否成功
        if (data.code === 0) {
            // 只返回业务数据
            return response;
        }else if(data.code===401){

        }

        // 处理其他状态码

        smMessage.error(data.msg || '请求失败');
        return Promise.reject(new Error(data.msg || '请求失败'));
    },
    (error) => {
        const { config } = error;
        const { showError = true } = (config || {}) as RequestConfig;

        // 处理 HTTP 错误状态
        let message = '网络错误';
        if (error.response) {
            switch (error.response.status) {
                case 401:
                    message = '未授权，请重新登录';
                    // 可以在这里处理登出逻辑
                    break;
                case 403:
                    message = '拒绝访问';
                    break;
                case 404:
                    message = '请求错误，未找到该资源';
                    break;
                case 500:
                    message = '服务器错误';
                    break;
                default:
                    message = `请求失败: ${error.response.status}`;
            }
        } else if (error.request) {
            message = '服务器无响应';
        }

        if (showError) {
            smMessage.error(message);
        }
        return Promise.reject(error);
    }
);

// 封装 GET 请求
export function get<T = any>(url: string, params?: any, config?: RequestConfig): Promise<ResponseData<T>> {
    return service.get(url, { params, ...config }).then(response => response.data);
}

// 封装 POST 请求，兼容 params（query）传参
export function post<T = any>(
    url: string,
    data?: any,
    config?: RequestConfig & { params?: any }
): Promise<ResponseData<T>> {
    // config.params 会自动作为 query 拼接到 url
    return service.post(url, data, config).then(response => response.data);
}

// 封装 PUT 请求
export function put<T = any>(url: string, data?: any, config?: RequestConfig): Promise<ResponseData<T>> {
    return service.put(url, data, config).then(response => response.data);
}

// 封装 DELETE 请求
export function del<T = any>(url: string, params?: any, config?: RequestConfig): Promise<ResponseData<T>> {
    return service.delete(url, { params, ...config }).then(response => response.data);
}

// 导出 axios 实例，以便需要时可以直接使用
export default service;

// 导出请求配置类型
export type { RequestConfig, ResponseData };
