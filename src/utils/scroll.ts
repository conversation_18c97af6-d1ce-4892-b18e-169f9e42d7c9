/**
 * 滚动工具函数
 */

/**
 * 恢复页面滚动
 */
export const restoreScroll = () => {
  // 恢复 body 的滚动
  document.body.style.overflow = ''
  document.body.style.overflowX = ''
  document.body.style.overflowY = ''
  
  // 强制触发重排以确保滚动恢复
  document.body.offsetHeight
  
  // 确保 html 元素也可以滚动
  document.documentElement.style.overflow = ''
  document.documentElement.style.overflowX = ''
  document.documentElement.style.overflowY = ''
}

/**
 * 禁用页面滚动
 */
export const disableScroll = () => {
  document.body.style.overflow = 'hidden'
  document.documentElement.style.overflow = 'hidden'
}

/**
 * 滚动到顶部
 */
export const scrollToTop = (behavior: ScrollBehavior = 'smooth') => {
  window.scrollTo({
    top: 0,
    behavior
  })
}

/**
 * 滚动到指定元素
 */
export const scrollToElement = (element: HTMLElement | string, offset: number = 0) => {
  const targetElement = typeof element === 'string' 
    ? document.querySelector(element) as HTMLElement
    : element
  
  if (targetElement) {
    const elementTop = targetElement.offsetTop - offset
    window.scrollTo({
      top: elementTop,
      behavior: 'smooth'
    })
  }
}

/**
 * 获取当前滚动位置
 */
export const getScrollPosition = () => {
  return {
    top: window.pageYOffset || document.documentElement.scrollTop,
    left: window.pageXOffset || document.documentElement.scrollLeft
  }
}

/**
 * 设置滚动位置
 */
export const setScrollPosition = (top: number, left: number = 0) => {
  window.scrollTo({
    top,
    left,
    behavior: 'smooth'
  })
} 