import { EventEmitter } from 'eventemitter3'
import { ElMessage } from 'element-plus'


export const eventemitter = new EventEmitter()

/**
 * 返回时分秒
 * @param completeTimestamp 时间戳，秒
 */
export const returnTimes = (timestamp: number, type?: string) => {
    const date = new Date(Number(timestamp) * 1000)
    const Y = date.getFullYear() + '-'
    const M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-'
    const D = (date.getDate() + 1 <= 10 ? '0' + date.getDate() : date.getDate()) + ' '
    const h = (date.getHours() + 1 <= 10 ? '0' + date.getHours() : date.getHours()) + ':'
    const m = (date.getMinutes() + 1 <= 10 ? '0' + date.getMinutes() : date.getMinutes()) + ':'
    const s = date.getSeconds() + 1 <= 10 ? '0' + date.getSeconds() : date.getSeconds()
    if (type === 'hours') {
        return h + m + s
    }
    return Y + M + D + h + m + s
}
export const returnDays = (timestamp: number) => {
    const date = new Date(Number(timestamp) * 1000)
    const Y = date.getFullYear() + '/'
    const M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '/'
    const D = (date.getDate() + 1 <= 10 ? '0' + date.getDate() : date.getDate()) + ' '
    return Y + M + D 
}
export function formatTimestampToString(timestamp: number): string {
    // 如果是10位，说明是秒，需要转成毫秒
    if (!timestamp) return ''
    if (timestamp.toString().length === 10) {
        timestamp = timestamp * 1000;
    }
    const date = new Date(timestamp);
    const monthNames = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
    const year = date.getFullYear();
    const month = monthNames[date.getMonth()];
    const day = date.getDate();
    const hour = String(date.getHours()).padStart(2, '0');
    const minute = String(date.getMinutes()).padStart(2, '0');
    const second = String(date.getSeconds()).padStart(2, '0');
    return `${month} ${day}, ${year} ${hour}:${minute}:${second}`;
}
export function formatTimestampToString2(timestamp: number): any {
    // 如果是10位，说明是秒，需要转成毫秒
    if (!timestamp) return ''
    if (timestamp.toString().length === 10) {
        timestamp = timestamp * 1000;
    }
    const date = new Date(timestamp);
    const monthNames = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
    const year = date.getFullYear();
    const month = monthNames[date.getMonth()];
    const day = date.getDate();
    const hour = String(date.getHours()).padStart(2, '0');
    const minute = String(date.getMinutes()).padStart(2, '0');
    const second = String(date.getSeconds()).padStart(2, '0');
    return { time: `${month} ${day}, ${year} ${hour}:${minute}`, isPm: Number(hour) > 12 ? 'pm' : 'at' }
}
export const copyText = async (text: string) => {
    try {
        // 方法1: 使用现代 Clipboard API
        if (window.navigator.clipboard && window.navigator.clipboard.writeText) {
            await window.navigator.clipboard.writeText(text)
            ElMessage.success('复制成功')
            return
        }
        // 方法2: 使用 document.execCommand (降级方案)
        const textArea = document.createElement('textarea')
        textArea.value = text
        textArea.style.position = 'fixed'
        textArea.style.left = '-999999px'
        textArea.style.top = '-999999px'
        document.body.appendChild(textArea)
        textArea.focus()
        textArea.select()

        const successful = document.execCommand('copy')
        document.body.removeChild(textArea)

        if (successful) {
            ElMessage.success('复制成功')
        } else {
            throw new Error('复制失败')
        }
    } catch (err) {
        console.error('复制失败:', err)
        ElMessage.error('复制失败，请手动复制')
    }
}

/**
 * 聊天时间友好显示（英文版，支持昨天）
 * @param timestamp 秒或毫秒级时间戳
 * @returns string
 */
export function formatChatTime(timestamp: number): string {
    // 兼容秒/毫秒
    if (timestamp < 1e12) timestamp = timestamp * 1000
    const now = Date.now()
    const diff = Math.floor((now - timestamp) / 1000) // 秒
    if (diff < 60) return 'now'
    if (diff < 600) return `${Math.floor(diff / 60)} minutes ago`
    const date = new Date(timestamp)
    const nowDate = new Date(now)
    // 今天0点
    const todayZero = new Date(nowDate.getFullYear(), nowDate.getMonth(), nowDate.getDate(), 0, 0, 0, 0)
    // 昨天0点
    const yesterdayZero = new Date(todayZero.getTime() - 24 * 60 * 60 * 1000)
    if (timestamp >= todayZero.getTime()) {
        // 今天内
        return date.toLocaleTimeString('en-US', { hour12: false })
    } else if (timestamp >= yesterdayZero.getTime()) {
        // 昨天
        const h = String(date.getHours()).padStart(2, '0')
        const min = String(date.getMinutes()).padStart(2, '0')
        return `Yesterday ${h}:${min}`
    } else {
        // 更早
        const y = date.getFullYear()
        const m = String(date.getMonth() + 1).padStart(2, '0')
        const d = String(date.getDate()).padStart(2, '0')
        const h = String(date.getHours()).padStart(2, '0')
        const min = String(date.getMinutes()).padStart(2, '0')
        return `${y}-${m}-${d} ${h}:${min}`
    }
}


