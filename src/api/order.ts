import { get, post } from '@/utils/request'


/**
 * 获取订单评论列表
 * @param augurUid 
 * @param page 
 * @param pageSize 
 * @returns 
 */
export const getAguarOrderCommit = ({
    augurUid,
    current,
    size,
   
    commentNotEmpty = 0
}: {
    augurUid: string,
    current: number,
    size: number,
    commentNotEmpty?: number
}) => {
    return get('/augur/order/comment/list', { augurUid, current, size, commentNotEmpty })
}


export const getMySelfOrderList = ({
    current,
    keyword,
    size,
    status,
    augurFortuneGroupId
}: {
    current?: number,
    keyword?: string,
    size?: number,
    status?: number,
    augurFortuneGroupId?: string
}) => {
    return get('/augur/order/list/v2', { current, keyword, size, status, augurFortuneGroupId })
}


/**
 * 
 * 创建订单
 * @param params 
 * @returns 
 */
interface CreateOrderParams {
    augurFortuneGroupId: string,
    augurFortuneId: string,
    augurUid: string,
    name: string,
    birth: string,
    gender: string,
    generalSituation: string,
    specificQuestion: string,
    imageUrl?: string,
    expeditingType?: number
    birthPlace: string,
}

export const createAugurOrder = (params: CreateOrderParams) => {
    return post(`/augur/order/create`, undefined, { params: params })
}

/**
 * 获取常见问题
 * @returns 
 */
export const getAugurOrderCommonQuestion = () => {
    return get('/augur/order/common/question')
}




/**
 * 取消订单
 * @returns 
 */
interface cancelOrderrderParmas {
    orderNo: string,
    status: string,
    errorMsg?: string,
    errorCode?: string,
}
export const cancelOrder = (parmas: cancelOrderrderParmas) => {
    return post('/augur/order/cancel', undefined, { params: parmas })
}

/**
 * 获取订单详情
 * @param orderNo 
 * @returns 
 */
export const getOrderDetail = (orderNo: string) => {
    return get(`/augur/order/detail?orderNo=${orderNo}`)
}
/**
 * 查询实时文字订单消息记录
 */
interface orderMessageListParams {
    current: number,
    orderNo: string,
    size?: number,
    sort?: number
}
export const orderMessageList = (params: orderMessageListParams) => {
    return get('/augur/order/real/time/msg', params)
}


/**
 * 
 * 完成订单（不是实时单）
 * @param orderNo 
 * @returns 
 */
export const completeOrder = (orderNo: string) => {
    return post(`/augur/order/complete?orderNo=${orderNo}`)
}

/**
 * 
 * 结束订单（实时单）
 * @param orderNo 
 * @returns 
 */
export const endOrder = (orderNo: string) => {
    return post(`/augur/order/end?orderNo=${orderNo}`)
}
/**
 * 评价订单
 */
interface commentOrderParams {
    comment: string,
    grade: number,
    hideUsername?: string,
    orderNo: string,
}

export const commentOrder = (params: commentOrderParams) => {
    return post('/augur/order/comment', undefined, { params })
}

/**
 * 获取充值商品列表
 */
export const getCoinPackList = () => {
    return get('/product/coin/pack/list')
}







