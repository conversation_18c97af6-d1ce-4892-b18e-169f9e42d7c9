import { get,post } from '@/utils/request'

export const getUserInfo = () => {
    return get('/mine/profile')
}

/**
 * @description 获取关注的占卜师列表
 * @param param0 
 * @returns 
 */
export const getFollowList = ({
    current,
    size
}: {
    current: number,
    size?: number
}) => {
    return get('/augur/follow/list', { current, size })
}


/**
 * @description 得到活动卡片
 * @param param0 
 * @returns 
 */
export const activityCardList = ({
    isOnlyExpire,
    current,
    size,
}: {
    isOnlyExpire: number,
    current?: number,
    size?: number
}) => {
    return get('/mine/activity/card/list', {
        isOnlyExpire,
        current,
        size
    })
}
/**
 * @description 更新用户信息
 * @param data 
 * @returns 
 */
export const updateUserInfo = (data: any) => {
    return post('/mine/profile/update',undefined, {params:data})
}
/**
 * 获取自己的中奖记录
 * @returns 
 */
export const getLuckyDrawRewardList = ({
    current,
    size
}: {
    current: number,
    size?: number
}) => {
    return get('/lucky/draw/reward/list', { current, size })
}
