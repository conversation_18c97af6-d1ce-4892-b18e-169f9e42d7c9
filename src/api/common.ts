import { get, post } from '@/utils/request'
/**
 * 上传文件
 * @param file 文件对象
 * @param uploadUrl 上传地址
 */
export const uploadFile = (file: File): Promise<void> => {
    const formData = new FormData()
    formData.append('files', file)
    return post('/album/media/upload', formData, {
        headers: {
            'Content-Type': 'multipart/form-data'
        } as any
    })
}

export const searchBirthPlace = (keyword: string) => {
    return get('/client/config/geo', { keyword })
}


/**
 * 
 */
export const getImConfig = () => {
    return get('/client/config/tsl')
}


/**
 * 系统心跳
 * @returns 
 */
export const imHeartbeat = () => {
    return post('/mine/heartbeat')
}