import { get, post } from '@/utils/request'

/**
 * 获取IM聊天消息次数
 * @returns 
 */
export const getImServiceTimes = () => get('/im/message/times/get')

/**
 * 发送IM聊天消息
 * @param data 
 * @returns 
 */
interface SendImMessageParams {
    augurUid: string
    content: string,
    messageBO:{
        augurUid:string,
        content:string,
    }
}
export const sendImMessage = (data: SendImMessageParams) => {
    return post('/im/message/send', data)
}

/**
 * 拿到单聊的快捷回复模版
 * @returns 
 */
export const getImServiceQuickReply = () => get('/augur/order/template')