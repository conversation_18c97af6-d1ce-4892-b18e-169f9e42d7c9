import { get, post } from '@/utils/request'

/**
 * 获取算命师详情
 * @param params 
 * @returns 
 */
export const getAugurDetail = (params: { augurUid: string }) => {
    return get('/augur/detail', params)
}
/**
 * 关注算命师
 * @param params 
 * @returns 
 */
export const followAugur = (params: { augurUid: string }) => {
    return post(`/augur/follow?augurUid=${params.augurUid}`)
}
/**
 * 取消关注算命师
 * @param params 
 * @returns 
 */
export const unFollowAugur = (params: { augurUid: string }) => {
    return post(`/augur/unFollow?augurUid=${params.augurUid}`)
}


/**
 * 搜索算命师
 * @param params 
 * @returns 
 */
export const searchAugur = (params: { sorted?: string, keyword?: string, current: number, size?: number, priceMin?: string, priceMax?: string, serviceType?: string }) => {
    return get('/augur/search', params)
}


/**
 * 随机推荐一个占卜师
 */
// /augur/recommend/on/augur/offline
export const recommendAugur = ({ augurFortuneGroupId }: { augurFortuneGroupId: number }) => {
    return get('/augur/recommend/one', { augurFortuneGroupId })
}

/**
 * 实时订单连接失败推荐一个占卜师
 */
export const recommendAugurInOrder = ({ augurFortuneGroupId }: { augurFortuneGroupId: string }) => {
    return get('/augur/recommend/in/order/detail', { augurFortuneGroupId })
}


