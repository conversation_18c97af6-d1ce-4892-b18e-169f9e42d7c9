import { createApp } from 'vue'
import App from './App.vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import './styles/index.scss'
import router from './routers/index'
import { useThemeStore } from './stores/theme'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import i18n from './languages/index'
import { useIMStore } from '@/stores/im'

import { setupLoadingDirective } from '@/components/Loading'

const pinia = createPinia()
const app = createApp(App)
console.log(ElementPlus.version)
app.use(router)
app.use(pinia)
app.use(i18n)
app.use(ElementPlus, {
    locale: zhCn
})

// 初始化主题
// const themeStore = useThemeStore(pinia)
// themeStore.init()

// 全局初始化IM
const imStore = useIMStore()
imStore.tryAutoLogin() // 自动登录IM

setupLoadingDirective(app)
router.isReady().then(() => {
    app.mount('#app')
})
