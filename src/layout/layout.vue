<!--  -->
<template>
	<div class="layout" ref="layoutRef">
		<Header :headerBgOpacity="headerBgOpacity" @loginIn="loginIn" class="sticky top-0 left-0 right-0 z-50" />
		<router-view :showHeaderBg="showHeaderBg"></router-view>
		<Footer v-if="showFooter"></Footer>
		<LoginDialog :dialogVisible="dialogVisible" @update:dialogVisible="dialogVisible = $event" :dialogAccountVisible="dialogAccountVisible" @update:dialogAccountVisible="dialogAccountVisible = $event" @loginWithPhone="loginWithPhone"></LoginDialog>
        <PhoneLoginDialog :isOpen="dialogPhoneVisible" @update:dialog-phone-visible="changePhoneDialogVisible"></PhoneLoginDialog>
		<Help :isShow="isShow" @backToTop="backToTop"></Help>
        <!--客服-->
        <CustomerSupport></CustomerSupport>
	</div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted, watch,computed } from 'vue'
const { showHeaderBg } = defineProps<{
	showHeaderBg?: boolean
}>()
import Header from './components/header/index.vue'
import Footer from './components/footer.vue'
import LoginDialog from '@/components/loginDialog/loginDialog.vue'
import PhoneLoginDialog from '@/components/loginDialog/phoneLogin.vue'
import Help from './components/help.vue'
import CustomerSupport from './components/customerSupport/customerSupport.vue'
import {useLayoutStore} from '@/stores/layout'
import { useUserStore } from '@/stores/user';
const layoutStore = useLayoutStore()
const showFooter = computed(()=>layoutStore.showFooter)

const userStore = useUserStore()
const dialogPhoneVisible = computed(()=>userStore.dialogPhoneVisible)

const layoutRef = ref<HTMLElement | null>(null)
const headerBgOpacity = ref(showHeaderBg ? 0 : 1)
const dialogVisible = ref(false)
const isShow = ref(false)
const dialogAccountVisible = ref(false)
watch(
	() => showHeaderBg,
	(newVal, oldVal) => {
        console.log(newVal)
		if (!newVal) {
			headerBgOpacity.value = 0
		} else {
			headerBgOpacity.value = 1
		}
	},
	{ immediate: true,deep:true}
)

const changePhoneDialogVisible = (newVal:boolean)=>{
    userStore.setDialogPhoneVisible(newVal)
}   
const handleScroll = () => {
	if (!layoutRef.value) return

	const scrollPosition = document.body.scrollTop
	const threshold = 400 // 滚动阈值
	// 计算透明度：0 到 200px 之间线性变化
	if (!showHeaderBg) {
		headerBgOpacity.value = Math.min(scrollPosition / threshold, 1)
	} else {
		headerBgOpacity.value = 1
	}

	if (scrollPosition < 300) {
		isShow.value = false
	} else {
		isShow.value = true
	}
}
const backToTop = () => {
	document.body.scrollTo({
		top: 0,
		behavior: 'smooth',
	})
}
const loginIn = () => {
	dialogVisible.value = true
}

const loginWithPhone = () => {
    dialogVisible.value = false
	userStore.setDialogPhoneVisible(true)
}
onMounted(() => {
	// 监听滚动事件
	if (layoutRef.value) {
		document.body.addEventListener('scroll', handleScroll)
	}
})

onUnmounted(() => {
	// 移除滚动事件监听
	if (layoutRef.value) {
		document.body.removeEventListener('scroll', handleScroll)
	}
})
</script>
<style lang="scss" scoped>
.layout {
	min-height: 100vh;
	width: 100%;
	display: flex;
	flex-direction: column;
	.app-container {
		flex: 1;
	}
}
</style>
