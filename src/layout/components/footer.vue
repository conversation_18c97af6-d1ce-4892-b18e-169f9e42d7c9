<!--  -->
<template>
	<footer class="flex flex-col items-center w-full mt-[35px] h-[302px] pt-[26px] lg:pt-[44px] lg:h-[382px] bg-white">
		<img src="@/assets/common/logo.png" alt="" class="h-[70px] w-[70px] lg:w-[80px] lg:h-[80px]" />
		<span class="text-[26px] mt-1.5 lg:lending-[40px] lg:mt-[10px] lg:text-[30px] lg:leding-[45px]">StarMet</span>
        <span class="text-[14px] mt-2 lg:lending-[20px] lg:mt-3 lg:text-[18px]">©2023 xxxxxxxxx , Inc.</span>
        <div class="flex justify-center items-center gap-5 mt-[18px] text-base text-[#4484FF] lending-6 font-[400] font-['Open Sans'] lg:mt-6 lg:gap-[99px] lg:font-normal lg:text-[20px]">
            <a href="#" class="">Privacy Policy</a>
            <a href="#" class="">Contact Us</a>
            <a href="#" class="">Become psychic</a>
        </div>
        <div class="app-store-container flex gap-5 lg:gap-[60px]">
            <img src="@/assets/footer/app-store.png" alt="" class="w-[108px] h-[32px] mt-[18px] lg:mt-[25px] lg:w-[162px] lg:h-[48px]">
            <img src="@/assets/footer/google-play.png" alt="" class="w-[108px] h-[32px] mt-[18px] lg:mt-[25px] lg:w-[162px] lg:h-[48px]">
        </div>
	</footer>
</template>

<script lang="ts" setup name="Footer"></script>
<style lang="scss" scoped>

a{
    text-decoration: underline;
}
</style>
