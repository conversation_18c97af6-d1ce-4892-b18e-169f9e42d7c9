<template>
    <transition name="slide-fade">
        <div class="help-container w-[58px] fixed top-0 right-[10px] bottom-0 h-max m-auto z-1000 lg:right-[18px] bg-white transition-all duration-300 lg:w-[114px]"
            v-show="isShow">
            <div class="help-img absolute top-[-33px] right-0 left-0 lg:top-[-68px]">
                <img src="@/assets/help/common.png" alt="" class="w-[40px] h-[52px] lg:w-[84px] lg:h-[105px]" />
            </div>
            <div class="help-content pt-[28px] pb-[12px]">
                <div class="help-item relative flex flex-col items-center justify-center mt-[13px] lg:mt-[28px]"
                    @click="changeDownload">
                    <img src="@/assets/help/download.png" alt="" class="w-[20px] h-[20px] lg:w-[38px] lg:h-[38px]" />
                    <span
                        class="mt-[6px] text-[8px] text-center font-['Source_Sans_3'] font-normal text-[#1C2158] opacity-90 lg:text-[14px]">Download
                        App</span>
                    <div class="show-download absolute w-max p-[10px] bg-white rounded-[10px] shadow-lg right-[calc(100%+10px)] top-[50%] translate-y-[-50%]"
                        :class="showDownload ? 'show' : ''">
                        <img src="@/assets/footer/app-store.png" alt="" class="w-[122px] h-[36px]" />
                        <img src="@/assets/footer/google-play.png" alt="" class="w-[122px] h-[36px] mt-2" />
                    </div>
                </div>
                <div class="help-item flex flex-col items-center justify-center mt-[13px] lg:mt-[28px]"
                    @click="customerSupport">
                    <img src="@/assets/help/support.png" alt="" class="w-[20px] h-[20px] lg:w-[38px] lg:h-[38px]" />
                    <span
                        class="mt-[6px] text-[8px] text-center font-['Source_Sans_3'] font-normal text-[#1C2158] opacity-90 lg:text-[14px]">Customer
                        <br /> Support</span>
                </div>
                <div class="help-item flex flex-col items-center justify-center mt-[13px] lg:mt-[28px]"
                    @click="backToTop">
                    <img src="@/assets/help/back-to-top.png" alt="" class="w-[20px] h-[20px] lg:w-[38px] lg:h-[38px]" />
                    <span
                        class="mt-[6px] text-[8px] text-center font-['Source_Sans_3'] font-normal text-[#1C2158] opacity-90 lg:text-[14px]">Back
                        to Top</span>
                </div>
            </div>

        </div>

    </transition>
</template>

<script lang="ts" setup name="Help">
import { ref } from 'vue'
import { useUserStore } from '@/stores/user';

const userStore = useUserStore()
const { isShow } = defineProps<{
    isShow: boolean
}>()
const emit = defineEmits(['backToTop'])
const showDownload = ref(false)

const backToTop = () => {
    emit('backToTop')
}

const changeDownload = () => {
    showDownload.value = !showDownload.value
    console.log(showDownload.value)
}


const customerSupport = () => {
    userStore.setShowCustomerSupport(true)
}
</script>

<style lang="scss" scoped>
.help-container {
    transition: all 0.3s ease-in-out;
    box-shadow: 0px 2.05px 4.09px 0px #5f6bd330;
    border-radius: 5px;
    background: #ffffff;
    z-index: 9999;

    .help-img {
        margin: 0 auto;
        display: flex;
        justify-content: center;
    }

    .help-item {
        cursor: pointer;

        @media (hover: hover) {
            &:hover {
                .show-download {
                    opacity: 1;
                    visibility: visible;
                }
            }
        }

        .show-download {
            opacity: 0;
            visibility: hidden;
            transition: all .3s ease-in-out;

            &.show {
                opacity: 1;
                visibility: visible;
            }

            &.hidden {
                opacity: 0;
                visibility: hidden;
            }

            &::before {
                content: '';
                width: 0;
                height: 0;
                border-top: 10px solid transparent;
                border-bottom: 10px solid transparent;
                border-left: 10px solid #fff;
                position: absolute;
                right: -8px;
                top: 50%;
                transform: translateY(-50%);
            }
        }
    }
}

// 添加动画效果
.slide-fade-enter-active,
.slide-fade-leave-active {
    transition: all 0.3s ease;
}

.slide-fade-enter-from,
.slide-fade-leave-to {
    transform: translateX(30px);
    opacity: 0;
}

.slide-fade-enter-to,
.slide-fade-leave-from {
    transform: translateX(0);
    opacity: 1;
}
</style>
