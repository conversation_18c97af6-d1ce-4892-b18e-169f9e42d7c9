<!--  -->
<template>

    <transition name="fade-slide">
        <!---客服功能-->
        <div class="over-layer w-full h-full fixed bg-[rgba(0,0,0,.5)] z-[9999] md:bg-inherit" v-show="showCustomerSupport">
            <div
                class="customer-support flex flex-col overflow-hidden w-[348px] h-[682px] bg-white rounded-[16px] absolute top-[116px] right-[4px] md:w-[500px] md:h-[702px] md:right-[18px] md:top-[90px]">
                <div class="w-full h-[50px] relative flex justify-center items-center md:h-[70px]">
                    <img src="@/assets/common/customer-close.png" alt=""
                        class="w-[22px] h-[22px] absolute left-[10px] cursor-pointer md:w-[28px] md:h-[28px] md:left-[20px]" @click="closeCustomerSupport">
                    <div class="flex items-center">
                        <img src="@/assets/common/default-avatar.png" alt="" class="w-[30px] h-[30px] mr-[8px] md:w-[34px] md:h-[34px]">
                        <span class="font-blod text-[#1C2158] text-[18px] font-['Philosopher'] md:text-[20px]">StarMet Official</span>
                    </div>
                </div>
                <!--中间聊天的部分-->
                <div class="customer-chat flex-1 overflow-y-auto bg-[#F8F8FA]">
                    <!--消息列表-->
                    <div class="message-list px-[10px] py-[16px]">
                        <div class="message-item">
                            <div class="message-item-content flex">
                                <img src="@/assets/common/default-avatar.png" alt="" class="w-[30px] h-[30px] mr-[8px]">
                                <!--固定的内容-->
                                <div
                                    class="message-item-content-fixed p-[10px] bg-[rgba(190,213,255,.7)] max-w-[85%] rounded-[10px]">
                                    <div
                                        class="tip-message text-[14px] text-[#1C2158] font-semibold leading-[19px] font-['Open_Sans_3'] md:text-[16px] md:font-[400] md:leading-[20px]">
                                        Hello，welcome to StarMet ! Let's start a mysterious journey!Question frequently
                                        asked:
                                    </div>
                                    <div class="tip-list mt-[16px]">
                                        <div v-for="(item, index) in faqItems" :key="index"
                                            class="tip-list-item pr-[4px] pl-[10px] py-[8px] bg-[rgba(255,255,255,.4)] rounded-[4px] text-[#4484FF] text-[14px] leading-[18px] font-['Source_Sans_3'] mb-[4px] md:text-[16px] md:leading-[24px]">
                                            <div class="tip-list-item-top flex items-center justify-between cursor-pointer"
                                                @click="toggleFaq(index)">
                                                <div class="tip-list-item-top-left">
                                                    {{ index + 1 }}.{{ item.question }}
                                                </div>
                                                <img src="@/assets/chat/arrow-right.png" alt=""
                                                    class="shrink-0 w-[16px] h-[16px]"
                                                    :class="{ 'rotate-90': activeIndex === index }">
                                            </div>
                                            <transition name="accordion" @enter="startTransition"
                                                @after-enter="endTransition" @before-leave="beforeLeave" @leave="leave">
                                                <div class="tip-list-item-bottom mt-[5px]"
                                                    v-show="activeIndex === index">
                                                    {{ item.answer }}
                                                </div>
                                            </transition>
                                        </div>
                                    </div>
                                    <div class="more-btn flex items-center justify-end" @click="addFaqItem">
                                        <span
                                            class="text-[14px] text-[rgba(28,33,88,.7)] font-['Source_Sans_3'] leading-[19px] mr-[9px]">More</span>
                                        <img src="@/assets/chat/arrow-right.png" alt=""
                                            class="w-[16px] h-[16px] rotate-[90deg]">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="message-item" v-for="item in messageList">
                            <div class="message-item-content flex mt-[16px] md:mt-[18px]" :class="{ 'flex-row-reverse': item.type === 'user',}">
                                <img src="@/assets/common/default-avatar.png" alt="" class="w-[30px] h-[30px] mr-[8px]">
                                <div
                                    class="message-item-content-fixed p-[10px] bg-[white] max-w-[85%] rounded-[10px] text-[#1C2158] text-[14px] leading-[18px] md:text-[16px]">
                                    {{ item.content }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="customer-footer w-full h-[136px] p-[10px] flex flex-col md:h-[116px]">
                    <div class="">
                        <img src="@/assets/chat/md-add.png" alt="" class="w-[22px] h-[22px]" />
                    </div>
                    <div class="flex-1 mt-[10px] text-[14px]">
                        <textarea name="" id="" placeholder="Message..." class="w-full h-full outline-none"
                            @keydown.enter="handleSend($event)" v-model="message"></textarea>
                    </div>
                </div>
            </div>
        </div>
    </transition>

</template>

<script lang='ts' setup name="CustomerSupport">
import { useUserStore } from '@/stores/user';
import { ref, computed, watch } from 'vue'

const userStore = useUserStore()

const showCustomerSupport = computed(() => {
    return userStore.showCustomerSupport
})

watch(showCustomerSupport, (newValue) => {
    if (newValue) {
        document.body.style.overflow = 'hidden';
    } else {
        document.body.style.overflow = '';
    }
});

const message = ref('')
const messageList = ref([
    {
        type: 'user',
        content: 'Hello',
    },
    {
        type: 'bot',
        content: 'Hello sfasfvsadfasfsafsafsasfsafsda?cdsdfasfafsafasfadsfafafsafa',
    }
])

// FAQ手风琴效果相关
const activeIndex = ref(-1) // 当前激活的FAQ项索引，-1表示都不激活
const faqItems = ref([
    {
        question: 'How it works?',
        answer: 'StarMet connects you with professional advisors who can provide guidance on various aspects of life. Simply choose an advisor and start your consultation.'
    },
    {
        question: 'How to pay?',
        answer: 'We accept various payment methods including credit cards, PayPal, and more. All transactions are secure and encrypted.'
    },
    {
        question: 'Can I get a refund?',
        answer: 'Yes, we offer refunds under certain conditions. Please refer to our refund policy for more details.'
    },
    {
        question: 'Which reading service should I choos?',
        answer: 'Yes, we offer refunds under certain conditions. Please refer to our refund policy for more details.'
    }
])
const addFaqItem = () => {
    faqItems.value.push({
        question: 'How to pay?',
        answer: 'We accept various payment methods including credit cards, PayPal, and more. All transactions are secure and encrypted.'
    },
        {
            question: 'Can I get a refund?',
            answer: 'Yes, we offer refunds under certain conditions. Please refer to our refund policy for more details.'
        },
        {
            question: 'Which reading service should I choos?',
            answer: 'Yes, we offer refunds under certain conditions. Please refer to our refund policy for more details.'
        },
    )
}
// 切换FAQ项的展开/折叠状态
const toggleFaq = (index: number) => {
    if (activeIndex.value === index) {
        // 如果点击的是当前已展开的项，则折叠它
        activeIndex.value = -1
    } else {
        // 否则展开点击的项，并折叠其他项
        activeIndex.value = index
    }
}

// 处理过渡动画
const startTransition = (el: Element) => {
    const element = el as HTMLElement
    element.style.height = 'auto'
    const height = element.scrollHeight
    element.style.height = '0px'
    // 重置透明度，确保元素可见
    element.style.opacity = '1'
    // 触发回流
    element.offsetHeight
    element.style.height = `${height}px`
}

const endTransition = (el: Element) => {
    const element = el as HTMLElement
    element.style.height = ''
    // 确保透明度重置为1
    element.style.opacity = '1'
}

// 处理离开过渡
const beforeLeave = (el: Element) => {
    const element = el as HTMLElement
    // 设置初始高度为当前高度
    element.style.height = `${element.scrollHeight}px`
    // 触发回流
    element.offsetHeight
}

const leave = (el: Element, done: () => void) => {
    const element = el as HTMLElement
    // 设置为0高度，触发过渡
    element.style.height = '0px'
    element.style.opacity = '0'
    element.addEventListener('transitionend', done, { once: true })
}

const handleSend = (event: KeyboardEvent) => {
    if (event.code === 'Enter' && !event.shiftKey) {
        event.preventDefault() // 只有在按住 Shift 键时才阻止默认行为
        console.log('send')
    }
}

const closeCustomerSupport = () => {
    userStore.setShowCustomerSupport(false)
}
</script>
<style lang='scss' scoped>
.customer-support {
    box-shadow: 0px 0px 16px 0px #0F19721A;
}

// 添加动画效果
.fade-slide-enter-active {
    transition: all 0.3s ease-out;
}

.fade-slide-leave-active {
    transition: all 0.3s ease-in;
}

.fade-slide-enter-from,
.fade-slide-leave-to {
    opacity: 0;
    transform: translateX(100%);
}

.fade-slide-enter-to,
.fade-slide-leave-from {
    opacity: 1;
    transform: translateX(0);
}

// 手风琴动画
.accordion-enter-active,
.accordion-leave-active {
    transition: height 0.3s ease, opacity 0.3s ease;
    overflow: hidden;
}

.accordion-enter-from {
    height: 0;
    opacity: 0;
}

// 箭头旋转动画
img.rotate-90 {
    transform: rotate(90deg);
    transition: transform 0.3s ease;
}

img {
    transition: transform 0.3s ease;
}
</style>
