<template>
	<header class="header-container">
		<FreeHeader :hasExperienceCard="userInfo.hasExperienceCard"/>
		<!-- Navigation -->
		<nav
			class="nav-container transition-colors duration-300"
			:style="{
				'--tw-bg-opacity': headerBgOpacity,
				'box-shadow': headerBgOpacity === 1 ? '0px -0.5px 0px 0px #0000002E inset' : 'none',
			}">
			<nav class="mx-auto flex h-[72px] items-center justify-between px-3 md:px-6 lg:px-[120px] lg:h-[92px]" :class="{ 'lg:px-[60px]': isSearchExpanded }">
				<!-- Left Side -->
				<div class="flex items-center gap-4">
					<!-- Mobile Menu Button -->
					<button type="button" class="lg:hidden -ml-2.5 inline-flex items-center justify-center rounded-md p-2.5 text-gray-700 dark:text-gray-200" @click="toggleMenu">
						<span class="sr-only">Open main menu</span>
						<svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" :class="[isMenuOpen ? 'hidden' : '', headerBgOpacity == 1 ? 'text-black' : 'text-white']">
							<path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
						</svg>
						<svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" :class="[!isMenuOpen ? 'hidden' : '', headerBgOpacity == 1 ? 'text-black' : 'text-white']">
							<path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
						</svg>
					</button>
					<!-- Logo -->
					<Logo :header-bg-opacity="headerBgOpacity" :class="{ hidden: islogoHidden }" />
					<!-- Navigation -->
				</div>
				<Navigation :header-bg-opacity="headerBgOpacity" :active-route="activeRoute" :isSearchExpanded="isSearchExpanded" @update:activeRoute="activeRouteChange" @routerClickChild="routerClickChild" v-show="!isSearchExpanded"/>
				<!-- Right Side -->
				<div class="flex justify-end items-center gap-6">
					<!-- Auth Buttons -->
					<div class="flex items-center gap-3.5 justify-end lg:gap-8 flex-1">
						<div class="search-container relative flex items-center  justify-end   transition-all duration-300 ease-in-out" :class="[isSearchExpanded ? 'flex-1 border-[#FFFFFF80]' : 'w-[40px] border-transparent', headerBgOpacity < 0.5 ? 'bg-transparent' : `bg-[rgba(255,255,255,${headerBgOpacity})]`, headerBgOpacity > 0.5 && isSearchExpanded ? 'active-search' : '']">
							<transition name="search-expand">
								<div class="search-input-wrapper absolute right-0 px-[10px] w-[220px] md:w-[400px] bg-[#fff] rounded-[8px] h-[40px] flex-1 flex items-center overflow-hidden"  v-if="isSearchExpanded">
									<img src="@/assets/common/close.png" alt="" class="w-[17px] h-[17px] cursor-pointer mr-2" @click="closeSearch" />
									<input type="text" ref="searchInput" class="w-full outline-none bg-transparent" v-model="searchValue" @keyup.enter="performSearch" placeholder="Search for advisors" />
								</div>
							</transition>
							<div class="search-icon-wrapper flex-shrink-0 w-[24px] h-[24px] flex items-center justify-center" v-if="!isSearchExpanded">
								<img src="@/assets/common/search-w.png" alt="" class="search-icon w-6 h-6 cursor-pointer" v-if="headerBgOpacity < 0.5" @click="isSearchExpanded = true" />
								<img src="@/assets/common/search.png" alt="" class="search-icon w-6 h-6 cursor-pointer" v-else @click="isSearchExpanded = true" />
							</div>
						</div>

						<a class="login-button px-[13px] py-[7px] text-base text-white md:text-r-1 hover:text-gray-900 lg:py-[9px] lg:px-[23px] lg:text-lg flex-shrink-0" @click="loginIn" v-if="!isLogin">Sign in</a>
						<div class="is-login flex items-center gap-[14px] lg:gap-[30px]" v-else>
							<el-popover placement="bottom-start" v-model:visible="isPopoverVisible" popper-class="avator-popover" trigger="click" :show-arrow="false" :teleported="false" :offset="10" :popper-style="{ right: isLg ? '150px' : '45px' }">
								<template #reference>
									<img :src="userInfo.avatar" alt="" class="w-[32px] h-[32px] rounded-[50%] cursor-pointer lg:w-[42px] lg:h-[42px]" />
								</template>
								<template #default>
									<div class="avator-popover-content h-full px-[15px] flex flex-col items-center lg:px-[30px]">
										<div class="avator-popover-content-item" @click="goUserProfile">
											<img :src="userInfo.avatar" alt="" class="w-16 h-16 rounded-[50%] cursor-pointer lg:w-[86px] lg:h-[86px]" />
										</div>
										<!--name-->
										<div class="avator-popover-content-name flex items-center gap-[6px] text-[#1C2158] mt-[11px] lg:mt-[14px]" @click="goUserProfile">
											<span class="font-[Philosopher] font-bold text-[16px] lg:text-[20px]">{{ userInfo.username }}</span>
											<img src="@/assets/user/edit.png" alt="" class="w-[14px] h-[14px] lg:w-[18px] lg:h-[18px]" />
										</div>
										<!--Id-->
										<div class="avator-popover-content-id flex items-center text-[#1C2158] mt-[9px] lg:mt-[12px]">
											<span class="font-['Source_Sans_3'] font-normal text-[12px] text-[#1C2158] opacity-[0.8] lg:text-[16px]">ID: {{ userInfo.uid }}</span>
											<img src="@/assets/user/copy.png" alt="" class="w-[12px] h-[12px] ml-1 mt-[-4px] lg:w-[14px] lg:h-[14px] cursor-pointer" @click="copyId" />
										</div>
										<!--详细的用户信息-->
										<div class="avator-popover-content-detail w-full flex items-center justify-between mt-[15px]  lg:mt-[20px]">
											<div class="avator-popover-content-detail-item flex-1 flex flex-col items-center justify-center lg:py-[2px]" @click="goToCardPurchase">
												<div class="flex items-center gap-[4px]">
													<span class="text-[18px] font-['Open_Sans'] font-bold text-[#FF7C44] lg:text-[24px]">{{ userInfo.coins }}</span>
													<img src="@/assets/user/money.png" alt="" class="w-[12px] h-[12px] lg:w-[22px] lg:h-[22px]" />
												</div>
												<span class="font-['Source_Sans_3'] font-normal text-[12px] text-[#1C2158] opacity-[0.8] lg:text-[16px]">Balance</span>
											</div>
											<div class="avator-popover-content-detail-line w-[1px] h-[20px] bg-[#d9d9d9] lg:h-[30px]"></div>
											<div class="avator-popover-content-detail-item flex-1 flex flex-col items-center justify-center cursor-pointer lg:py-[2px]" @click="goSelfOrder">
												<div class="message-count relative">
													<span class="text-[18px] font-['Open_Sans'] font-bold text-[#1C2158] lg:text-[24px]">{{orderList}}</span>
													<!-- <div class="message-count-icon px-[6px] bg-[#F53A3A] text-white rounded-[8px] absolute top-[-10px] right-[-25px]">30</div> -->
												</div>
												<span class="font-['Source_Sans_3'] font-normal text-[12px] text-[#1C2158] opacity-[0.8] lg:text-[16px]">Orders</span>
											</div>
										</div>
										<!--操作 以及退出 登录-->
										<div class="avator-popover-content-operation w-full mt-[14px] border-b border-[#E5E5E5] border-solid lg:mt-[25px]">
											<div class="avator-popover-content-operation-item w-full flex items-center justify-between h-[46px] lg:h-[60px] cursor-pointer" @click="goMyCard">
												<div class="avator-popover-content-operation-item-left flex items-center gap-[10px]">
													<img src="@/assets/user/discount.png" alt="" class="w-[22px] h-[22px] lg:w-[26px] lg:h-[26px]" />
													<span class="text-[14px] lg:text-[18px]">My Card</span>
												</div>
												<div class="avator-popover-content-operation-item-right flex items-center">
													<span class="text-[#1C2158] font-['Source_Sans_3'] font-normal text-[14px] opacity-[0.6] lg:text-[18px]">X{{ cardNum }}</span>
													<img src="@/assets/user/arrow-right.png" alt="" class="w-[18px] h-[18px] lg:w-[24px] lg:h-[24px]" />
												</div>
											</div>
											<div class="avator-popover-content-operation-item w-full flex items-center justify-between h-[46px] lg:h-[60px]">
												<div class="avator-popover-content-operation-item-left flex items-center gap-[10px]">
													<img src="@/assets/user/follow.png" alt="" class="w-[22px] h-[22px] lg:w-[26px] lg:h-[26px]" />
													<span class="text-[14px] lg:text-[18px]">Follow List</span>
												</div>
												<div class="avator-popover-content-operation-item-right flex items-center">
													<img src="@/assets/user/arrow-right.png" alt="" class="w-[18px] h-[18px] lg:w-[24px] lg:h-[24px]" />
												</div>
											</div>
											<div class="avator-popover-content-operation-item w-full flex items-center justify-between h-[46px] lg:h-[60px]">
												<div class="avator-popover-content-operation-item-left flex items-center gap-[10px]">
													<img src="@/assets/user/question.png" alt="" class="w-[22px] h-[22px] lg:w-[26px] lg:h-[26px]" />
													<span class="text-[14px] lg:text-[18px]">FAQ</span>
												</div>
												<div class="avator-popover-content-operation-item-right flex items-center">
													<img src="@/assets/user/arrow-right.png" alt="" class="w-[18px] h-[18px] lg:w-[24px] lg:h-[24px]" />
												</div>
											</div>
											<!--line-->
										</div>
										<!--退出登录-->
										<div class="avator-popover-content-operation-item w-full flex-1 flex items-center justify-center">
											<span class="font-[Philosopher] font-bold text-[18px] text-[#4484FF] lg:text-[22px] cursor-pointer" @click="loginOut">Sign Out</span>
										</div>
									</div>
								</template>
							</el-popover>
							<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="transition-colors duration-300 cursor-pointer">
								<path
									d="M20 22H4C3.73736 22 3.47728 21.9483 3.23463 21.8478C2.99198 21.7472 2.7715 21.5999 2.58579 21.4142C2.40007 21.2285 2.25275 21.008 2.15224 20.7654C2.05173 20.5227 2 20.2626 2 20V4C2 3.73736 2.05173 3.47728 2.15224 3.23463C2.25275 2.99198 2.40007 2.7715 2.58579 2.58579C2.7715 2.40007 2.99198 2.25275 3.23463 2.15224C3.47728 2.05173 3.73736 2 4 2H18C18.2626 2 18.5227 2.05173 18.7654 2.15224C19.008 2.25275 19.2285 2.40007 19.4142 2.58579C19.5999 2.7715 19.7472 2.99198 19.8478 3.23463C19.9483 3.47728 20 3.73736 20 4V5.5C20.2626 5.5 20.5227 5.55173 20.7654 5.65224C21.008 5.75275 21.2285 5.90007 21.4142 6.08579C21.5999 6.2715 21.7472 6.49198 21.8478 6.73463C21.9483 6.97728 22 7.23736 22 7.5V20C22 20.2626 21.9483 20.5227 21.8478 20.7654C21.7472 21.008 21.5999 21.2285 21.4142 21.4142C21.2285 21.5999 21.008 21.7472 20.7654 21.8478C20.5227 21.9483 20.2626 22 20 22ZM18.5 4C18.5 3.93434 18.4871 3.86932 18.4619 3.80866C18.4368 3.748 18.4 3.69288 18.3536 3.64645C18.3071 3.60002 18.252 3.56319 18.1913 3.53806C18.1307 3.51293 18.0657 3.5 18 3.5H4C3.93434 3.5 3.86932 3.51293 3.80866 3.53806C3.748 3.56319 3.69288 3.60002 3.64645 3.64645C3.60002 3.69288 3.56319 3.748 3.53806 3.80866C3.51293 3.86932 3.5 3.93434 3.5 4V5.5H18.5V4ZM20.5 11.5H15.75C15.1533 11.5 14.581 11.7371 14.159 12.159C13.7371 12.581 13.5 13.1533 13.5 13.75C13.5 14.3467 13.7371 14.919 14.159 15.341C14.581 15.7629 15.1533 16 15.75 16H20.5V11.5ZM20.5 17.5H15.75C14.7554 17.5 13.8016 17.1049 13.0983 16.4017C12.3951 15.6984 12 14.7446 12 13.75C12 12.7554 12.3951 11.8016 13.0983 11.0983C13.8016 10.3951 14.7554 10 15.75 10H20.5V7.5C20.5 7.43434 20.4871 7.36932 20.4619 7.30866C20.4368 7.24799 20.4 7.19288 20.3536 7.14645C20.3071 7.10002 20.252 7.06319 20.1913 7.03806C20.1307 7.01293 20.0657 7 20 7H3.5V20C3.5 20.0657 3.51293 20.1307 3.53806 20.1913C3.56319 20.252 3.60002 20.3071 3.64645 20.3536C3.69288 20.4 3.748 20.4368 3.80866 20.4619C3.86932 20.4871 3.93434 20.5 4 20.5H20C20.0657 20.5 20.1307 20.4871 20.1913 20.4619C20.252 20.4368 20.3071 20.4 20.3536 20.3536C20.4 20.3071 20.4368 20.252 20.4619 20.1913C20.4871 20.1307 20.5 20.0657 20.5 20V17.5Z"
									:fill="headerBgOpacity < 0.5 ? 'white' : 'url(#paint0_linear_3368_44870)'" />
								<path d="M15.5 13.75C15.5 14.0815 15.6317 14.3995 15.8661 14.6339C16.1005 14.8683 16.4185 15 16.75 15C17.0815 15 17.3995 14.8683 17.6339 14.6339C17.8683 14.3995 18 14.0815 18 13.75C18 13.4185 17.8683 13.1005 17.6339 12.8661C17.3995 12.6317 17.0815 12.5 16.75 12.5C16.4185 12.5 16.1005 12.6317 15.8661 12.8661C15.6317 13.1005 15.5 13.4185 15.5 13.75Z" :fill="headerBgOpacity < 0.5 ? 'white' : 'url(#paint1_linear_3368_44870)'" />
								<defs>
									<linearGradient id="paint0_linear_3368_44870" x1="2" y1="12" x2="22.2147" y2="12.4548" gradientUnits="userSpaceOnUse">
										<stop stop-color="#7AAFFF" />
										<stop offset="0.953125" stop-color="#7A87FF" />
									</linearGradient>
									<linearGradient id="paint1_linear_3368_44870" x1="15.5" y1="13.75" x2="18.0268" y2="13.8068" gradientUnits="userSpaceOnUse">
										<stop stop-color="#7AAFFF" />
										<stop offset="0.953125" stop-color="#7A87FF" />
									</linearGradient>
								</defs>
							</svg>
						</div>
					</div>
				</div>
			</nav>
		</nav>
		<!-- Mobile Menu -->
		<MobileMenu :is-menu-open="isMenuOpen" @close="closeMenu" :active-route="activeRoute" @update:activeRoute="activeRouteChange" @routerClickChild="routerClickChild" />
	</header>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, nextTick } from 'vue'
import FreeHeader from './components/FreeHeader.vue'
import Logo from './components/Logo.vue'
import Navigation from './components/Navigation.vue'
import MobileMenu from './components/MobileMenu.vue'
import { useUserStore } from '@/stores/user'
import { useRouter, useRoute } from 'vue-router'
import { restoreScroll, disableScroll } from '@/utils/scroll'
import { ElMessage } from 'element-plus'
import { activityCardList } from '@/api/user'
import { getMySelfOrderList } from '@/api/order'
const route = useRoute()
const router = useRouter()
const userStore = useUserStore()
const isLogin = computed(() => userStore.isLogin)
const userInfo = computed(() => userStore.userInfo)
const orderList = computed(() => userStore.orderList)
const emit = defineEmits<{
	loginIn: []
}>()

const props = defineProps<{
	headerBgOpacity: number
}>()

const isMenuOpen = ref(false)

const activeRoute = ref('')

const searchValue = ref('')
const cardNum = ref<number>(0)
const isPopoverVisible = ref(false)
const isSearchExpanded = ref(false)
const searchInput = ref<HTMLInputElement | null>(null)

const isLg = ref(true)
const islogoHidden = computed(() => {
	return isSearchExpanded.value && window.innerWidth < 768
})
const activeRouteChange = (routeName: string) => {
	activeRoute.value = routeName
	router.push({
		name: routeName,
	})
}
const toggleMenu = () => {
	isMenuOpen.value = !isMenuOpen.value
	if (isMenuOpen.value) {
		disableScroll()
	} else {
		restoreScroll()
	}
}

const closeMenu = () => {
	isMenuOpen.value = false
	restoreScroll()
}

const performSearch = () => {
	if (searchValue.value.trim()) {
		const newQuery = { search: searchValue.value.trim() }
		if (route.path === '/searchAdvisor') {
			router.push({ query: newQuery });
		} else {
			router.push({
				path: '/searchAdvisor',
				query: newQuery
			});
		}
	}
}

const closeSearch = () => {
	isSearchExpanded.value = false
	searchValue.value = ''
}
const routerClickChild = (routeName: string) => {
    activeRoute.value = 'Advisor'
    // 确保路由参数正确传递
    const targetRoute = {
        name: 'Advisor',
        params: { type: routeName }
    }
    router.push(targetRoute).then(() => {
       
    }).catch(err => {
        // 如果命名路由失败，尝试使用路径跳转
        const fallbackPath = `/advisor/${routeName}`
        
        router.push(fallbackPath).then(() => {
            console.log('备用路径跳转成功')
        }).catch(pathErr => {
            console.error('备用路径跳转也失败:', pathErr)
        })
    })
}

const copyId = async () => {
	if (window.navigator.clipboard && window.navigator.clipboard.writeText) {
		await window.navigator.clipboard.writeText(userInfo.value.uid)
		ElMessage.success('Copy success')
	} else {
		// 方法2: 使用 document.execCommand (降级方案)
		const textArea = document.createElement('textarea')
		textArea.value = userInfo.value.uid
		textArea.style.position = 'fixed'
		textArea.style.left = '-999999px'
		textArea.style.top = '-999999px'
		document.body.appendChild(textArea)
		textArea.focus()
		textArea.select()

		const successful = document.execCommand('copy')
		document.body.removeChild(textArea)

		if (successful) {
			ElMessage.success('Copy success')
		} else {
			throw new Error('Copy failed')
		}
	}
}
const loginIn = () => {
	emit('loginIn')
}

const goSelfOrder = () => {
	router.push({
		name: 'OrderList',
	})
}

const goMyCard = () => {
	router.push({
		name: 'MyCard',
	})
}
const goToCardPurchase = () => {
	router.push({
		name: 'CardPurchase',
	})
}
watch(isPopoverVisible, (newVal) => {
	if (newVal) {
		getActivityCardList()
        getOrderNum()
	} else {
	}
})

const getActivityCardList = async () => {
	try {
		cardNum.value = (await activityCardList({ isOnlyExpire: 0 })).count as number
        console.log(cardNum.value)
	} catch (error) {
		console.log(error)
	}
}
const getOrderNum = async ()=>{
    const orderNum = (await getMySelfOrderList({})).count as number
    userStore.setOrderList(orderNum)
}
const loginOut = ()=>{
    console.log(userStore)
    userStore.loginOut()
    window.location.reload()
}

const goUserProfile = ()=>{
    router.push({
        name:'ProfileEdit'
    })
}

watch(isSearchExpanded, (visible) => {
    if (visible) {
        nextTick(() => {
            searchInput.value?.focus();
        });
    }
});

watch(route, (newRoute) => {
    if(newRoute.path === '/searchAdvisor') {
        isSearchExpanded.value = true;
        searchValue.value = newRoute.query.search as string || '';
    } else {
        isSearchExpanded.value = false;
    }
}, { immediate: true });

onMounted(() => {
	activeRoute.value = route.name?.toString() ?? ''
	if (window.innerWidth > 1336) {
		isLg.value = true
	} else {
		isLg.value = false
	}
	window.addEventListener('resize', () => {
		isLg.value = window.innerWidth > 1336
	})
})
</script>

<style lang="scss" scoped>
.header-container {
	width: 100%;
}

.nav-container {
	background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}

.login-button {
	font-family: 'Philosopher';
	font-weight: 700;
	background: linear-gradient(91.29deg, #7aafff 1.1%, #7a87ff 95.36%);
	border-radius: 8px;
}

.search-container {
	transition: all 0.3s ease-in-out;
}

.search-input-wrapper {
	transition: all 0.3s ease-in-out;
    box-shadow:0px 0px 5px 0px rgba(0, 0, 0, 0.3) ;
}
.active-search {
	position: relative;
	border-color: #7b97ff !important;

	// &::before {
	// 	content: '';
	// 	position: absolute;
	// 	top: 0;
	// 	left: 0;
	// 	right: 0;
	// 	bottom: 0;
	// 	border-radius: 28px;
	// 	padding: 1px; /* 控制边框厚度 */
	// 	background: linear-gradient(91.29deg, #7aafff 1.1%, #7a87ff 95.36%);
	// 	-webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
	// 	-webkit-mask-composite: xor;
	// 	mask-composite: exclude;
	// 	pointer-events: none;
	// }
}

.search-expand-enter-active,
.search-expand-leave-active {
  transition: width 0.3s ease, opacity 0.2s ease;
  overflow: hidden;
}
.search-expand-enter-from,
.search-expand-leave-to {
  width: 0 !important;
  opacity: 0;
}
</style>
