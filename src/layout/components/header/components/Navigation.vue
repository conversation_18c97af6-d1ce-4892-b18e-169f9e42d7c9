<template>
	<div class="menu-list-wrapper relative z-[100]">
		<div class="menu-list hidden text-[22px] font-['Open_Sans'] font-semibold font-normal ml-[50px] lg:flex lg:gap-x-[40px]">
			<a href="/home" :class="[navLinkClass, activeRoute === 'Home' ? 'active' : '']">Home</a>
			<!-- All Advisor Dropdown -->
			<div class="relative group">
				<a href="#" :class="[navLinkClass,activeRoute === 'Advisor' ? 'active' : '', 'flex items-center gap-1']">
					All Advisor
					<svg class="w-4 h-4 mt-1 transition-transform duration-200 group-hover:rotate-180" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
					</svg>
				</a>
				<div class="absolute w-full h-4 top-full"></div>
				<div class="menu-group hidden z-[1000] absolute group-hover:block top-[calc(100%+16px)] left-0 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 w-[294px]">
					<div class="triangle absolute -top-2 left-4 w-4 h-4 rotate-45 bg-white border-t border-l border-black/5"></div>
					<div class="relative">
						<div v-for="(item, index) in menuItems" :key="index" class="menu-group-title px-5 pt-4 hover:bg-[#d0e0ff] cursor-pointer" :class="{'active':childRoute === item.name}" @click="routerClickChild(item)">
							<div class="block pb-5 border-b border-solid border-gray-200 pl-2.5 text-base text-gray-700">{{ item.name }}</div>
						</div>
					</div>
				</div>
			</div>

			<a href="/freeBonus" :class="[navLinkClass, activeRoute === 'FreeBonus' ? 'active' : '']" @click="routerClick('FreeBonus')">Free Bonus</a>
			<a href="/publicReading" :class="[navLinkClass, activeRoute === 'PublicReading' ? 'active' : '']" @click="routerClick('PublicReading')">Public Reading</a>
			<a href="/message" :class="[navLinkClass, activeRoute === 'Message' ? 'active' : '']" @click="routerClick('Message')">Message</a>
		</div>
	</div>
</template>

<script setup lang="ts">
import { useRoute } from 'vue-router'
import { computed, ref,onMounted } from 'vue'
const emit = defineEmits(['update:activeRoute','routerClickChild'])
const route = useRoute()

const props = defineProps<{
	headerBgOpacity: number
	activeRoute: string
	isSearchExpanded: boolean
}>()
const activeMenu = computed(() => (props.headerBgOpacity > 0 ? '#4484FF' : '#ffffff'))
const navLinkClass = computed(() => [props.headerBgOpacity > 0 ? 'text-gray-900' : 'text-white', 'transition-colors duration-300 hover:text-[#4484FF]'])
const childRoute = ref('')
const menuItems = [
	{
		name: 'All',
		type: '0',
	},
	{
		name: 'New',
		type: '4',
	},
	{
		name: 'Reviews',
		type: '2',
	},
	{
		name: 'Readings',
		type: '1',
	},
	{
		name: 'Rated',
		type: '3',
	},
]

const routerClick = (routeName: string) => {
	emit('update:activeRoute', routeName)
}
const routerClickChild = (item: any) => {
	
	childRoute.value = item.name
	emit('routerClickChild', item.type)
}
onMounted(() => {
	childRoute.value = menuItems.find(item => item.type === route.params.type)?.name || ''
})
</script>

<style lang="scss" scoped>
.menu-list {
	a {
		position: relative;
		display: flex;
		align-items: center;
		line-height: 29px;
        white-space: nowrap;
		&.active {
			color: v-bind(activeMenu);
			position: relative;
			&::before {
				content: '';
				position: absolute;
				width: 22px;
				height: 5px;
				background-color: v-bind(activeMenu);
				border-radius: 10px;
				left: 0;
				right: 0;
				bottom: -5px;
				margin: auto;
			}
		}
	}
}

.menu-group {
	border-radius: 1rem;
	left: -40px;
    box-shadow: 0px 4px 20px 0px #5F6BD38C;

	.triangle {
		left: 0px;
		right: 0;
		margin: auto;
		z-index: -1;
	}

	.menu-group-title {
		position: relative;

		&:first-child {
			border-top-left-radius: 1rem;
			border-top-right-radius: 1rem;
		}

		&:last-child {
			border-bottom-left-radius: 1rem;
			border-bottom-right-radius: 1rem;
		}
        &.active{
            background-color: #d0e0ff;
        }
	}
}

.group:hover .menu-group {
	animation: fadeIn 0.2s ease-out forwards;
}

@keyframes fadeIn {
	from {
		opacity: 0;
		transform: translateY(-10px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

.absolute {
	z-index: 50;

	.rotate-45 {
		box-shadow: -3px -3px 5px rgba(0, 0, 0, 0.02);
	}
}

.relative {
	z-index: 1;
	border-radius: 0.375rem;
}

.absolute a {
	font-size: 1.25rem;
	font-family: 'Source Sans 3';
	font-weight: 400;
	font-style: normal;
	transition: all 0.2s ease;
}

.group {
	&:hover {
		.absolute {
			display: block;
		}
	}
}
</style>
