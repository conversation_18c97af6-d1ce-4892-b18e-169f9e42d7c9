<template>
  <a href="/" class="flex items-center gap-2">
    <img src="@/assets/common/logo.png" alt="Logo" class="h-[32px] w-[32px] lg:h-[42px] lg:w-[42px]" />
    <span class="text-[22px] lg:text-r-2 font-['Kefa']" :class="logoClass">StarMet</span>
  </a>
</template>

<script setup lang="ts">
import { computed } from 'vue'

const props = defineProps<{
	headerBgOpacity: number
}>()

const logoClass = computed(() => [props.headerBgOpacity >= 0.5 ? 'header-title' :'white-title'])
</script>

<style lang="scss" scoped>
.header-title {
  background: linear-gradient(91.29deg, #7aafff 1.1%, #7a87ff 95.36%);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
}
.white-title {
  color: white;
}
</style> 