<template>
    <div class="free-header relative w-full h-[74px] pl-[100px] lg:pl-[553px] lg:h-[90px]" v-if="hasExperienceCard">
        <img src="@/assets/free-top.png" alt=""
            class="absolute left-0 hidden lg:block lg:left-[204px] h-full w-[534px] object-contain" />
        <img src="@/assets/free-top-mobile.png" alt=""
            class="absolute left-0 block h-full w-[264px] object-contain lg:hidden" />
        <div class="relative flex items-center h-full pr-[10px]">
            <div class="free-title flex flex-col justify-center h-full">
                <span class="text-title text-[16px] leading-[24px] lg:text-[21px] font-['krungthep']">3 Mins Free<span
                        class="hidden lg:inline">of Your First Reading</span></span>
                <span
                    class="mt-[3px] text-[9px] leading-[15px] text-[#1C378D] font-['ttChocolates'] opacity-80 lg:text-[17px]">*Free
                    trial available for Live Text Chat only</span>
            </div>

            <button
                class="relative common-button text-xs font-semibold py-[7px] px-[12px] rounded-[23px] text-[#06236C] lg:font-['krungthep'] lg:ml-[109px] lg:px-[22px] lg:py-[8px] lg:rounded-[33px] lg:leading-6 lg:border lg:border-[#7397EE]">
                <span class="text-title">Try for Free</span>
            </button>
        </div>
    </div>
</template>

<script setup lang="ts">

const { hasExperienceCard } = defineProps({
    hasExperienceCard: {
        type:Number,
        default:0
    }
})
// 组件逻辑
</script>

<style lang="scss" scoped>
.free-header {
    background: url('@/assets/free-bg.png') no-repeat;
    background-size: 100% 100%;

}

.text-title {
    background: linear-gradient(180deg, #976af8 0%, #06236c 100%);
    background-clip: text;
    -webkit-background-clip: text;
    color: transparent;
}

@media (min-width: 1024px) {
    .common-button {
        background: linear-gradient(180deg, #f6f6ff 32.29%, #dbdaff 100%);
        box-shadow: 0px 0px 10px 0px #ffffff inset, 0px 4px 5px 0px #203a9340;
        background-size: 100% 100%;
        background-position: center;

        &:hover {
            opacity: 0.8;
        }
    }
}

@media (max-width: 1023px) {
    .common-button {
        background: #fff;
        white-space: nowrap;

        &:hover {
            opacity: 0.8;
        }
    }
}
</style>
