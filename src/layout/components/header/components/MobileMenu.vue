<template>
	<div class="fixed inset-0 z-200 lg:hidden font-['Philosopher']" v-show="isMenuOpen">
		<!-- Backdrop -->
		<div class="fixed inset-0 bg-black/20 backdrop-blur-sm" @click="closeMenu"></div>

		<!-- Sidebar with animation -->
		<transition name="slide-menu">
			<div v-if="isMenuOpen" class="fixed inset-y-0 left-0 w-[280px] bg-white">
				<!-- Logo -->
				<div class="pt-5 px-3 flex items-center text-[18px] h-[72px]">
					<!-- Close button -->
					<div class="mr-[14px]">
						<button type="button" class="text-gray-700" @click="closeMenu">
							<svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
								<path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
							</svg>
						</button>
					</div>
					<img src="@/assets/common/logo.png" alt="Logo" class="h-8 w-auto" />
					<span class="ml-2 text-[#4484FF] text-xl font-bold">StarMet</span>
				</div>

				<!-- Menu items -->
				<div class="overflow-y-auto h-[calc(100%-80px)]">
					<div class="pl-5">
						<!-- Home menu item -->
						<div class="flex items-center h-[72px]" :class="{ 'text-[#4484FF] border-b border-[#f2f2f5] border-solid': activeRoute == 'Home' }" @click="routerClick('Home')">
							<div class="w-6 h-6 mr-3 flex items-center justify-center">
								<svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5">
									<path stroke-linecap="round" stroke-linejoin="round" d="M2.25 12l8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25" />
								</svg>
							</div>
							<div>Home</div>
						</div>

						<!-- Our Psychics with submenu -->
						<div class="">
							<div class="flex items-center justify-between text-[#1C2158] h-[72px] cursor-pointer pr-[14px]" @click="toggleSubmenu" :class="{ 'text-[#4484FF]': submenuItems.some(item => item.type === route.params.type) }">
								<div class="flex items-center">
									<div class="w-6 h-6 mr-3 flex items-center justify-center">
										<svg width="20" height="20" viewBox="0 0 20 20" fill="none" stroke="currentColor" xmlns="http://www.w3.org/2000/svg">
											<circle cx="10.0003" cy="10.0001" r="8.33333" stroke-width="1.25" />
											<mask id="path-2-inside-1_3442_14334" fill="white">
												<path d="M11 5C11.442 5 11.8705 5.05741 12.2785 5.16515C13.0895 5.37927 12.9176 6.48342 12.299 7.04993C11.5006 7.78109 11 8.832 11 10C11 11.1681 11.5008 12.2187 12.2993 12.9496C12.918 13.5158 13.0901 14.6199 12.2793 14.8344C11.871 14.9424 11.4423 15 11 15C8.23858 15 6 12.7614 6 10C6 7.23858 8.23858 5 11 5Z" />
											</mask>
											<path
												d="M12.2793 14.8344L11.9596 13.626L12.2793 14.8344ZM11 5V6.25C11.3333 6.25 11.6546 6.29324 11.9594 6.37373L12.2785 5.16515L12.5976 3.95656C12.0864 3.82158 11.5506 3.75 11 3.75V5ZM12.299 7.04993L11.4548 6.12809C10.4088 7.08605 9.75 8.46691 9.75 10H11H12.25C12.25 9.19709 12.5925 8.47613 13.1433 7.97177L12.299 7.04993ZM11 10H9.75C9.75 11.5333 10.409 12.9139 11.4553 13.8716L12.2993 12.9496L13.1433 12.0275C12.5925 11.5234 12.25 10.8028 12.25 10H11ZM12.2793 14.8344L11.9596 13.626C11.6547 13.7067 11.3334 13.75 11 13.75V15V16.25C11.5512 16.25 12.0874 16.1782 12.599 16.0429L12.2793 14.8344ZM11 15V13.75C8.92893 13.75 7.25 12.0711 7.25 10H6H4.75C4.75 13.4518 7.54822 16.25 11 16.25V15ZM6 10H7.25C7.25 7.92893 8.92893 6.25 11 6.25V5V3.75C7.54822 3.75 4.75 6.54822 4.75 10H6ZM12.2993 12.9496L11.4553 13.8716C11.4851 13.8989 11.5199 13.9435 11.5466 13.9984C11.5741 14.0552 11.5771 14.0899 11.577 14.0875C11.577 14.0874 11.5718 13.9954 11.6486 13.8712C11.6882 13.8073 11.7419 13.7484 11.806 13.7021C11.8694 13.6563 11.9259 13.6349 11.9596 13.626L12.2793 14.8344L12.599 16.0429C13.0903 15.9129 13.5071 15.6187 13.7744 15.187C14.0277 14.7777 14.0928 14.3341 14.0737 13.9604C14.037 13.2382 13.6796 12.5184 13.1433 12.0275L12.2993 12.9496ZM12.2785 5.16515L11.9594 6.37373C11.9257 6.36483 11.8692 6.34352 11.8058 6.29771C11.7416 6.25139 11.6878 6.1925 11.6483 6.12861C11.5713 6.00438 11.5765 5.91228 11.5765 5.91216C11.5766 5.90968 11.5736 5.94438 11.5461 6.00122C11.5194 6.05617 11.4847 6.10074 11.4548 6.12809L12.299 7.04993L13.1433 7.97177C13.6795 7.48074 14.0367 6.76081 14.0733 6.03863C14.0922 5.66489 14.027 5.2212 13.7735 4.81201C13.506 4.38028 13.089 4.08628 12.5976 3.95656L12.2785 5.16515Z"
												mask="url(#path-2-inside-1_3442_14334)" />
										</svg>
									</div>
									<div>Our Psychics</div>
								</div>
								<div class="w-5 h-5">
									<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" :class="{ 'transform rotate-180': showSubmenu }">
										<path stroke-linecap="round" stroke-linejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
									</svg>
								</div>
							</div>
							<!-- Submenu items (shown when expanded) -->
							<transition name="slide-child-menu">
								<div v-if="showSubmenu" class="ml-10">
									<div v-for="(item, index) in submenuItems" :key="index" class="text-[#1C2158] h-[60px] opacity-80 border-b border-[#f9f9fb] border-solid leading-[60px] cursor-pointer" :class="{ 'active-submenu-item': childRoute == item.name }" @click="routerClickChild(item)">
										{{ item.name }}
										<!-- <div v-if="index === 0" class="h-[1px] bg-[#1C2158] mt-1"></div> -->
									</div>
								</div>
							</transition>
						</div>
						<!-- Free Bonus -->
						<div class="flex items-center text-[#1C2158] h-[72px] cursor-pointer" :class="{ 'text-[#4484FF] border-b border-[#f2f2f5] border-solid': activeRoute === 'FreeBonus' }" @click="routerClick('FreeBonus')">
							<div class="w-6 h-6 mr-3 flex items-center justify-center">
								<svg width="20" height="20" viewBox="0 0 20 20" fill="none" stroke="currentColor" xmlns="http://www.w3.org/2000/svg">
									<circle cx="10.0003" cy="10.0001" r="8.33333" stroke-width="1.25" />
									<path
										d="M9.05138 7.80287C9.4736 7.04546 9.6847 6.66675 10.0003 6.66675C10.3159 6.66675 10.5271 7.04546 10.9493 7.80287L11.0585 7.99883C11.1785 8.21406 11.2385 8.32168 11.332 8.39268C11.4255 8.46369 11.542 8.49005 11.775 8.54276L11.9871 8.59076C12.807 8.77627 13.217 8.86902 13.3145 9.18266C13.4121 9.4963 13.1326 9.8231 12.5736 10.4767L12.429 10.6458C12.2702 10.8316 12.1908 10.9244 12.155 11.0393C12.1193 11.1542 12.1313 11.2781 12.1553 11.5259L12.1772 11.7515C12.2617 12.6236 12.304 13.0596 12.0486 13.2535C11.7933 13.4473 11.4094 13.2706 10.6418 12.9171L10.4432 12.8257C10.225 12.7253 10.1159 12.675 10.0003 12.675C9.88471 12.675 9.77563 12.7253 9.55749 12.8257L9.35888 12.9171C8.59122 13.2706 8.20738 13.4473 7.95204 13.2535C7.6967 13.0596 7.73895 12.6236 7.82346 11.7515L7.84532 11.5259C7.86933 11.2781 7.88134 11.1542 7.84561 11.0393C7.80989 10.9244 7.73047 10.8316 7.57163 10.6458L7.42702 10.4767C6.86808 9.8231 6.5886 9.4963 6.68613 9.18266C6.78366 8.86902 7.19361 8.77627 8.0135 8.59076L8.22562 8.54276C8.45861 8.49005 8.5751 8.46369 8.66864 8.39268C8.76218 8.32168 8.82217 8.21406 8.94215 7.99883L9.05138 7.80287Z"
										stroke-width="1.25" />
								</svg>
							</div>
							<div>Free Bonus</div>
						</div>

						<!-- Public Reading -->
						<div class="flex items-center text-[#1C2158] h-[72px] cursor-pointer" :class="{ 'text-[#4484FF] border-b border-[#f2f2f5] border-solid': activeRoute === 'PublicReading' }" @click="routerClick('PublicReading')">
							<div class="w-6 h-6 mr-3 flex items-center justify-center">
								<svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5">
									<path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 006 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 016 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 016-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0018 18a8.967 8.967 0 00-6 2.292m0-14.25v14.25" />
								</svg>
							</div>
							<div>Public Reading</div>
						</div>

						<!-- Message -->
						<div class="flex items-center text-[#1C2158] h-[72px] cursor-pointer" :class="{ 'text-[#4484FF] border-b border-[#f2f2f5] border-solid': activeRoute === 'Message' }" @click="routerClick('Message')">
							<div class="w-6 h-6 mr-3 flex items-center justify-center">
								<svg width="20" height="20" viewBox="0 0 20 20" fill="none" stroke="currentColor" xmlns="http://www.w3.org/2000/svg">
									<mask id="path-1-inside-1_3442_14355" fill="white">
										<path d="M10 1.5C14.6944 1.5 18.5 5.30558 18.5 10C18.5 11.6512 18.0266 13.1908 17.2119 14.4951L18.1396 16.9121C18.3175 17.376 17.9753 17.874 17.4785 17.874H13.2021C12.2136 18.2764 11.1331 18.5 10 18.5C5.30558 18.5 1.5 14.6944 1.5 10C1.5 5.30558 5.30558 1.5 10 1.5Z" />
									</mask>
									<path
										d="M17.2119 14.4951L16.1517 13.8329L15.8217 14.3614L16.0449 14.9431L17.2119 14.4951ZM18.1396 16.9121L19.3068 16.4645L19.3066 16.4642L18.1396 16.9121ZM13.2021 17.874V16.624H12.9575L12.7308 16.7163L13.2021 17.874ZM10 1.5V2.75C14.0041 2.75 17.25 5.99594 17.25 10H18.5H19.75C19.75 4.61522 15.3848 0.25 10 0.25V1.5ZM18.5 10H17.25C17.25 11.4087 16.8469 12.72 16.1517 13.8329L17.2119 14.4951L18.2721 15.1573C19.2064 13.6616 19.75 11.8937 19.75 10H18.5ZM17.2119 14.4951L16.0449 14.9431L16.9727 17.36L18.1396 16.9121L19.3066 16.4642L18.3789 14.0472L17.2119 14.4951ZM18.1396 16.9121L16.9725 17.3597C16.8367 17.0055 17.0981 16.624 17.4785 16.624V17.874V19.124C18.8526 19.124 19.7984 17.7465 19.3068 16.4645L18.1396 16.9121ZM17.4785 17.874V16.624H13.2021V17.874V19.124H17.4785V17.874ZM13.2021 17.874L12.7308 16.7163C11.888 17.0594 10.9672 17.25 10 17.25V18.5V19.75C11.2989 19.75 12.5393 19.4935 13.6734 19.0318L13.2021 17.874ZM10 18.5V17.25C5.99594 17.25 2.75 14.0041 2.75 10H1.5H0.25C0.25 15.3848 4.61522 19.75 10 19.75V18.5ZM1.5 10H2.75C2.75 5.99594 5.99594 2.75 10 2.75V1.5V0.25C4.61522 0.25 0.25 4.61522 0.25 10H1.5Z"
										mask="url(#path-1-inside-1_3442_14355)" />
									<rect x="5.75" y="7.875" width="8.5" height="1.25" rx="0.625" />
									<rect x="5.75" y="11.4167" width="5.66667" height="1.25" rx="0.625" />
								</svg>
							</div>
							<div>Message</div>
						</div>

						<!-- Purchases -->
						<div class="flex items-center text-[#1C2158] h-[72px] cursor-pointer" :class="{ 'text-[#4484FF] border-b border-[#f2f2f5] border-solid': activeRoute === 'Purchases' }" @click="routerClick('Purchases')">
							<div class="w-6 h-6 mr-3 flex items-center justify-center">
								<svg width="20" height="20" viewBox="0 0 20 20" fill="none" stroke="currentColor" xmlns="http://www.w3.org/2000/svg">
									<path
										d="M10.5897 17.6461L11.1318 17.967L10.5897 17.6461ZM10.8653 17.1804L10.3232 16.8595L10.8653 17.1804ZM8.46668 17.1804L9.00883 16.8595L9.00882 16.8595L8.46668 17.1804ZM8.74233 17.6461L8.20018 17.967H8.20018L8.74233 17.6461ZM1.48953 12.993L2.07157 12.7519H2.07157L1.48953 12.993ZM5.7234 15.6006L5.74354 14.9709H5.74354L5.7234 15.6006ZM3.78961 15.2931L3.54852 15.8751H3.54852L3.78961 15.2931ZM17.8425 12.993L18.4245 13.234V13.234L17.8425 12.993ZM13.6086 15.6006L13.5885 14.9709L13.6086 15.6006ZM15.5424 15.2931L15.7835 15.8751L15.5424 15.2931ZM16.1366 1.79279L16.4658 1.25562V1.25562L16.1366 1.79279ZM17.5397 3.19589L18.0769 2.86672V2.86672L17.5397 3.19589ZM3.1954 1.79279L2.86622 1.25562V1.25562L3.1954 1.79279ZM1.7923 3.19589L1.25513 2.86672H1.25513L1.7923 3.19589ZM7.45216 15.8527L7.13142 16.395L7.45216 15.8527ZM10.5897 17.6461L11.1318 17.967L11.4075 17.5013L10.8653 17.1804L10.3232 16.8595L10.0475 17.3252L10.5897 17.6461ZM8.46668 17.1804L7.92453 17.5013L8.20018 17.967L8.74233 17.6461L9.28447 17.3252L9.00883 16.8595L8.46668 17.1804ZM10.5897 17.6461L10.0475 17.3252C9.88077 17.6069 9.45122 17.6069 9.28447 17.3252L8.74233 17.6461L8.20018 17.967C8.85481 19.073 10.4772 19.073 11.1318 17.967L10.5897 17.6461ZM8.39102 1.1665V1.7965H10.941V1.1665V0.536504H8.39102V1.1665ZM18.166 8.39153H17.536V9.24154H18.166H18.796V8.39153H18.166ZM1.16602 9.24154H1.79602V8.39153H1.16602H0.536016V9.24154H1.16602ZM1.16602 9.24154H0.536016C0.536016 10.223 0.535674 10.9907 0.577916 11.6098C0.620543 12.2346 0.708631 12.754 0.907483 13.234L1.48953 12.993L2.07157 12.7519C1.94691 12.4509 1.87324 12.0847 1.83499 11.5241C1.79636 10.9578 1.79602 10.2403 1.79602 9.24154H1.16602ZM5.7234 15.6006L5.74354 14.9709C4.8978 14.9438 4.41499 14.8702 4.0307 14.711L3.78961 15.2931L3.54852 15.8751C4.15346 16.1257 4.8276 16.2022 5.70326 16.2302L5.7234 15.6006ZM1.48953 12.993L0.907483 13.234C1.40277 14.4298 2.35278 15.3798 3.54852 15.8751L3.78961 15.2931L4.0307 14.711C3.1437 14.3436 2.43898 13.6389 2.07157 12.7519L1.48953 12.993ZM18.166 9.24154H17.536C17.536 10.2403 17.5357 10.9578 17.497 11.5241C17.4588 12.0847 17.3851 12.4509 17.2605 12.7519L17.8425 12.993L18.4245 13.234C18.6234 12.754 18.7115 12.2346 18.7541 11.6098C18.7964 10.9907 18.796 10.223 18.796 9.24154H18.166ZM13.6086 15.6006L13.6287 16.2302C14.5044 16.2022 15.1786 16.1257 15.7835 15.8751L15.5424 15.2931L15.3013 14.711C14.917 14.8702 14.4342 14.9438 13.5885 14.9709L13.6086 15.6006ZM17.8425 12.993L17.2605 12.7519C16.8931 13.6389 16.1883 14.3436 15.3013 14.711L15.5424 15.2931L15.7835 15.8751C16.9792 15.3798 17.9293 14.4298 18.4245 13.234L17.8425 12.993ZM10.941 1.1665V1.7965C12.3444 1.7965 13.3527 1.79717 14.1394 1.87196C14.9166 1.94586 15.4121 2.08766 15.8075 2.32995L16.1366 1.79279L16.4658 1.25562C15.8392 0.871627 15.1281 0.700288 14.2586 0.617618C13.3985 0.535839 12.32 0.536504 10.941 0.536504V1.1665ZM18.166 8.39153H18.796C18.796 7.01252 18.7967 5.93402 18.7149 5.07392C18.6322 4.20445 18.4609 3.49334 18.0769 2.86672L17.5397 3.19589L17.0026 3.52507C17.2449 3.92044 17.3867 4.41594 17.4606 5.19318C17.5354 5.9798 17.536 6.98812 17.536 8.39153H18.166ZM16.1366 1.79279L15.8075 2.32995C16.2945 2.62844 16.7041 3.03797 17.0026 3.52507L17.5397 3.19589L18.0769 2.86672C17.6745 2.21009 17.1224 1.65801 16.4658 1.25562L16.1366 1.79279ZM8.39102 1.1665V0.536504C7.01201 0.536504 5.93351 0.535839 5.07342 0.617618C4.20395 0.700288 3.49284 0.871627 2.86622 1.25562L3.1954 1.79279L3.52457 2.32995C3.91994 2.08766 4.41544 1.94586 5.19268 1.87196C5.97929 1.79717 6.98761 1.7965 8.39102 1.7965V1.1665ZM1.16602 8.39153H1.79602C1.79602 6.98812 1.79668 5.9798 1.87147 5.19318C1.94537 4.41594 2.08717 3.92044 2.32946 3.52507L1.7923 3.19589L1.25513 2.86672C0.871137 3.49334 0.699799 4.20445 0.617129 5.07392C0.535351 5.93402 0.536016 7.01252 0.536016 8.39153H1.16602ZM3.1954 1.79279L2.86622 1.25562C2.20959 1.65801 1.65751 2.21009 1.25513 2.86672L1.7923 3.19589L2.32946 3.52507C2.62795 3.03797 3.03748 2.62844 3.52457 2.32995L3.1954 1.79279ZM8.46668 17.1804L9.00882 16.8595C8.79389 16.4964 8.61125 16.1866 8.43544 15.9445C8.25214 15.6921 8.0492 15.4739 7.77291 15.3105L7.45216 15.8527L7.13142 16.395C7.20699 16.4397 7.29043 16.5121 7.41598 16.685C7.54901 16.8681 7.69769 17.1181 7.92453 17.5013L8.46668 17.1804ZM5.7234 15.6006L5.70326 16.2302C6.17355 16.2453 6.48482 16.2558 6.72378 16.285C6.95282 16.3129 7.05973 16.3526 7.13142 16.395L7.45216 15.8527L7.77291 15.3105C7.49274 15.1448 7.19423 15.073 6.87634 15.0342C6.56837 14.9967 6.19119 14.9852 5.74354 14.9709L5.7234 15.6006ZM10.8653 17.1804L11.4075 17.5013C11.6343 17.1181 11.783 16.8681 11.916 16.685C12.0416 16.5121 12.125 16.4397 12.2006 16.395L11.8798 15.8527L11.5591 15.3105C11.2828 15.4739 11.0799 15.6921 10.8966 15.9445C10.7207 16.1866 10.5381 16.4964 10.3232 16.8595L10.8653 17.1804ZM13.6086 15.6006L13.5885 14.9709C13.1408 14.9852 12.7636 14.9967 12.4557 15.0342C12.1378 15.073 11.8393 15.1448 11.5591 15.3105L11.8798 15.8527L12.2006 16.395C12.2723 16.3526 12.3792 16.3129 12.6082 16.285C12.8472 16.2558 13.1584 16.2453 13.6287 16.2302L13.6086 15.6006Z" />
									<path
										d="M9.72852 11.5291C9.96298 11.5291 10.1718 11.595 10.3447 11.7273L10.417 11.7878L10.4189 11.7898L10.4795 11.8572C10.6143 12.0195 10.6904 12.2201 10.6904 12.4568C10.6904 12.6927 10.6152 12.9028 10.4717 13.0681L10.4053 13.1365C10.2202 13.3091 9.9973 13.3953 9.72852 13.3953C9.49143 13.3953 9.29146 13.3192 9.12207 13.1853L9.05176 13.1248C8.86462 12.9501 8.77839 12.7254 8.77832 12.4568C8.77832 12.1881 8.86454 11.9626 9.05176 11.7878L9.12305 11.7273C9.29337 11.5952 9.49337 11.5291 9.72852 11.5291ZM9.9375 4.84155C10.7006 4.84162 11.3299 5.0408 11.8066 5.46948L11.9746 5.63354C12.3404 6.03341 12.5214 6.55144 12.5215 7.18237C12.5215 7.77227 12.368 8.26075 12.0811 8.64331L12.0781 8.64722C11.9709 8.7663 11.6315 9.08119 11.0762 9.56714L11.0752 9.56812C10.9235 9.69456 10.8039 9.83378 10.7109 9.98022L10.626 10.1296C10.5143 10.3531 10.459 10.5879 10.459 10.8572V11.0769H9.00977V10.8572C9.00977 10.4112 9.08006 10.0181 9.24805 9.69409C9.32746 9.52995 9.48291 9.32575 9.70898 9.08081C9.93592 8.83496 10.2368 8.54393 10.6123 8.20874L10.751 8.04858C10.9514 7.80305 11.0498 7.53778 11.0498 7.26343C11.0497 6.89392 10.9385 6.6078 10.7422 6.40015L10.6611 6.32788C10.4611 6.17036 10.1916 6.09351 9.85547 6.09351C9.35755 6.0936 9.02023 6.23957 8.81445 6.54272L8.81348 6.54468C8.62688 6.79713 8.53418 7.16538 8.53418 7.64624V7.70386H7.09766V7.64624C7.09766 6.77725 7.34412 6.08807 7.86816 5.58765L8.06836 5.41284C8.55467 5.03073 9.17678 4.84155 9.9375 4.84155Z"
										stroke="white"
										stroke-width="0.115909" />
								</svg>
							</div>
							<div>Purchases</div>
						</div>
					</div>
				</div>
			</div>
		</transition>
	</div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { restoreScroll } from '@/utils/scroll'
const route = useRoute()
const props = defineProps<{
	isMenuOpen: boolean
	activeRoute: string
}>()

const emit = defineEmits<{
	(e: 'close'): void
	(e: 'update:activeRoute', value: string): void
	(e: 'routerClickChild', value: string): void
}>()

const showSubmenu = ref(true) // Default to expanded
const childRoute = ref('')
const submenuItems = [
	{
		name: 'All',
		type: '0',
	},
	{
		name: 'New',
		type: '4',
	},
	{
		name: 'Reviews',
		type: '2',
	},
	{
		name: 'Readings',
		type: '1',
	},
	{
		name: 'Rated',
		type: '3',
	},
]

const toggleSubmenu = () => {
	showSubmenu.value = !showSubmenu.value
}
const routerClick = (routeName: string) => {
	emit('update:activeRoute', routeName)
	// 路由切换后自动关闭菜单并恢复滚动
	emit('close')
	// 确保滚动恢复
	restoreScroll()
}
const routerClickChild = (item: any) => {
	childRoute.value = item.name
	emit('routerClickChild', item.type)
	emit('close')
	restoreScroll()
}
const closeMenu = () => {
	emit('close')
}
onMounted(() => {
	childRoute.value = submenuItems.find((item) => item.type === route.params.type)?.name || ''
})
</script>

<style scoped>
.slide-menu-enter-from,
.slide-menu-leave-to {
	transform: translateX(-100%);
	opacity: 0;
}
.slide-menu-enter-active,
.slide-menu-leave-active {
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
.slide-menu-enter-to,
.slide-menu-leave-from {
	transform: translateX(0);
	opacity: 1;
}

.slide-child-menu-enter-to,
.slide-child-menu-leave-from {
	/* transform: translateY(0); */
	opacity: 1;
}
.slide-child-menu-enter-from,
.slide-child-menu-leave-to {
	/* transform: translateY(-100%); */
	opacity: 0;
}
.slide-child-menu-enter-active,
.slide-child-menu-leave-active {
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.active-submenu-item {
	color: #4484ff;
	font-weight: 500;
}
</style>
