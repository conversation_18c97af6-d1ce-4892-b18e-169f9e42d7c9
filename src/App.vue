<template>
    <el-config-provider :locale="currentLocale">
        <router-view v-slot="{ Component, route }" :showHeaderBg="showHeaderBg">
            <transition :name="transitionName" mode="out-in">
                <keep-alive :include="include" :max="10">
                    <component :is="Component" v-if="route.meta.keepAlive" :key="route.fullPath" />
                </keep-alive>
            </transition>
            <transition :name="transitionName" mode="out-in">
                <component :is="Component" v-if="!route.meta.keepAlive && isRouterAlive" :key="route.fullPath">
                </component>
            </transition>
        </router-view>
    </el-config-provider>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRoute } from 'vue-router'
const route = useRoute()
import { ElConfigProvider } from 'element-plus'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import en from 'element-plus/dist/locale/en.mjs'
import { useUserStore } from './stores/user'
const userStore = useUserStore()
import { getImConfig, imHeartbeat } from '@/api/common'

const include = ref([])
const transitionName = ref('slide-left')
const isRouterAlive = ref(true)
const showHeaderBg = ref(false)


watch(() => route.fullPath, (newVal, oldVal) => {
    console.log(newVal)
    if (newVal.includes('home')) {
        showHeaderBg.value = false
    } else {
        showHeaderBg.value = true
    }
}, { immediate: true, deep: true })

const { locale } = useI18n()
const currentLocale = computed(() => {
    return locale.value === 'zh' ? zhCn : en
})

const getFirstLevelRoute = function (route: { matched: string | any[] }) {
    if (!Array.isArray(route.matched) || route.matched.length === 0) {
        return route
    }
    return route.matched[0]
}

const getImConfigFunc = () => {
    getImConfig().then(response => {
        userStore.setUserSig(response.data)
    }).catch(error => {

    })
}

let imHeartbeatTimer: number | null = null
function startImHeartbeat() {
    if (imHeartbeatTimer) return
    imHeartbeat()
    imHeartbeatTimer = window.setInterval(() => {
        imHeartbeat()
    }, 60000)
}
function stopImHeartbeat() {
    if (imHeartbeatTimer) {
        clearInterval(imHeartbeatTimer)
        imHeartbeatTimer = null
    }
}
watch(
    () => userStore.isLogin,
    (isLogin) => {
        if (isLogin) {
            startImHeartbeat()
        } else {
            stopImHeartbeat()
        }
    },
    { immediate: true }
)
onUnmounted(() => {
    stopImHeartbeat()
})

onMounted(() => {
    getImConfigFunc()
})

</script>

<style lang="scss" scoped>
.slide-left-enter-from {
    transform: translateX(20px);
    opacity: 0.5;
}

.slide-left-enter-to {
    transform: translateX(0px);
}

.slide-left-leave-from {
    transform: translateX(0);
}

.slide-left-leave-to {
    transform: translateX(20px);
    opacity: 0.5;
}

.slide-left-enter-active,
.slide-left-leave-active {
    transition: all 0.3s;
}
</style>
