import express from 'express';
import { createProxyMiddleware } from 'http-proxy-middleware';
import cors from 'cors';

const app = express();
const PORT = 8080;

// 启用 CORS
app.use(cors({
  origin: ['http://localhost:5174', 'http://127.0.0.1:5174'],
  credentials: true
}));

// 代理配置
const proxyOptions = {
  target: 'https://test.api.allstarmet.com',
  changeOrigin: true,
  secure: true,
  pathRewrite: {
    '^/api': '', // 移除 /api 前缀
  },
  onProxyReq: (proxyReq, req, res) => {
    console.log(`[${new Date().toISOString()}] ${req.method} ${req.path} -> ${proxyReq.path}`);
  },
  onProxyRes: (proxyRes, req, res) => {
    console.log(`[${new Date().toISOString()}] ${req.method} ${req.path} <- ${proxyRes.statusCode}`);
  },
  onError: (err, req, res) => {
    console.error(`[${new Date().toISOString()}] Proxy Error:`, err.message);
    res.status(500).json({ error: '代理服务器错误' });
  }
};

// 设置代理中间件
app.use('/api', createProxyMiddleware(proxyOptions));

// 健康检查端点
app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`🚀 代理服务器启动成功！`);
  console.log(`📍 本地地址: http://localhost:${PORT}`);
  console.log(`🔗 代理规则: /api/* -> https://test.api.allstarmet.com/*`);
  console.log(`⏰ 启动时间: ${new Date().toISOString()}`);
});

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n🛑 正在关闭代理服务器...');
  process.exit(0);
}); 