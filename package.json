{"name": "vite-project", "private": true, "version": "0.0.0", "type": "module", "scripts": {"start": "start-proxy.sh", "dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@tencentcloud/chat": "^3.5.5", "@types/axios": "^0.14.4", "@vueuse/core": "^13.1.0", "axios": "^1.9.0", "cors": "^2.8.5", "element-plus": "^2.9.8", "eventemitter3": "^5.0.1", "express": "^5.1.0", "firebase": "^11.9.0", "http-proxy-middleware": "^3.0.5", "pinia": "^3.0.2", "tim-upload-plugin": "^1.4.2", "vue": "^3.5.13", "vue-i18n": "^11.1.3", "vue-router": "^4.5.0"}, "devDependencies": {"@types/node": "^22.13.15", "@vitejs/plugin-legacy": "^6.1.0", "@vitejs/plugin-vue": "^5.2.1", "@vue/tsconfig": "^0.7.0", "autoprefixer": "10.4.17", "postcss": "8.4.31", "postcss-pxtorem": "^6.1.0", "sass": "^1.86.3", "tailwindcss": "3.4.1", "typescript": "~5.7.2", "vite": "^6.2.0", "vue-tsc": "^2.2.4"}}