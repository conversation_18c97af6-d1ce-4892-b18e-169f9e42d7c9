# 项目核心流程说明

## 1. 用户登录

- 支持两种方式：
  - Google 登录
  - 手机号登录（含验证码）

---

## 2. 订单类型

- **Text Reading**（文字解读）
- **Live Text Chat**（实时文字聊天）

---

## 3. 下单与会话流程

### 通用下单流程

1. 用户登录后，选择占卜师（通过首页推荐、搜索、列表等入口）。
2. 选择服务类型（Text Reading 或 Live Text Chat）。
3. 填写订单信息并提交，订单创建成功。

---

### Live Text Chat 订单处理流程

1. **下单成功**  
   用户提交订单，系统生成订单号。

2. **邀请占卜师**  
   前端通过 IM（腾讯云）向占卜师发送通话邀请（C2C信令/自定义消息）。

3. **占卜师响应**  
   - 占卜师端收到邀请，可选择"接受"或"拒绝"。
   - 若长时间未响应，订单自动超时。

4. **后台创建群组**  
   占卜师"接受"后，后端自动创建群组，并将用户和占卜师都拉入该群组。

5. **进入通话**  
   - 用户端、占卜师端都通过事件监听（会话列表/群组变更）感知已被拉入群组。
   - 前端自动切换到聊天界面，双方可实时文字交流。

---

### Text Reading 订单处理流程

1. 用户提交问题，订单创建成功。
2. 占卜师在后台规定时间内完成文字解读并回复。
3. 用户在订单详情页查看解读内容。
4. 订单完成。

---

## 4. 订单状态

- Pending（待处理）
- Awaiting Receipt（待接单/等待占卜师响应）
- Success/Completed（已完成）
- Declined（被拒绝）
- Expired（超时未处理）
- Cancel（已取消）

---

## 5. 流程图

```mermaid
graph TD
  A[用户登录<br/>Google/手机号] --> B[选择占卜师]
  B --> C[选择服务类型]
  C --> D[填写并提交订单]
  D --> E{订单类型}
  E -- Text Reading --> F1[等待占卜师解读]
  F1 --> G1[用户查看回复]
  G1 --> H1[订单完成]
  E -- Live Text Chat --> F2[发送IM邀请占卜师]
  F2 --> G2{占卜师响应}
  G2 -- 接受 --> H2[后端创建群组并拉人]
  H2 --> I2[前端监听到入群事件]
  I2 --> J2[进入实时聊天]
  G2 -- 拒绝/超时 --> K2[订单变为Declined/Expired]
```

---

## 6. 关键事件监听

- **Live Text Chat**：前端通过监听 IM 会话/群组变更事件，自动感知被拉入群组，进入聊天。
- **Text Reading**：用户主动刷新或进入订单详情页查看解读内容。 